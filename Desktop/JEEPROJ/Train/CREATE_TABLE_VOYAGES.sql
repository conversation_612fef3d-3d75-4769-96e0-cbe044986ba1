-- Script de création de la table voyages avec données de test
USE train;

-- Créer la table voyages si elle n'existe pas
CREATE TABLE IF NOT EXISTS voyages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    trajet_id BIGINT NOT NULL COMMENT 'ID du trajet',
    date_voyage DATE NOT NULL COMMENT 'Date du voyage',
    places_disponibles INT NOT NULL COMMENT 'Places disponibles',
    places_reservees INT DEFAULT 0 COMMENT 'Places réservées',
    statut ENUM('PROGRAMME', 'EN_COURS', 'TERMINE', 'ANNULE', 'RETARDE') DEFAULT 'PROGRAMME' COMMENT 'Statut du voyage',
    retard_minutes INT DEFAULT 0 COMMENT 'Retard en minutes',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (trajet_id) REFERENCES trajets(id) ON DELETE CASCADE,
    
    -- Index pour améliorer les performances
    INDEX idx_trajet_date (trajet_id, date_voyage),
    INDEX idx_date_voyage (date_voyage),
    INDEX idx_statut (statut),
    
    -- Contraintes
    CONSTRAINT chk_places_disponibles CHECK (places_disponibles >= 0),
    CONSTRAINT chk_places_reservees CHECK (places_reservees >= 0),
    CONSTRAINT chk_places_coherentes CHECK (places_reservees <= (places_disponibles + places_reservees)),
    CONSTRAINT chk_retard CHECK (retard_minutes >= 0),
    
    -- Contrainte d'unicité : un seul voyage par trajet et par date
    UNIQUE KEY uk_trajet_date (trajet_id, date_voyage)
);

-- Vider la table si elle existe déjà (pour les tests)
DELETE FROM voyages;

-- Insérer des données de test (seulement si des trajets existent)
INSERT INTO voyages (trajet_id, date_voyage, places_disponibles, places_reservees, statut, retard_minutes) 
SELECT 
    t.id,
    CASE 
        WHEN t.id % 7 = 1 THEN CURDATE() + INTERVAL 1 DAY
        WHEN t.id % 7 = 2 THEN CURDATE() + INTERVAL 2 DAY
        WHEN t.id % 7 = 3 THEN CURDATE() + INTERVAL 3 DAY
        WHEN t.id % 7 = 4 THEN CURDATE() + INTERVAL 7 DAY
        WHEN t.id % 7 = 5 THEN CURDATE() + INTERVAL 14 DAY
        WHEN t.id % 7 = 6 THEN CURDATE() + INTERVAL 21 DAY
        ELSE CURDATE() + INTERVAL 30 DAY
    END as date_voyage,
    t.nombre_places as places_disponibles,
    CASE 
        WHEN t.id % 4 = 1 THEN FLOOR(t.nombre_places * 0.1)
        WHEN t.id % 4 = 2 THEN FLOOR(t.nombre_places * 0.3)
        WHEN t.id % 4 = 3 THEN FLOOR(t.nombre_places * 0.6)
        ELSE 0
    END as places_reservees,
    CASE 
        WHEN t.id % 5 = 1 THEN 'PROGRAMME'
        WHEN t.id % 5 = 2 THEN 'PROGRAMME'
        WHEN t.id % 5 = 3 THEN 'PROGRAMME'
        WHEN t.id % 5 = 4 THEN 'RETARDE'
        ELSE 'PROGRAMME'
    END as statut,
    CASE 
        WHEN t.id % 5 = 4 THEN 15
        ELSE 0
    END as retard_minutes
FROM trajets t 
WHERE t.actif = TRUE
LIMIT 20;

-- Ajouter quelques voyages pour demain et après-demain pour tous les trajets actifs
INSERT INTO voyages (trajet_id, date_voyage, places_disponibles, places_reservees, statut) 
SELECT 
    t.id,
    CURDATE() + INTERVAL 1 DAY,
    t.nombre_places,
    FLOOR(RAND() * t.nombre_places * 0.3),
    'PROGRAMME'
FROM trajets t 
WHERE t.actif = TRUE
AND NOT EXISTS (
    SELECT 1 FROM voyages v 
    WHERE v.trajet_id = t.id 
    AND v.date_voyage = CURDATE() + INTERVAL 1 DAY
);

INSERT INTO voyages (trajet_id, date_voyage, places_disponibles, places_reservees, statut) 
SELECT 
    t.id,
    CURDATE() + INTERVAL 2 DAY,
    t.nombre_places,
    FLOOR(RAND() * t.nombre_places * 0.2),
    'PROGRAMME'
FROM trajets t 
WHERE t.actif = TRUE
AND NOT EXISTS (
    SELECT 1 FROM voyages v 
    WHERE v.trajet_id = t.id 
    AND v.date_voyage = CURDATE() + INTERVAL 2 DAY
);

-- Vérifier les données créées
SELECT 
    v.id,
    CONCAT(gd.ville, ' → ', ga.ville) as trajet,
    v.date_voyage,
    v.places_disponibles,
    v.places_reservees,
    ROUND((v.places_reservees / (v.places_disponibles + v.places_reservees)) * 100, 1) as taux_occupation,
    v.statut,
    CASE WHEN v.retard_minutes > 0 THEN CONCAT(v.retard_minutes, ' min') ELSE 'À l\'heure' END as retard
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
ORDER BY v.date_voyage, gd.ville;

-- Statistiques
SELECT 
    COUNT(*) as total_voyages,
    COUNT(CASE WHEN statut = 'PROGRAMME' THEN 1 END) as programmes,
    COUNT(CASE WHEN statut = 'RETARDE' THEN 1 END) as retardes,
    COUNT(CASE WHEN date_voyage >= CURDATE() THEN 1 END) as futurs,
    ROUND(AVG(places_reservees / (places_disponibles + places_reservees) * 100), 1) as taux_occupation_moyen
FROM voyages;

COMMIT;
