@echo off
echo ========================================
echo    TRAINSYSTEM - APPLICATION COMPLETE
echo ========================================
echo.

cd /d "%~dp0"

echo 1. Arrêt des processus existants...
taskkill /F /IM python.exe 2>nul
taskkill /F /IM java.exe 2>nul
timeout /t 2 /nobreak >nul

echo 2. Compilation sans nettoyage...
call mvn compile -q
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur de compilation
    pause
    exit /b 1
)
echo ✅ Compilation réussie

echo 3. Packaging...
call mvn package -DskipTests -q
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur de packaging
    pause
    exit /b 1
)
echo ✅ WAR généré

echo 4. Démarrage de l'application...
echo.
echo 🚀 Lancement de TrainSystem...
echo.
echo 📍 URL de l'application : http://localhost:8080/Train
echo 🔑 Compte administrateur : <EMAIL> / password
echo.
echo ⚠️ IMPORTANT : Pour arrêter l'application, fermez la fenêtre du serveur
echo.

REM Démarrer le serveur Maven Tomcat dans une nouvelle fenêtre
start "TrainSystem Server - NE PAS FERMER" cmd /k "echo ========================================= && echo    SERVEUR TRAINSYSTEM EN COURS && echo ========================================= && echo. && echo Application disponible sur : http://localhost:8080/Train && echo. && echo Pour arreter le serveur : Ctrl+C ou fermer cette fenetre && echo. && mvn tomcat7:run -Dmaven.tomcat.port=8080 -Dmaven.tomcat.path=/Train"

echo ⏳ Attente du démarrage du serveur (25 secondes)...
timeout /t 25 /nobreak >nul

echo 🌐 Ouverture de l'application dans le navigateur...
start http://localhost:8080/Train

echo.
echo ========================================
echo    APPLICATION TRAINSYSTEM LANCEE !
echo ========================================
echo.
echo ✅ L'application est maintenant accessible à l'adresse :
echo    👉 http://localhost:8080/Train
echo.
echo 🎯 Fonctionnalités disponibles :
echo    ✅ Page d'accueil avec formulaire de recherche
echo    ✅ Système d'inscription utilisateur
echo    ✅ Connexion sécurisée avec sessions
echo    ✅ Recherche de voyages par ville et date
echo    ✅ Réservation de billets avec calcul automatique
echo    ✅ Gestion des réservations utilisateur
echo    ✅ Interface responsive et moderne
echo.
echo 👤 Comptes de test disponibles :
echo    📧 Administrateur : <EMAIL>
echo    🔑 Mot de passe   : password
echo.
echo 🗄️ Données de démonstration :
echo    🏢 8 gares principales (Paris, Lyon, Marseille, Bordeaux...)
echo    🚄 8 trajets bidirectionnels avec horaires réalistes
echo    📅 Voyages programmés pour les 7 prochains jours
echo    💰 Prix de 89,50€ à 125,00€ selon la destination
echo.
echo 🔧 Mode de fonctionnement :
echo    • Application en mode démonstration
echo    • Données stockées en mémoire
echo    • Pas besoin de base de données MySQL
echo    • Toutes les fonctionnalités opérationnelles
echo.
echo 🛑 Pour arrêter l'application :
echo    • Fermez la fenêtre "TrainSystem Server"
echo    • Ou utilisez Ctrl+C dans cette fenêtre
echo.
echo Profitez de votre application TrainSystem ! 🚂
echo.

pause
