@echo off
echo ========================================
echo    NETTOYAGE ET LANCEMENT TRAINSYSTEM
echo ========================================
echo.

cd /d "%~dp0"

echo 1. Nettoyage des processus...
taskkill /F /IM python.exe 2>nul
taskkill /F /IM java.exe 2>nul
timeout /t 3 /nobreak >nul

echo 2. Suppression des fichiers temporaires...
if exist "target\webapp" (
    echo Suppression de target\webapp...
    rmdir /s /q "target\webapp" 2>nul
)

echo 3. Compilation propre...
call mvn clean compile -q
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur de compilation
    pause
    exit /b 1
)

echo 4. Génération du WAR...
call mvn package -DskipTests -q
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur de packaging
    pause
    exit /b 1
)

echo 5. Démarrage avec Maven Tomcat...
echo.
echo 🚀 Démarrage de l'application TrainSystem...
echo 📍 URL: http://localhost:8080/Train
echo 🔑 Compte: <EMAIL> / password
echo.
echo ⚠️ Pour arrêter: Ctrl+C dans cette fenêtre
echo.

start "TrainSystem Application" cmd /k "mvn tomcat7:run -Dmaven.tomcat.port=8080 -Dmaven.tomcat.path=/Train"

echo ⏳ Attente du démarrage (20 secondes)...
timeout /t 20 /nobreak >nul

echo 🌐 Ouverture de l'application dans le navigateur...
start http://localhost:8080/Train

echo.
echo ========================================
echo    APPLICATION LANCEE !
echo ========================================
echo.
echo ✅ TrainSystem est maintenant accessible
echo 📱 Fonctionnalités disponibles :
echo    • Page d'accueil moderne
echo    • Inscription utilisateur
echo    • Connexion sécurisée
echo    • Recherche de voyages
echo    • Réservation de billets
echo    • Gestion des réservations
echo.
echo 🧪 Données de test :
echo    • 8 gares (Paris, Lyon, Marseille...)
echo    • Trajets avec horaires réalistes
echo    • Voyages pour 7 jours
echo.
echo Pour utiliser l'application, allez sur :
echo http://localhost:8080/Train
echo.

pause
