# 🚀 Guide - Voyages Automatiques depuis votre Base de Données

## ✅ **Votre système est déjà configuré !**

Votre application utilise **automatiquement** les voyages de votre base de données. Le code Java récupère les vrais voyages via `VoyageDAO` et `VoyageService`.

## 🔍 **Étape 1 : Vérifier vos données existantes**

### **Exécutez ce script dans phpMyAdmin :**
```sql
USE train;

-- Vérifier les voyages existants
SELECT 
    v.id,
    CONCAT(gd.ville, ' → ', ga.ville) as trajet,
    v.date_voyage,
    v.places_disponibles,
    v.places_reservees,
    v.statut
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
ORDER BY v.date_voyage DESC;
```

### **Résultats possibles :**
- ✅ **Si vous voyez des voyages** → Votre liste s'affichera automatiquement
- ❌ **Si la table est vide** → Suivez l'étape 2

## 🎯 **Étape 2 : Créer des voyages automatiques**

### **Option A : Script automatique complet**
1. **Ouvrez phpMyAdmin** : `http://localhost/phpmyadmin`
2. **Sélectionnez la base** `train`
3. **Exécutez le script** `CREER_VOYAGES_AUTOMATIQUES.sql`

Ce script va :
- ✅ Créer des voyages pour **14 jours** sur tous vos trajets
- ✅ Simuler des **réservations réalistes**
- ✅ Ajouter différents **statuts** (PROGRAMME, EN_COURS, RETARDE, ANNULE)
- ✅ Générer des **retards aléatoires**

### **Option B : Script simple et rapide**
```sql
-- Créer des voyages pour les 7 prochains jours
INSERT INTO voyages (trajet_id, date_voyage, places_disponibles, places_reservees, statut)
SELECT 
    t.id,
    CURDATE() + INTERVAL day_offset DAY,
    t.nombre_places,
    FLOOR(RAND() * t.nombre_places * 0.3), -- 0-30% de réservations
    'PROGRAMME'
FROM trajets t
CROSS JOIN (SELECT 1 as day_offset UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7) days
WHERE t.actif = TRUE;
```

## 🧪 **Étape 3 : Tester l'affichage automatique**

1. **Accédez à** : `http://localhost:8080/Train/admin/voyages`
2. **Résultat attendu** : Liste des voyages de votre base de données
3. **Vérifiez** : Les données correspondent à votre base

## 📊 **Fonctionnalités automatiques disponibles**

### ✅ **Récupération automatique**
- **Tous les voyages** de votre table `voyages`
- **Jointures automatiques** avec `trajets` et `gares`
- **Calculs automatiques** : taux d'occupation, places disponibles
- **Tri automatique** par date et trajet

### ✅ **Affichage intelligent**
- **Statuts colorés** selon l'état du voyage
- **Informations complètes** : trajet, horaires, prix, type de train
- **Actions contextuelles** selon le statut
- **Gestion des cas vides** avec message informatif

### ✅ **Actions fonctionnelles**
- **Créer** : Nouveau voyage sur un trajet existant
- **Modifier** : Édition des voyages existants
- **Annuler** : Change le statut à "ANNULE"
- **Supprimer** : Suppression définitive (avec vérifications)

## 🔄 **Synchronisation en temps réel**

### **Votre application récupère automatiquement :**
- ✅ **Nouveaux voyages** ajoutés en base
- ✅ **Modifications** des voyages existants
- ✅ **Suppressions** de voyages
- ✅ **Changements de statut**

### **Pas besoin de redémarrer** l'application !
Chaque fois que vous accédez à la page voyages, les données sont récupérées fraîchement depuis la base.

## 📋 **Structure de votre table voyages**

```sql
voyages:
├── id (BIGINT, AUTO_INCREMENT)
├── trajet_id (BIGINT, FK vers trajets)
├── date_voyage (DATE)
├── places_disponibles (INT)
├── places_reservees (INT)
├── statut (ENUM: PROGRAMME, EN_COURS, TERMINE, ANNULE, RETARDE)
├── retard_minutes (INT)
├── date_creation (TIMESTAMP)
└── date_modification (TIMESTAMP)
```

## 🎨 **Personnalisation des données**

### **Ajouter manuellement un voyage :**
```sql
INSERT INTO voyages (trajet_id, date_voyage, places_disponibles, places_reservees, statut)
VALUES (1, '2024-06-01', 200, 50, 'PROGRAMME');
```

### **Modifier un voyage existant :**
```sql
UPDATE voyages 
SET statut = 'RETARDE', retard_minutes = 15 
WHERE id = 1;
```

### **Simuler des réservations :**
```sql
UPDATE voyages 
SET places_reservees = places_reservees + 10,
    places_disponibles = places_disponibles - 10
WHERE id = 1 AND places_disponibles >= 10;
```

## 🚀 **Résultat final**

Après avoir exécuté les scripts :

1. **Page voyages** affiche automatiquement vos données
2. **Toutes les actions CRUD** fonctionnent avec votre base
3. **Synchronisation** en temps réel
4. **Interface moderne** avec vos vraies données

## 📝 **Commandes de vérification**

### **Compter vos voyages :**
```sql
SELECT COUNT(*) as total_voyages FROM voyages;
```

### **Voyages par statut :**
```sql
SELECT statut, COUNT(*) as nombre 
FROM voyages 
GROUP BY statut;
```

### **Prochains voyages :**
```sql
SELECT v.*, CONCAT(gd.ville, ' → ', ga.ville) as trajet
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
WHERE v.date_voyage >= CURDATE()
ORDER BY v.date_voyage
LIMIT 10;
```

---

## 🎉 **Votre système est prêt !**

**Votre application récupère automatiquement les voyages de votre base de données.**

**Étapes suivantes :**
1. ✅ Exécutez un des scripts SQL pour créer des données
2. ✅ Accédez à `http://localhost:8080/Train/admin/voyages`
3. ✅ Profitez de votre système de gestion des voyages !

**Toutes les données que vous voyez proviennent directement de votre base MySQL ! 🚀**
