-- Script d'initialisation de la base de données Train
-- Créer la base de données si elle n'existe pas
CREATE DATABASE IF NOT EXISTS train CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE train;

-- Table des utilisateurs
CREATE TABLE IF NOT EXISTS utilisateurs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    mot_de_passe VARCHAR(255) NOT NULL,
    telephone VARCHAR(20),
    type_utilisateur ENUM('CLIENT', 'ADMINISTRATEUR', 'EMPLOYE') NOT NULL DEFAULT 'CLIENT',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    derniere_connexion TIMESTAMP NULL,
    actif BOOLEAN DEFAULT TRUE,
    INDEX idx_email (email),
    INDEX idx_type (type_utilisateur)
);

-- Table des gares
CREATE TABLE IF NOT EXISTS gares (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(200) NOT NULL,
    ville VARCHAR(100) NOT NULL,
    code_gare VARCHAR(10) UNIQUE NOT NULL,
    adresse TEXT,
    code_postal VARCHAR(10),
    active BOOLEAN DEFAULT TRUE,
    INDEX idx_ville (ville),
    INDEX idx_code (code_gare)
);

-- Table des trajets
CREATE TABLE IF NOT EXISTS trajets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    gare_depart_id BIGINT NOT NULL,
    gare_arrivee_id BIGINT NOT NULL,
    heure_depart TIME NOT NULL,
    heure_arrivee TIME NOT NULL,
    prix DECIMAL(10,2) NOT NULL,
    nombre_places INT NOT NULL DEFAULT 100,
    actif BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (gare_depart_id) REFERENCES gares(id) ON DELETE CASCADE,
    FOREIGN KEY (gare_arrivee_id) REFERENCES gares(id) ON DELETE CASCADE,
    INDEX idx_gares (gare_depart_id, gare_arrivee_id),
    INDEX idx_horaires (heure_depart, heure_arrivee)
);

-- Table des voyages (instances de trajets à des dates spécifiques)
CREATE TABLE IF NOT EXISTS voyages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    trajet_id BIGINT NOT NULL,
    date_voyage DATE NOT NULL,
    places_disponibles INT NOT NULL,
    places_reservees INT DEFAULT 0,
    statut ENUM('PROGRAMME', 'EN_COURS', 'TERMINE', 'ANNULE', 'RETARDE') DEFAULT 'PROGRAMME',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (trajet_id) REFERENCES trajets(id) ON DELETE CASCADE,
    UNIQUE KEY unique_voyage (trajet_id, date_voyage),
    INDEX idx_date_voyage (date_voyage),
    INDEX idx_statut (statut)
);

-- Table des réservations
CREATE TABLE IF NOT EXISTS reservations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    numero_reservation VARCHAR(50) UNIQUE NOT NULL,
    utilisateur_id BIGINT NOT NULL,
    voyage_id BIGINT NOT NULL,
    nombre_places INT NOT NULL DEFAULT 1,
    prix_total DECIMAL(10,2) NOT NULL,
    statut ENUM('EN_ATTENTE', 'CONFIRMEE', 'ANNULEE', 'REMBOURSEE') DEFAULT 'EN_ATTENTE',
    date_reservation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_annulation TIMESTAMP NULL,
    motif_annulation TEXT,
    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id) ON DELETE CASCADE,
    FOREIGN KEY (voyage_id) REFERENCES voyages(id) ON DELETE CASCADE,
    INDEX idx_utilisateur (utilisateur_id),
    INDEX idx_voyage (voyage_id),
    INDEX idx_numero (numero_reservation),
    INDEX idx_statut_reservation (statut)
);

-- Table des promotions (optionnel)
CREATE TABLE IF NOT EXISTS promotions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(200) NOT NULL,
    description TEXT,
    pourcentage_reduction DECIMAL(5,2) NOT NULL,
    date_debut DATE NOT NULL,
    date_fin DATE NOT NULL,
    code_promo VARCHAR(50) UNIQUE,
    actif BOOLEAN DEFAULT TRUE,
    INDEX idx_dates (date_debut, date_fin),
    INDEX idx_code (code_promo)
);

-- Table des paiements (optionnel)
CREATE TABLE IF NOT EXISTS paiements (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    reservation_id BIGINT NOT NULL,
    montant DECIMAL(10,2) NOT NULL,
    methode_paiement ENUM('CARTE_CREDIT', 'PAYPAL', 'VIREMENT', 'ESPECES') NOT NULL,
    statut_paiement ENUM('EN_ATTENTE', 'VALIDE', 'REFUSE', 'REMBOURSE') DEFAULT 'EN_ATTENTE',
    date_paiement TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reference_transaction VARCHAR(100),
    FOREIGN KEY (reservation_id) REFERENCES reservations(id) ON DELETE CASCADE,
    INDEX idx_reservation (reservation_id),
    INDEX idx_statut_paiement (statut_paiement)
);

-- Insertion de données de test

-- Insertion des gares
INSERT INTO gares (nom, ville, code_gare, adresse, code_postal) VALUES
('Gare Centrale', 'Paris', 'PAR01', '1 Place de la Gare, Paris', '75001'),
('Gare du Nord', 'Lyon', 'LYO01', '2 Avenue de la République, Lyon', '69001'),
('Gare Saint-Jean', 'Bordeaux', 'BOR01', '3 Cours de la Marne, Bordeaux', '33000'),
('Gare Centrale', 'Marseille', 'MAR01', '4 Boulevard National, Marseille', '13001'),
('Gare SNCF', 'Toulouse', 'TOU01', '5 Boulevard Pierre Semard, Toulouse', '31000'),
('Gare de l\'Est', 'Strasbourg', 'STR01', '6 Place de la Gare, Strasbourg', '67000'),
('Gare Centrale', 'Lille', 'LIL01', '7 Place des Buisses, Lille', '59000'),
('Gare Saint-Charles', 'Nice', 'NIC01', '8 Avenue Thiers, Nice', '06000');

-- Insertion d'un administrateur par défaut
INSERT INTO utilisateurs (nom, prenom, email, mot_de_passe, type_utilisateur) VALUES
('Admin', 'Système', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'ADMINISTRATEUR');
-- Mot de passe: password (hashé avec BCrypt)

-- Insertion de quelques trajets de test
INSERT INTO trajets (gare_depart_id, gare_arrivee_id, heure_depart, heure_arrivee, prix, nombre_places) VALUES
(1, 2, '08:00:00', '12:30:00', 89.50, 200),  -- Paris -> Lyon
(2, 1, '14:00:00', '18:30:00', 89.50, 200),  -- Lyon -> Paris
(1, 3, '09:15:00', '15:45:00', 125.00, 150), -- Paris -> Bordeaux
(3, 1, '16:30:00', '23:00:00', 125.00, 150), -- Bordeaux -> Paris
(2, 4, '07:30:00', '13:15:00', 95.00, 180),  -- Lyon -> Marseille
(4, 2, '15:45:00', '21:30:00', 95.00, 180),  -- Marseille -> Lyon
(1, 5, '10:00:00', '17:20:00', 110.00, 160), -- Paris -> Toulouse
(5, 1, '18:00:00', '01:20:00', 110.00, 160); -- Toulouse -> Paris

-- Insertion de quelques voyages pour les prochains jours
INSERT INTO voyages (trajet_id, date_voyage, places_disponibles) VALUES
(1, CURDATE(), 200),
(2, CURDATE(), 200),
(1, DATE_ADD(CURDATE(), INTERVAL 1 DAY), 200),
(2, DATE_ADD(CURDATE(), INTERVAL 1 DAY), 200),
(3, CURDATE(), 150),
(4, CURDATE(), 150),
(5, DATE_ADD(CURDATE(), INTERVAL 2 DAY), 180),
(6, DATE_ADD(CURDATE(), INTERVAL 2 DAY), 180);

COMMIT;
