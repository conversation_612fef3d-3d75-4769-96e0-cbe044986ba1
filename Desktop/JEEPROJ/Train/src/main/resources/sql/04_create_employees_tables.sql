-- Script de création des tables pour la gestion des employés
-- À exécuter dans phpMyAdmin ou MySQL Workbench

USE train;

-- Table des affectations d'employés aux voyages
CREATE TABLE IF NOT EXISTS affectations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    voyage_id BIGINT NOT NULL,
    employe_id BIGINT NOT NULL,
    role_voyage ENUM('COND<PERSON><PERSON>UR', 'CONTROLEUR', 'CHEF_DE_BORD', 'AGENT_COMMERCIAL', 'TECHNICIEN') NOT NULL,
    date_affectation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_modification DATETIME NULL,
    actif BOOLEAN NOT NULL DEFAULT TRUE,
    commentaire TEXT NULL,
    
    -- Clés étrangères
    FOREIGN KEY (voyage_id) REFERENCES voyages(id) ON DELETE CASCADE,
    FOREIGN KEY (employe_id) REFERENCES utilisateurs(id) ON DELETE CASCADE,
    
    -- Index pour les performances
    INDEX idx_voyage_id (voyage_id),
    INDEX idx_employe_id (employe_id),
    INDEX idx_role_voyage (role_voyage),
    INDEX idx_actif (actif),
    
    -- Contrainte unique pour éviter les doublons
    UNIQUE KEY unique_affectation (voyage_id, employe_id, role_voyage)
);

-- Table des paiements
CREATE TABLE IF NOT EXISTS paiements (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    reservation_id BIGINT NOT NULL,
    montant DECIMAL(10,2) NOT NULL,
    statut_paiement ENUM('EN_ATTENTE', 'CONFIRME', 'ECHEC', 'ANNULE', 'REMBOURSE') NOT NULL DEFAULT 'EN_ATTENTE',
    type_paiement ENUM('PAIEMENT', 'REMBOURSEMENT', 'FRAIS') NOT NULL DEFAULT 'PAIEMENT',
    methode_paiement VARCHAR(50) NOT NULL, -- CARTE, PAYPAL, VIREMENT, etc.
    transaction_id VARCHAR(100) NULL,
    date_paiement DATETIME NULL,
    date_creation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_modification DATETIME NULL,
    commentaire TEXT NULL,
    
    -- Clés étrangères
    FOREIGN KEY (reservation_id) REFERENCES reservations(id) ON DELETE CASCADE,
    
    -- Index pour les performances
    INDEX idx_reservation_id (reservation_id),
    INDEX idx_statut_paiement (statut_paiement),
    INDEX idx_type_paiement (type_paiement),
    INDEX idx_date_paiement (date_paiement),
    INDEX idx_transaction_id (transaction_id)
);

-- Table des demandes d'annulation
CREATE TABLE IF NOT EXISTS demandes_annulation (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    reservation_id BIGINT NOT NULL,
    utilisateur_id BIGINT NOT NULL,
    motif TEXT NOT NULL,
    statut_annulation ENUM('EN_ATTENTE', 'APPROUVEE', 'REJETEE', 'TRAITEE') NOT NULL DEFAULT 'EN_ATTENTE',
    montant_paye DECIMAL(10,2) NOT NULL,
    frais_annulation DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    montant_remboursement DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    date_demande DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_traitement DATETIME NULL,
    commentaire_admin TEXT NULL,
    admin_id BIGINT NULL,
    
    -- Clés étrangères
    FOREIGN KEY (reservation_id) REFERENCES reservations(id) ON DELETE CASCADE,
    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES utilisateurs(id) ON DELETE SET NULL,
    
    -- Index pour les performances
    INDEX idx_reservation_id (reservation_id),
    INDEX idx_utilisateur_id (utilisateur_id),
    INDEX idx_statut_annulation (statut_annulation),
    INDEX idx_date_demande (date_demande),
    INDEX idx_admin_id (admin_id)
);

-- Insertion de données de démonstration pour les affectations
INSERT INTO affectations (voyage_id, employe_id, role_voyage, commentaire) VALUES
(1, 2, 'CONDUCTEUR', 'Conducteur principal pour ce trajet'),
(1, 3, 'CONTROLEUR', 'Contrôle des billets'),
(2, 2, 'CONDUCTEUR', 'Conducteur expérimenté'),
(2, 4, 'CHEF_DE_BORD', 'Responsable de l\'équipe'),
(3, 3, 'CONTROLEUR', 'Service voyageurs'),
(3, 5, 'TECHNICIEN', 'Maintenance préventive');

-- Insertion de données de démonstration pour les paiements
INSERT INTO paiements (reservation_id, montant, statut_paiement, type_paiement, methode_paiement, transaction_id, date_paiement) VALUES
(1, 89.50, 'CONFIRME', 'PAIEMENT', 'CARTE', 'TXN001', NOW() - INTERVAL 2 DAY),
(2, 65.00, 'CONFIRME', 'PAIEMENT', 'PAYPAL', 'TXN002', NOW() - INTERVAL 1 DAY),
(3, 120.00, 'EN_ATTENTE', 'PAIEMENT', 'CARTE', NULL, NULL),
(4, -55.00, 'CONFIRME', 'REMBOURSEMENT', 'CARTE', 'REF001', NOW() - INTERVAL 3 DAY),
(5, 110.00, 'ECHEC', 'PAIEMENT', 'CARTE', NULL, NULL);

-- Insertion de données de démonstration pour les demandes d'annulation
INSERT INTO demandes_annulation (reservation_id, utilisateur_id, motif, statut_annulation, montant_paye, frais_annulation, montant_remboursement) VALUES
(1, 1, 'Changement de programme personnel', 'EN_ATTENTE', 89.50, 15.00, 74.50),
(2, 2, 'Problème de santé', 'APPROUVEE', 65.00, 10.00, 55.00),
(3, 3, 'Urgence familiale', 'TRAITEE', 120.00, 20.00, 100.00);

-- Mise à jour des utilisateurs pour avoir des employés
UPDATE utilisateurs SET type_utilisateur = 'EMPLOYE' WHERE id IN (2, 3, 4, 5);

-- Affichage des tables créées
SHOW TABLES;

-- Vérification des données
SELECT 'Affectations' as Table_Name, COUNT(*) as Count FROM affectations
UNION ALL
SELECT 'Paiements', COUNT(*) FROM paiements
UNION ALL
SELECT 'Demandes Annulation', COUNT(*) FROM demandes_annulation;
