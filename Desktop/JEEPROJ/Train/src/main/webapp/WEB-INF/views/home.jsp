<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrainSystem - Réservation de billets de train</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
            min-height: 70vh;
            display: flex;
            align-items: center;
        }
        .feature-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            height: 100%;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .auth-section {
            background: linear-gradient(45deg, #f8f9fa 0%, #e9ecef 100%);
        }
        .auth-card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
        }
        .stats-section {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
        }
        .stat-item {
            text-align: center;
            padding: 2rem 1rem;
        }
        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            color: #3498db;
        }
        .train-animation {
            animation: float 3s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        .service-icon {
            font-size: 3rem;
            color: #3498db;
            margin-bottom: 1rem;
        }
        .navbar-brand {
            font-size: 1.5rem;
            font-weight: bold;
        }
        .btn-auth {
            border-radius: 25px;
            padding: 8px 20px;
            margin: 0 5px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/">
                <i class="fas fa-train"></i> TrainSystem
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="${pageContext.request.contextPath}/">
                            <i class="fas fa-home"></i> Accueil
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="${pageContext.request.contextPath}/search">
                            <i class="fas fa-search"></i> Rechercher
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#services">
                            <i class="fas fa-cogs"></i> Services
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">
                            <i class="fas fa-info-circle"></i> À propos
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link btn btn-outline-light btn-auth" href="${pageContext.request.contextPath}/login">
                            <i class="fas fa-sign-in-alt"></i> Connexion
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-primary btn-auth" href="${pageContext.request.contextPath}/register">
                            <i class="fas fa-user-plus"></i> Inscription
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-3 fw-bold mb-4">
                        Voyagez en toute simplicité avec 
                        <span class="text-warning">TrainSystem</span>
                    </h1>
                    <p class="lead mb-4 fs-5">
                        Réservez vos billets de train en ligne rapidement et facilement. 
                        Découvrez nos destinations, comparez les prix et voyagez en toute sérénité.
                    </p>
                    <div class="d-flex flex-wrap gap-3 mb-4">
                        <a href="${pageContext.request.contextPath}/search" class="btn btn-warning btn-lg px-4">
                            <i class="fas fa-search"></i> Rechercher un trajet
                        </a>
                        <a href="${pageContext.request.contextPath}/register" class="btn btn-outline-light btn-lg px-4">
                            <i class="fas fa-user-plus"></i> Créer un compte
                        </a>
                    </div>
                    <div class="d-flex align-items-center gap-4">
                        <div class="text-center">
                            <div class="fw-bold fs-4">1000+</div>
                            <small>Destinations</small>
                        </div>
                        <div class="text-center">
                            <div class="fw-bold fs-4">50K+</div>
                            <small>Clients satisfaits</small>
                        </div>
                        <div class="text-center">
                            <div class="fw-bold fs-4">24/7</div>
                            <small>Support client</small>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 text-center">
                    <div class="train-animation">
                        <i class="fas fa-train fa-10x text-warning opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Recherche rapide -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="card shadow-lg border-0">
                        <div class="card-body p-5">
                            <div class="text-center mb-5">
                                <h2 class="fw-bold text-primary">
                                    <i class="fas fa-search"></i> Rechercher votre voyage
                                </h2>
                                <p class="text-muted">Trouvez le trajet parfait en quelques clics</p>
                            </div>
                            <form action="${pageContext.request.contextPath}/search" method="get">
                                <div class="row g-4">
                                    <div class="col-md-4">
                                        <label for="villeDepart" class="form-label fw-bold">
                                            <i class="fas fa-map-marker-alt text-success"></i> Ville de départ
                                        </label>
                                        <input type="text" class="form-control form-control-lg" id="villeDepart" 
                                               name="villeDepart" placeholder="Ex: Paris" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="villeArrivee" class="form-label fw-bold">
                                            <i class="fas fa-map-marker-alt text-danger"></i> Ville d'arrivée
                                        </label>
                                        <input type="text" class="form-control form-control-lg" id="villeArrivee" 
                                               name="villeArrivee" placeholder="Ex: Lyon" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="dateVoyage" class="form-label fw-bold">
                                            <i class="fas fa-calendar text-primary"></i> Date de voyage
                                        </label>
                                        <input type="date" class="form-control form-control-lg" id="dateVoyage" 
                                               name="dateVoyage" required>
                                    </div>
                                </div>
                                <div class="text-center mt-5">
                                    <button type="submit" class="btn btn-primary btn-lg px-5 py-3">
                                        <i class="fas fa-search"></i> Rechercher maintenant
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistiques -->
    <section class="stats-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-3 stat-item">
                    <div class="stat-number">1000+</div>
                    <h5>Destinations</h5>
                    <p>Voyagez partout en France</p>
                </div>
                <div class="col-md-3 stat-item">
                    <div class="stat-number">50K+</div>
                    <h5>Clients satisfaits</h5>
                    <p>Nous font confiance</p>
                </div>
                <div class="col-md-3 stat-item">
                    <div class="stat-number">24/7</div>
                    <h5>Support client</h5>
                    <p>Assistance disponible</p>
                </div>
                <div class="col-md-3 stat-item">
                    <div class="stat-number">99%</div>
                    <h5>Ponctualité</h5>
                    <p>Trains à l'heure</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold">Nos Services</h2>
                <p class="text-muted fs-5">Découvrez tout ce que nous offrons pour faciliter vos voyages</p>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card text-center p-4">
                        <div class="service-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h5 class="fw-bold">Recherche Intelligente</h5>
                        <p class="text-muted">Trouvez rapidement les trajets qui vous conviennent avec notre moteur de recherche avancé et nos filtres personnalisés.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card text-center p-4">
                        <div class="service-icon">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <h5 class="fw-bold">Réservation Instantanée</h5>
                        <p class="text-muted">Réservez vos billets en quelques clics et recevez votre confirmation par email immédiatement.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card text-center p-4">
                        <div class="service-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h5 class="fw-bold">Paiement Sécurisé</h5>
                        <p class="text-muted">Vos transactions sont protégées par nos systèmes de sécurité de pointe et le cryptage SSL.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card text-center p-4">
                        <div class="service-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h5 class="fw-bold">Billets Électroniques</h5>
                        <p class="text-muted">Téléchargez vos billets en PDF et voyagez sans papier. Écologique et pratique !</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card text-center p-4">
                        <div class="service-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h5 class="fw-bold">Support 24/7</h5>
                        <p class="text-muted">Notre équipe de support est disponible 24h/24 et 7j/7 pour vous aider en cas de besoin.</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="card feature-card text-center p-4">
                        <div class="service-icon">
                            <i class="fas fa-undo"></i>
                        </div>
                        <h5 class="fw-bold">Annulation Flexible</h5>
                        <p class="text-muted">Modifiez ou annulez vos réservations facilement selon nos conditions flexibles.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section Connexion/Inscription -->
    <section class="auth-section py-5">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="fw-bold">Rejoignez TrainSystem</h2>
                <p class="text-muted fs-5">Créez votre compte pour profiter de tous nos services</p>
            </div>
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="card auth-card h-100">
                                <div class="card-body text-center p-5">
                                    <i class="fas fa-sign-in-alt fa-3x text-primary mb-4"></i>
                                    <h4 class="fw-bold mb-3">Déjà client ?</h4>
                                    <p class="text-muted mb-4">
                                        Connectez-vous à votre compte pour accéder à vos réservations,
                                        votre historique de voyages et gérer vos préférences.
                                    </p>
                                    <a href="${pageContext.request.contextPath}/login" class="btn btn-primary btn-lg w-100">
                                        <i class="fas fa-sign-in-alt"></i> Se connecter
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card auth-card h-100">
                                <div class="card-body text-center p-5">
                                    <i class="fas fa-user-plus fa-3x text-success mb-4"></i>
                                    <h4 class="fw-bold mb-3">Nouveau client ?</h4>
                                    <p class="text-muted mb-4">
                                        Créez votre compte gratuitement et profitez de nos offres exclusives,
                                        de la gestion de vos voyages et bien plus encore.
                                    </p>
                                    <a href="${pageContext.request.contextPath}/register" class="btn btn-success btn-lg w-100">
                                        <i class="fas fa-user-plus"></i> S'inscrire gratuitement
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section À propos -->
    <section id="about" class="py-5 bg-light">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h2 class="fw-bold mb-4">À propos de TrainSystem</h2>
                    <p class="fs-5 text-muted mb-4">
                        TrainSystem est votre partenaire de confiance pour tous vos voyages en train.
                        Depuis notre création, nous nous engageons à vous offrir la meilleure expérience
                        de réservation en ligne.
                    </p>
                    <div class="row g-3">
                        <div class="col-sm-6">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle text-success me-3"></i>
                                <span>Interface intuitive</span>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle text-success me-3"></i>
                                <span>Réservation rapide</span>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle text-success me-3"></i>
                                <span>Prix transparents</span>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle text-success me-3"></i>
                                <span>Support client</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 text-center">
                    <img src="https://via.placeholder.com/500x400/667eea/ffffff?text=TrainSystem"
                         alt="À propos de TrainSystem" class="img-fluid rounded shadow">
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-train"></i> TrainSystem
                    </h5>
                    <p class="text-light">
                        Votre plateforme de réservation de voyages en train.
                        Voyagez facilement et en toute sécurité avec nous.
                    </p>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-light"><i class="fab fa-facebook fa-lg"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-twitter fa-lg"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-instagram fa-lg"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-linkedin fa-lg"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">Navigation</h6>
                    <ul class="list-unstyled">
                        <li><a href="${pageContext.request.contextPath}/" class="text-light text-decoration-none">Accueil</a></li>
                        <li><a href="${pageContext.request.contextPath}/search" class="text-light text-decoration-none">Rechercher</a></li>
                        <li><a href="#services" class="text-light text-decoration-none">Services</a></li>
                        <li><a href="#about" class="text-light text-decoration-none">À propos</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">Compte</h6>
                    <ul class="list-unstyled">
                        <li><a href="${pageContext.request.contextPath}/login" class="text-light text-decoration-none">Connexion</a></li>
                        <li><a href="${pageContext.request.contextPath}/register" class="text-light text-decoration-none">Inscription</a></li>
                        <li><a href="#" class="text-light text-decoration-none">Mot de passe oublié</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 mb-4">
                    <h6 class="fw-bold mb-3">Contact</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-phone me-2"></i>
                            <span>3635 (Service gratuit + prix appel)</span>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-envelope me-2"></i>
                            <span><EMAIL></span>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            <span>123 Avenue des Trains, 75001 Paris</span>
                        </li>
                    </ul>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2025 TrainSystem. Tous droits réservés.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-light text-decoration-none me-3">Conditions d'utilisation</a>
                    <a href="#" class="text-light text-decoration-none me-3">Politique de confidentialité</a>
                    <a href="#" class="text-light text-decoration-none">Mentions légales</a>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Définir la date minimale à aujourd'hui
        document.addEventListener('DOMContentLoaded', function() {
            const dateInput = document.querySelector('input[name="dateVoyage"]');
            if (dateInput) {
                const today = new Date().toISOString().split('T')[0];
                dateInput.min = today;
                dateInput.value = today;
            }
        });

        // Animation smooth scroll pour les liens d'ancrage
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
