<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Paiements - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .admin-sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .admin-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .payment-card {
            border-left: 4px solid #28a745;
            transition: transform 0.2s;
        }
        .payment-card:hover {
            transform: translateY(-2px);
        }
        .status-confirmed {
            border-left-color: #28a745;
        }
        .status-pending {
            border-left-color: #ffc107;
        }
        .status-failed {
            border-left-color: #dc3545;
        }
        .status-refund {
            border-left-color: #17a2b8;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 admin-sidebar p-0">
                <div class="d-flex flex-column p-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-cogs"></i> Administration
                        </h4>
                        <small class="text-light">Bonjour ${sessionScope.user.prenom}</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin">
                            <i class="fas fa-tachometer-alt me-2"></i> Tableau de bord
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/gares">
                            <i class="fas fa-building me-2"></i> Gares
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/trajets">
                            <i class="fas fa-route me-2"></i> Trajets
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/voyages">
                            <i class="fas fa-train me-2"></i> Voyages
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/users">
                            <i class="fas fa-users me-2"></i> Utilisateurs
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/reservations">
                            <i class="fas fa-ticket-alt me-2"></i> Réservations
                        </a>
                        <a class="nav-link text-white active" href="${pageContext.request.contextPath}/admin/payments">
                            <i class="fas fa-credit-card me-2"></i> Paiements
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/cancellations">
                            <i class="fas fa-undo me-2"></i> Annulations
                        </a>
                        
                        <hr class="text-white">
                        
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/">
                            <i class="fas fa-home me-2"></i> Retour au site
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/logout">
                            <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 admin-content">
                <div class="container-fluid py-4">
                    
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="h3 mb-0">
                            <i class="fas fa-credit-card me-2"></i>
                            Gestion des Paiements
                        </h1>
                        <div>
                            <button class="btn btn-outline-secondary" onclick="window.location.reload()">
                                <i class="fas fa-sync-alt me-1"></i> Actualiser
                            </button>
                            <button class="btn btn-success" onclick="exportPayments()">
                                <i class="fas fa-download me-1"></i> Exporter
                            </button>
                        </div>
                    </div>
                    
                    <!-- Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center border-success">
                                <div class="card-body">
                                    <h5 class="card-title text-success">€ 2,450</h5>
                                    <p class="card-text">Revenus total</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center border-primary">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">€ 890</h5>
                                    <p class="card-text">Revenus ce mois</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center border-warning">
                                <div class="card-body">
                                    <h5 class="card-title text-warning">€ 170</h5>
                                    <p class="card-text">En attente</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center border-info">
                                <div class="card-body">
                                    <h5 class="card-title text-info">€ 95</h5>
                                    <p class="card-text">Remboursements à traiter</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Filters -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <form method="get" class="row g-3">
                                <div class="col-md-3">
                                    <label for="status" class="form-label">Statut</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="">Tous les statuts</option>
                                        <option value="CONFIRME">Confirmé</option>
                                        <option value="EN_ATTENTE">En attente</option>
                                        <option value="ECHEC">Échec</option>
                                        <option value="REMBOURSE">Remboursé</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="type" class="form-label">Type</label>
                                    <select class="form-select" id="type" name="type">
                                        <option value="">Tous les types</option>
                                        <option value="PAIEMENT">Paiement</option>
                                        <option value="REMBOURSEMENT">Remboursement</option>
                                        <option value="FRAIS">Frais</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="dateFrom" class="form-label">Date de début</label>
                                    <input type="date" class="form-control" id="dateFrom" name="dateFrom">
                                </div>
                                <div class="col-md-3">
                                    <label for="search" class="form-label">Rechercher</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="search" name="search" 
                                               placeholder="Transaction ID, réservation...">
                                        <button class="btn btn-outline-secondary" type="submit">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Payments Table -->
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Liste des paiements</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>ID</th>
                                            <th>Réservation</th>
                                            <th>Type</th>
                                            <th>Montant</th>
                                            <th>Méthode</th>
                                            <th>Statut</th>
                                            <th>Date</th>
                                            <th>Transaction ID</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Demo data - Replace with actual data -->
                                        <tr>
                                            <td>#PAY001</td>
                                            <td>#RES123</td>
                                            <td><span class="badge bg-primary">Paiement</span></td>
                                            <td class="text-success fw-bold">€ 89.50</td>
                                            <td><i class="fas fa-credit-card me-1"></i> Carte</td>
                                            <td><span class="badge bg-success">Confirmé</span></td>
                                            <td>10/06/2025 14:30</td>
                                            <td><code>TXN001</code></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-info" onclick="viewPaymentDetails('PAY001')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>#PAY002</td>
                                            <td>#RES124</td>
                                            <td><span class="badge bg-primary">Paiement</span></td>
                                            <td class="text-success fw-bold">€ 65.00</td>
                                            <td><i class="fab fa-paypal me-1"></i> PayPal</td>
                                            <td><span class="badge bg-success">Confirmé</span></td>
                                            <td>09/06/2025 16:45</td>
                                            <td><code>TXN002</code></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-info" onclick="viewPaymentDetails('PAY002')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>#PAY003</td>
                                            <td>#RES125</td>
                                            <td><span class="badge bg-primary">Paiement</span></td>
                                            <td class="text-warning fw-bold">€ 75.50</td>
                                            <td><i class="fas fa-credit-card me-1"></i> Carte</td>
                                            <td><span class="badge bg-warning">En attente</span></td>
                                            <td>11/06/2025 09:15</td>
                                            <td>-</td>
                                            <td>
                                                <button class="btn btn-sm btn-success me-1" onclick="confirmPayment('PAY003')">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button class="btn btn-sm btn-danger" onclick="failPayment('PAY003')">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>#REF001</td>
                                            <td>#RES126</td>
                                            <td><span class="badge bg-info">Remboursement</span></td>
                                            <td class="text-info fw-bold">-€ 55.00</td>
                                            <td><i class="fas fa-credit-card me-1"></i> Carte</td>
                                            <td><span class="badge bg-success">Confirmé</span></td>
                                            <td>08/06/2025 11:20</td>
                                            <td><code>REF001</code></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-info" onclick="viewPaymentDetails('REF001')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>#PAY004</td>
                                            <td>#RES127</td>
                                            <td><span class="badge bg-primary">Paiement</span></td>
                                            <td class="text-danger fw-bold">€ 110.00</td>
                                            <td><i class="fas fa-credit-card me-1"></i> Carte</td>
                                            <td><span class="badge bg-danger">Échec</span></td>
                                            <td>07/06/2025 13:10</td>
                                            <td>-</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-info" onclick="viewPaymentDetails('PAY004')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Pagination -->
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" tabindex="-1">Précédent</a>
                                    </li>
                                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                                    <li class="page-item">
                                        <a class="page-link" href="#">Suivant</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmPayment(id) {
            if (confirm('Confirmer ce paiement ?')) {
                alert('Paiement confirmé (fonctionnalité à implémenter)');
            }
        }
        
        function failPayment(id) {
            if (confirm('Marquer ce paiement comme échoué ?')) {
                alert('Paiement marqué comme échoué (fonctionnalité à implémenter)');
            }
        }
        
        function viewPaymentDetails(id) {
            alert('Affichage des détails du paiement ' + id + ' (fonctionnalité à implémenter)');
        }
        
        function exportPayments() {
            alert('Export des paiements (fonctionnalité à implémenter)');
        }
    </script>
</body>
</html>
