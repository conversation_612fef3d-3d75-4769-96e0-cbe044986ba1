<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Réservations - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .admin-sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .admin-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 admin-sidebar p-0">
                <div class="d-flex flex-column p-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-cogs"></i> Administration
                        </h4>
                        <small class="text-light">Bonjour ${sessionScope.user.prenom}</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin">
                            <i class="fas fa-tachometer-alt me-2"></i> Tableau de bord
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/gares">
                            <i class="fas fa-building me-2"></i> Gares
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/trajets">
                            <i class="fas fa-route me-2"></i> Trajets
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/voyages">
                            <i class="fas fa-train me-2"></i> Voyages
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/users">
                            <i class="fas fa-users me-2"></i> Utilisateurs
                        </a>
                        <a class="nav-link text-white active" href="${pageContext.request.contextPath}/admin/reservations">
                            <i class="fas fa-ticket-alt me-2"></i> Réservations
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/payments">
                            <i class="fas fa-credit-card me-2"></i> Paiements
                        </a>
                        
                        <hr class="text-white">
                        
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/">
                            <i class="fas fa-home me-2"></i> Retour au site
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/logout">
                            <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 admin-content">
                <div class="container-fluid py-4">
                    
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="h3 mb-0">
                            <i class="fas fa-ticket-alt me-2"></i>
                            Gestion des Réservations
                        </h1>
                        <div>
                            <button class="btn btn-outline-secondary" onclick="window.location.reload()">
                                <i class="fas fa-sync-alt me-1"></i> Actualiser
                            </button>
                        </div>
                    </div>
                    
                    <!-- Messages -->
                    <c:if test="${not empty sessionScope.successMessage}">
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            ${sessionScope.successMessage}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <c:remove var="successMessage" scope="session" />
                    </c:if>
                    
                    <c:if test="${not empty sessionScope.errorMessage}">
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            ${sessionScope.errorMessage}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <c:remove var="errorMessage" scope="session" />
                    </c:if>
                    
                    <!-- Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">${reservations.size()}</h5>
                                    <p class="card-text">Total réservations</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-success">
                                        <c:set var="nbConfirmees" value="0" />
                                        <c:forEach var="reservation" items="${reservations}">
                                            <c:if test="${reservation.statut == 'CONFIRMEE'}">
                                                <c:set var="nbConfirmees" value="${nbConfirmees + 1}" />
                                            </c:if>
                                        </c:forEach>
                                        ${nbConfirmees}
                                    </h5>
                                    <p class="card-text">Confirmées</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-warning">
                                        <c:set var="nbEnAttente" value="0" />
                                        <c:forEach var="reservation" items="${reservations}">
                                            <c:if test="${reservation.statut == 'EN_ATTENTE'}">
                                                <c:set var="nbEnAttente" value="${nbEnAttente + 1}" />
                                            </c:if>
                                        </c:forEach>
                                        ${nbEnAttente}
                                    </h5>
                                    <p class="card-text">En attente</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-danger">
                                        <c:set var="nbAnnulees" value="0" />
                                        <c:forEach var="reservation" items="${reservations}">
                                            <c:if test="${reservation.statut == 'ANNULEE'}">
                                                <c:set var="nbAnnulees" value="${nbAnnulees + 1}" />
                                            </c:if>
                                        </c:forEach>
                                        ${nbAnnulees}
                                    </h5>
                                    <p class="card-text">Annulées</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Reservations Table -->
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Liste des réservations</h6>
                        </div>
                        <div class="card-body">
                            <c:choose>
                                <c:when test="${not empty reservations}">
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>N° Réservation</th>
                                                    <th>Client</th>
                                                    <th>Voyage</th>
                                                    <th>Places</th>
                                                    <th>Prix Total</th>
                                                    <th>Statut</th>
                                                    <th>Date</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <c:forEach var="reservation" items="${reservations}">
                                                    <tr>
                                                        <td>
                                                            <strong>${reservation.numeroReservation}</strong>
                                                        </td>
                                                        <td>
                                                            <div>
                                                                <div class="fw-bold">${reservation.utilisateur.prenom} ${reservation.utilisateur.nom}</div>
                                                                <small class="text-muted">${reservation.utilisateur.email}</small>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div>
                                                                <div class="fw-bold">${reservation.voyage.trajet.libelle}</div>
                                                                <small class="text-muted">
                                                                    <fmt:formatDate value="${reservation.voyage.dateVoyage}" pattern="dd/MM/yyyy" />
                                                                    - ${reservation.voyage.trajet.heureDepart}
                                                                </small>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-info">${reservation.nombrePlaces}</span>
                                                        </td>
                                                        <td>
                                                            <strong><fmt:formatNumber value="${reservation.prixTotal}" type="currency" currencySymbol="€" /></strong>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-${reservation.statut == 'CONFIRMEE' ? 'success' : 
                                                                                   reservation.statut == 'EN_ATTENTE' ? 'warning' : 
                                                                                   reservation.statut == 'ANNULEE' ? 'danger' : 'secondary'}">
                                                                ${reservation.statut}
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <fmt:formatDate value="${reservation.dateReservation}" pattern="dd/MM/yyyy HH:mm" />
                                                        </td>
                                                        <td>
                                                            <div class="btn-group" role="group">
                                                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                                                        data-bs-toggle="modal" 
                                                                        data-bs-target="#detailsModal${reservation.id}">
                                                                    <i class="fas fa-eye"></i>
                                                                </button>
                                                                <c:if test="${reservation.statut == 'EN_ATTENTE'}">
                                                                    <button type="button" class="btn btn-sm btn-outline-success" 
                                                                            onclick="confirmerReservation(${reservation.id})">
                                                                        <i class="fas fa-check"></i>
                                                                    </button>
                                                                </c:if>
                                                                <c:if test="${reservation.statut != 'ANNULEE'}">
                                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                                            onclick="annulerReservation(${reservation.id})">
                                                                        <i class="fas fa-times"></i>
                                                                    </button>
                                                                </c:if>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    
                                                    <!-- Modal de détails -->
                                                    <div class="modal fade" id="detailsModal${reservation.id}" tabindex="-1">
                                                        <div class="modal-dialog">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title">Détails de la réservation</h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    <div class="row">
                                                                        <div class="col-md-6">
                                                                            <strong>Numéro:</strong><br>
                                                                            ${reservation.numeroReservation}
                                                                        </div>
                                                                        <div class="col-md-6">
                                                                            <strong>Statut:</strong><br>
                                                                            <span class="badge bg-${reservation.statut == 'CONFIRMEE' ? 'success' : 
                                                                                                   reservation.statut == 'EN_ATTENTE' ? 'warning' : 
                                                                                                   reservation.statut == 'ANNULEE' ? 'danger' : 'secondary'}">
                                                                                ${reservation.statut}
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                    <hr>
                                                                    <div class="row">
                                                                        <div class="col-md-6">
                                                                            <strong>Client:</strong><br>
                                                                            ${reservation.utilisateur.prenom} ${reservation.utilisateur.nom}<br>
                                                                            <small class="text-muted">${reservation.utilisateur.email}</small>
                                                                        </div>
                                                                        <div class="col-md-6">
                                                                            <strong>Voyage:</strong><br>
                                                                            ${reservation.voyage.trajet.libelle}<br>
                                                                            <small class="text-muted">
                                                                                <fmt:formatDate value="${reservation.voyage.dateVoyage}" pattern="dd/MM/yyyy" />
                                                                                à ${reservation.voyage.trajet.heureDepart}
                                                                            </small>
                                                                        </div>
                                                                    </div>
                                                                    <hr>
                                                                    <div class="row">
                                                                        <div class="col-md-6">
                                                                            <strong>Nombre de places:</strong><br>
                                                                            ${reservation.nombrePlaces}
                                                                        </div>
                                                                        <div class="col-md-6">
                                                                            <strong>Prix total:</strong><br>
                                                                            <fmt:formatNumber value="${reservation.prixTotal}" type="currency" currencySymbol="€" />
                                                                        </div>
                                                                    </div>
                                                                    <hr>
                                                                    <div class="row">
                                                                        <div class="col-md-6">
                                                                            <strong>Date de réservation:</strong><br>
                                                                            <fmt:formatDate value="${reservation.dateReservation}" pattern="dd/MM/yyyy HH:mm" />
                                                                        </div>
                                                                        <c:if test="${not empty reservation.dateAnnulation}">
                                                                            <div class="col-md-6">
                                                                                <strong>Date d'annulation:</strong><br>
                                                                                <fmt:formatDate value="${reservation.dateAnnulation}" pattern="dd/MM/yyyy HH:mm" />
                                                                            </div>
                                                                        </c:if>
                                                                    </div>
                                                                    <c:if test="${not empty reservation.motifAnnulation}">
                                                                        <hr>
                                                                        <strong>Motif d'annulation:</strong><br>
                                                                        ${reservation.motifAnnulation}
                                                                    </c:if>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </c:forEach>
                                            </tbody>
                                        </table>
                                    </div>
                                </c:when>
                                <c:otherwise>
                                    <div class="text-center py-4">
                                        <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Aucune réservation trouvée</p>
                                    </div>
                                </c:otherwise>
                            </c:choose>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmerReservation(reservationId) {
            if (confirm('Êtes-vous sûr de vouloir confirmer cette réservation ?')) {
                // TODO: Implémenter la confirmation de réservation
                alert('Fonctionnalité en cours d\'implémentation');
            }
        }
        
        function annulerReservation(reservationId) {
            if (confirm('Êtes-vous sûr de vouloir annuler cette réservation ?')) {
                // TODO: Implémenter l'annulation de réservation
                alert('Fonctionnalité en cours d\'implémentation');
            }
        }
    </script>
</body>
</html>
