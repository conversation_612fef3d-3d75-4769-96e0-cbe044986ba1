<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demande d'annulation - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .cancel-card {
            border: 2px solid #dc3545;
            border-radius: 15px;
            background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
        }
        .warning-box {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #ffc107;
            border-radius: 10px;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/">
                <i class="fas fa-train"></i> TrainSystem
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">Bonjour ${sessionScope.user.prenom}</span>
                <a class="nav-link" href="${pageContext.request.contextPath}/reservation/">Mes réservations</a>
                <a class="nav-link" href="${pageContext.request.contextPath}/">Accueil</a>
            </div>
        </div>
    </nav>

    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Header -->
                <div class="text-center mb-4">
                    <h2><i class="fas fa-exclamation-triangle text-danger"></i> Demande d'annulation</h2>
                    <p class="text-muted">Demandez l'annulation de votre réservation</p>
                </div>

                <!-- Messages -->
                <c:if test="${not empty errorMessage}">
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle"></i> ${errorMessage}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </c:if>

                <!-- Informations de la réservation -->
                <div class="card cancel-card mb-4">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0"><i class="fas fa-ticket-alt"></i> Réservation à annuler</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-route"></i> Trajet</h6>
                                <p class="mb-2">
                                    <strong>${reservation.voyage.trajet.gareDepart.ville}</strong>
                                    <i class="fas fa-arrow-right mx-2"></i>
                                    <strong>${reservation.voyage.trajet.gareArrivee.ville}</strong>
                                </p>
                                <p class="text-muted mb-3">
                                    <i class="fas fa-calendar"></i> 
                                    <fmt:formatDate value="${reservation.voyage.dateVoyage}" pattern="EEEE dd MMMM yyyy" />
                                    <br>
                                    <i class="fas fa-clock"></i> 
                                    ${reservation.voyage.trajet.heureDepart} → ${reservation.voyage.trajet.heureArrivee}
                                </p>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-info-circle"></i> Détails de la réservation</h6>
                                <p class="mb-1">
                                    <strong>Numéro :</strong> ${reservation.numeroReservation}
                                </p>
                                <p class="mb-1">
                                    <strong>Places :</strong> ${reservation.nombrePlaces}
                                </p>
                                <p class="mb-1">
                                    <strong>Prix payé :</strong> 
                                    <fmt:formatNumber value="${reservation.prixTotal}" type="currency" currencySymbol="€" />
                                </p>
                                <p class="mb-0">
                                    <span class="badge bg-${reservation.statut == 'CONFIRMEE' ? 'success' : 'warning'}">
                                        ${reservation.statut}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Avertissements -->
                <div class="warning-box p-4 mb-4">
                    <h6><i class="fas fa-exclamation-triangle text-warning"></i> Conditions d'annulation</h6>
                    <ul class="mb-3">
                        <li><strong>Délai :</strong> L'annulation doit être demandée au moins 24h avant le départ</li>
                        <li><strong>Validation :</strong> Votre demande sera examinée par un administrateur</li>
                        <li><strong>Remboursement :</strong> En cas d'acceptation, le remboursement sera effectué sous 5-10 jours ouvrés</li>
                        <li><strong>Frais :</strong> Des frais d'annulation peuvent s'appliquer selon les conditions générales</li>
                    </ul>
                    
                    <c:set var="heuresAvantDepart" value="${(reservation.voyage.dateVoyage.toEpochDay() * 24 + reservation.voyage.trajet.heureDepart.hour) - (java.time.LocalDateTime.now().toLocalDate().toEpochDay() * 24 + java.time.LocalDateTime.now().hour)}" />
                    
                    <c:choose>
                        <c:when test="${heuresAvantDepart < 24}">
                            <div class="alert alert-danger mb-0">
                                <i class="fas fa-times-circle"></i> 
                                <strong>Attention :</strong> Il reste moins de 24h avant le départ. 
                                L'annulation pourrait ne pas être acceptée ou entraîner des frais supplémentaires.
                            </div>
                        </c:when>
                        <c:otherwise>
                            <div class="alert alert-success mb-0">
                                <i class="fas fa-check-circle"></i> 
                                <strong>Délai respecté :</strong> Votre demande d'annulation peut être traitée normalement.
                            </div>
                        </c:otherwise>
                    </c:choose>
                </div>

                <!-- Formulaire de demande d'annulation -->
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-edit"></i> Formulaire de demande d'annulation</h5>
                    </div>
                    <div class="card-body">
                        <form action="${pageContext.request.contextPath}/reservation/cancel/${reservation.id}" method="post">
                            <!-- Motif d'annulation -->
                            <div class="mb-3">
                                <label for="motifAnnulation" class="form-label">
                                    <i class="fas fa-comment"></i> Motif de l'annulation <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="motifAnnulation" name="motifAnnulation" required>
                                    <option value="">Sélectionnez un motif</option>
                                    <option value="CHANGEMENT_PROGRAMME">Changement de programme</option>
                                    <option value="PROBLEME_SANTE">Problème de santé</option>
                                    <option value="URGENCE_FAMILIALE">Urgence familiale</option>
                                    <option value="PROBLEME_PROFESSIONNEL">Problème professionnel</option>
                                    <option value="INSATISFACTION_SERVICE">Insatisfaction du service</option>
                                    <option value="ERREUR_RESERVATION">Erreur lors de la réservation</option>
                                    <option value="AUTRE">Autre motif</option>
                                </select>
                            </div>

                            <!-- Explication détaillée -->
                            <div class="mb-3">
                                <label for="explicationDetaillee" class="form-label">
                                    <i class="fas fa-align-left"></i> Explication détaillée <span class="text-danger">*</span>
                                </label>
                                <textarea class="form-control" id="explicationDetaillee" name="explicationDetaillee" 
                                          rows="4" required placeholder="Expliquez en détail les raisons de votre demande d'annulation..."></textarea>
                                <div class="form-text">
                                    Plus votre explication est détaillée, plus votre demande a de chances d'être acceptée rapidement.
                                </div>
                            </div>

                            <!-- Informations de remboursement -->
                            <div class="mb-3">
                                <label for="methodePaiement" class="form-label">
                                    <i class="fas fa-credit-card"></i> Méthode de remboursement souhaitée
                                </label>
                                <select class="form-select" id="methodePaiement" name="methodePaiement">
                                    <option value="CARTE_ORIGINE">Remboursement sur la carte utilisée pour le paiement</option>
                                    <option value="VIREMENT">Virement bancaire</option>
                                    <option value="AVOIR">Avoir pour un futur voyage</option>
                                </select>
                            </div>

                            <!-- Acceptation des conditions -->
                            <div class="mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="acceptConditions" required>
                                    <label class="form-check-label" for="acceptConditions">
                                        J'accepte les <a href="#" target="_blank">conditions d'annulation</a> et 
                                        je comprends que ma demande sera soumise à validation administrative
                                    </label>
                                </div>
                            </div>

                            <!-- Boutons d'action -->
                            <div class="d-grid gap-2 d-md-flex justify-content-md-between">
                                <a href="${pageContext.request.contextPath}/reservation/details/${reservation.id}" 
                                   class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left"></i> Retour aux détails
                                </a>
                                <button type="submit" class="btn btn-danger" id="submitBtn" disabled>
                                    <i class="fas fa-paper-plane"></i> Envoyer la demande d'annulation
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Informations sur le processus -->
                <div class="alert alert-info mt-4">
                    <h6><i class="fas fa-info-circle"></i> Que se passe-t-il après ma demande ?</h6>
                    <ol class="mb-0">
                        <li><strong>Réception :</strong> Votre demande est immédiatement transmise à nos services</li>
                        <li><strong>Examen :</strong> Un administrateur examine votre demande sous 24-48h</li>
                        <li><strong>Notification :</strong> Vous recevez un email avec la décision</li>
                        <li><strong>Remboursement :</strong> En cas d'acceptation, le remboursement est traité sous 5-10 jours</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const acceptConditions = document.getElementById('acceptConditions');
        const submitBtn = document.getElementById('submitBtn');
        const motifSelect = document.getElementById('motifAnnulation');
        const explicationTextarea = document.getElementById('explicationDetaillee');
        
        function updateSubmitButton() {
            const motifSelected = motifSelect.value !== '';
            const explicationFilled = explicationTextarea.value.trim().length >= 10;
            const conditionsAccepted = acceptConditions.checked;
            
            submitBtn.disabled = !(motifSelected && explicationFilled && conditionsAccepted);
        }
        
        motifSelect.addEventListener('change', updateSubmitButton);
        explicationTextarea.addEventListener('input', updateSubmitButton);
        acceptConditions.addEventListener('change', updateSubmitButton);
        
        // Confirmation avant envoi
        document.querySelector('form').addEventListener('submit', function(e) {
            if (!confirm('Êtes-vous sûr de vouloir envoyer cette demande d\'annulation ?\n\nCette action ne peut pas être annulée.')) {
                e.preventDefault();
            }
        });
    </script>
</body>
</html>
