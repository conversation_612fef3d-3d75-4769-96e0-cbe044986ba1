<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Détails de ma réservation - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .ticket-card {
            border: 3px solid #007bff;
            border-radius: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            box-shadow: 0 8px 25px rgba(0,123,255,0.15);
        }
        .ticket-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border-radius: 17px 17px 0 0;
        }
        .qr-placeholder {
            width: 120px;
            height: 120px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
        }
        .status-badge {
            font-size: 1.1em;
            padding: 0.5rem 1rem;
        }
        @media print {
            .no-print { display: none !important; }
            .ticket-card { border: 2px solid #000; }
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary no-print">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/">
                <i class="fas fa-train"></i> TrainSystem
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">Bonjour ${sessionScope.user.prenom}</span>
                <a class="nav-link" href="${pageContext.request.contextPath}/reservation/">Mes réservations</a>
                <a class="nav-link" href="${pageContext.request.contextPath}/">Accueil</a>
            </div>
        </div>
    </nav>

    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- Header -->
                <div class="text-center mb-4 no-print">
                    <h2><i class="fas fa-ticket-alt text-primary"></i> Détails de ma réservation</h2>
                    <p class="text-muted">Réservation n° ${reservation.numeroReservation}</p>
                </div>

                <!-- Messages -->
                <c:if test="${not empty sessionScope.successMessage}">
                    <div class="alert alert-success alert-dismissible fade show no-print">
                        <i class="fas fa-check-circle"></i> ${sessionScope.successMessage}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <c:remove var="successMessage" scope="session"/>
                </c:if>

                <!-- Billet électronique -->
                <div class="ticket-card mb-4">
                    <div class="ticket-header p-4">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h3 class="mb-1"><i class="fas fa-train"></i> TrainSystem</h3>
                                <p class="mb-0">Billet électronique de transport</p>
                            </div>
                            <div class="col-md-4 text-end">
                                <span class="status-badge badge bg-${reservation.statut == 'CONFIRMEE' ? 'success' : reservation.statut == 'EN_ATTENTE' ? 'warning' : 'danger'}">
                                    ${reservation.statut}
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="p-4">
                        <div class="row">
                            <!-- Informations du voyage -->
                            <div class="col-md-8">
                                <div class="row mb-4">
                                    <div class="col-6">
                                        <h5><i class="fas fa-map-marker-alt text-success"></i> Départ</h5>
                                        <h4 class="text-primary">${reservation.voyage.trajet.gareDepart.ville}</h4>
                                        <p class="mb-1"><strong>${reservation.voyage.trajet.gareDepart.nom}</strong></p>
                                        <p class="text-muted mb-0">
                                            <i class="fas fa-clock"></i> ${reservation.voyage.trajet.heureDepart}
                                        </p>
                                    </div>
                                    <div class="col-6">
                                        <h5><i class="fas fa-map-marker-alt text-danger"></i> Arrivée</h5>
                                        <h4 class="text-primary">${reservation.voyage.trajet.gareArrivee.ville}</h4>
                                        <p class="mb-1"><strong>${reservation.voyage.trajet.gareArrivee.nom}</strong></p>
                                        <p class="text-muted mb-0">
                                            <i class="fas fa-clock"></i> ${reservation.voyage.trajet.heureArrivee}
                                        </p>
                                    </div>
                                </div>
                                
                                <div class="row mb-4">
                                    <div class="col-6">
                                        <h6><i class="fas fa-calendar"></i> Date du voyage</h6>
                                        <p class="h5 text-dark">
                                            <fmt:formatDate value="${reservation.voyage.dateVoyage}" pattern="EEEE dd MMMM yyyy" />
                                        </p>
                                    </div>
                                    <div class="col-6">
                                        <h6><i class="fas fa-train"></i> Type de train</h6>
                                        <p class="h5 text-dark">${reservation.voyage.trajet.typeTrain}</p>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-4">
                                        <h6><i class="fas fa-users"></i> Places</h6>
                                        <p class="h5 text-dark">${reservation.nombrePlaces}</p>
                                    </div>
                                    <div class="col-4">
                                        <h6><i class="fas fa-euro-sign"></i> Prix total</h6>
                                        <p class="h5 text-success">
                                            <fmt:formatNumber value="${reservation.prixTotal}" type="currency" currencySymbol="€" />
                                        </p>
                                    </div>
                                    <div class="col-4">
                                        <h6><i class="fas fa-hashtag"></i> Réservation</h6>
                                        <p class="h6 text-dark">${reservation.numeroReservation}</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- QR Code et informations passager -->
                            <div class="col-md-4 text-center">
                                <div class="qr-placeholder mx-auto mb-3">
                                    <div class="text-muted">
                                        <i class="fas fa-qrcode fa-3x"></i>
                                        <br><small>QR Code</small>
                                    </div>
                                </div>
                                
                                <h6><i class="fas fa-user"></i> Passager</h6>
                                <p class="mb-1"><strong>${reservation.utilisateur.nomComplet}</strong></p>
                                <p class="text-muted small">${reservation.utilisateur.email}</p>
                                
                                <h6 class="mt-3"><i class="fas fa-calendar-plus"></i> Réservé le</h6>
                                <p class="text-muted small">
                                    <fmt:formatDate value="${reservation.dateReservation}" pattern="dd/MM/yyyy à HH:mm" />
                                </p>
                            </div>
                        </div>
                        
                        <!-- Informations importantes -->
                        <div class="border-top pt-3 mt-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-info-circle text-info"></i> Informations importantes</h6>
                                    <ul class="small text-muted mb-0">
                                        <li>Présentez-vous en gare 30 minutes avant le départ</li>
                                        <li>Munissez-vous d'une pièce d'identité valide</li>
                                        <li>Ce billet est nominatif et non cessible</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-phone text-info"></i> Contact</h6>
                                    <p class="small text-muted mb-0">
                                        Service client : 3635<br>
                                        Email : <EMAIL><br>
                                        Site web : www.trainsystem.fr
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="text-center no-print">
                    <div class="btn-group" role="group">
                        <a href="${pageContext.request.contextPath}/reservation/" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Retour à mes réservations
                        </a>
                        
                        <c:if test="${reservation.statut == 'CONFIRMEE'}">
                            <a href="${pageContext.request.contextPath}/reservation/pdf/${reservation.id}" 
                               class="btn btn-success">
                                <i class="fas fa-download"></i> Télécharger PDF
                            </a>
                        </c:if>
                        
                        <button onclick="window.print()" class="btn btn-info">
                            <i class="fas fa-print"></i> Imprimer
                        </button>
                        
                        <c:if test="${reservation.statut == 'CONFIRMEE' or reservation.statut == 'EN_ATTENTE'}">
                            <a href="${pageContext.request.contextPath}/reservation/edit/${reservation.id}" 
                               class="btn btn-warning">
                                <i class="fas fa-edit"></i> Modifier
                            </a>
                            
                            <a href="${pageContext.request.contextPath}/reservation/cancel/${reservation.id}" 
                               class="btn btn-outline-danger">
                                <i class="fas fa-times"></i> Demander annulation
                            </a>
                        </c:if>
                    </div>
                </div>

                <!-- Historique des modifications (si applicable) -->
                <c:if test="${not empty reservation.motifModification}">
                    <div class="card mt-4 no-print">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-history"></i> Historique des modifications</h6>
                        </div>
                        <div class="card-body">
                            <p class="mb-0">
                                <strong>Dernière modification :</strong> ${reservation.motifModification}
                            </p>
                        </div>
                    </div>
                </c:if>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
