<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formulaire Gare - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .admin-sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .admin-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 admin-sidebar p-0">
                <div class="d-flex flex-column p-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-cogs"></i> Administration
                        </h4>
                        <small class="text-light">Bonjour ${sessionScope.user.prenom}</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin">
                            <i class="fas fa-tachometer-alt me-2"></i> Tableau de bord
                        </a>
                        <a class="nav-link text-white active" href="${pageContext.request.contextPath}/admin/gares">
                            <i class="fas fa-building me-2"></i> Gares
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/trajets">
                            <i class="fas fa-route me-2"></i> Trajets
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/voyages">
                            <i class="fas fa-train me-2"></i> Voyages
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/users">
                            <i class="fas fa-users me-2"></i> Utilisateurs
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/reservations">
                            <i class="fas fa-ticket-alt me-2"></i> Réservations
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/payments">
                            <i class="fas fa-credit-card me-2"></i> Paiements
                        </a>
                        
                        <hr class="text-white">
                        
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/">
                            <i class="fas fa-home me-2"></i> Retour au site
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/logout">
                            <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 admin-content">
                <div class="container-fluid py-4">
                    
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="h3 mb-0">
                            <i class="fas fa-building me-2"></i>
                            <c:choose>
                                <c:when test="${not empty gare && not empty gare.id}">Modifier la gare</c:when>
                                <c:otherwise>Nouvelle gare</c:otherwise>
                            </c:choose>
                        </h1>
                        <div>
                            <a href="${pageContext.request.contextPath}/admin/gares" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-1"></i> Retour à la liste
                            </a>
                        </div>
                    </div>

                    <!-- Messages d'alerte -->
                    <c:if test="${not empty error}">
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            ${error}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    </c:if>

                    <c:if test="${not empty success}">
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            ${success}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    </c:if>
                    
                    <!-- Content -->
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <c:choose>
                                    <c:when test="${not empty gare && not empty gare.id}">Modification de gare</c:when>
                                    <c:otherwise>Création d'une nouvelle gare</c:otherwise>
                                </c:choose>
                            </h6>
                        </div>
                        <div class="card-body">
                            <form method="post" action="${pageContext.request.contextPath}/admin/gares/save" id="gareForm">
                                <!-- ID caché pour la modification -->
                                <c:if test="${not empty gare && not empty gare.id}">
                                    <input type="hidden" name="id" value="${gare.id}">
                                </c:if>

                                <div class="row">
                                    <!-- Informations principales -->
                                    <div class="col-md-6">
                                        <h6 class="text-primary mb-3">
                                            <i class="fas fa-info-circle me-2"></i>Informations principales
                                        </h6>

                                        <!-- Nom de la gare -->
                                        <div class="mb-3">
                                            <label for="nom" class="form-label">
                                                <i class="fas fa-building me-1"></i>Nom de la gare *
                                            </label>
                                            <input type="text" class="form-control" id="nom" name="nom"
                                                   value="${gare.nom}" required maxlength="100"
                                                   placeholder="Ex: Gare de Paris-Montparnasse">
                                            <div class="form-text">Nom complet et officiel de la gare</div>
                                        </div>

                                        <!-- Ville -->
                                        <div class="mb-3">
                                            <label for="ville" class="form-label">
                                                <i class="fas fa-city me-1"></i>Ville *
                                            </label>
                                            <input type="text" class="form-control" id="ville" name="ville"
                                                   value="${gare.ville}" required maxlength="100"
                                                   placeholder="Ex: Paris">
                                            <div class="form-text">Ville où se trouve la gare</div>
                                        </div>

                                        <!-- Code gare -->
                                        <div class="mb-3">
                                            <label for="codeGare" class="form-label">
                                                <i class="fas fa-tag me-1"></i>Code de la gare *
                                            </label>
                                            <input type="text" class="form-control" id="codeGare" name="codeGare"
                                                   value="${gare.codeGare}" required maxlength="10"
                                                   placeholder="Ex: PAR01" style="text-transform: uppercase;">
                                            <div class="form-text">Code unique d'identification (5-10 caractères)</div>
                                        </div>

                                        <!-- Statut -->
                                        <div class="mb-3">
                                            <label for="active" class="form-label">
                                                <i class="fas fa-toggle-on me-1"></i>Statut
                                            </label>
                                            <select class="form-select" id="active" name="active">
                                                <option value="true" ${gare.active ? 'selected' : ''}>
                                                    <i class="fas fa-check-circle"></i> Active
                                                </option>
                                                <option value="false" ${!gare.active ? 'selected' : ''}>
                                                    <i class="fas fa-times-circle"></i> Inactive
                                                </option>
                                            </select>
                                            <div class="form-text">Une gare inactive n'apparaît pas dans les recherches</div>
                                        </div>
                                    </div>

                                    <!-- Localisation -->
                                    <div class="col-md-6">
                                        <h6 class="text-primary mb-3">
                                            <i class="fas fa-map-marker-alt me-2"></i>Localisation
                                        </h6>

                                        <!-- Adresse -->
                                        <div class="mb-3">
                                            <label for="adresse" class="form-label">
                                                <i class="fas fa-map-pin me-1"></i>Adresse complète
                                            </label>
                                            <textarea class="form-control" id="adresse" name="adresse" rows="3"
                                                      maxlength="255" placeholder="Ex: 17 Boulevard de Vaugirard">${gare.adresse}</textarea>
                                            <div class="form-text">Adresse postale complète de la gare</div>
                                        </div>

                                        <!-- Code postal -->
                                        <div class="mb-3">
                                            <label for="codePostal" class="form-label">
                                                <i class="fas fa-mail-bulk me-1"></i>Code postal
                                            </label>
                                            <input type="text" class="form-control" id="codePostal" name="codePostal"
                                                   value="${gare.codePostal}" maxlength="10"
                                                   placeholder="Ex: 75015" pattern="[0-9]{5}">
                                            <div class="form-text">Code postal de la ville</div>
                                        </div>

                                        <!-- Coordonnées GPS -->
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="latitude" class="form-label">
                                                        <i class="fas fa-globe me-1"></i>Latitude
                                                    </label>
                                                    <input type="number" class="form-control" id="latitude" name="latitude"
                                                           value="${gare.latitude}" step="0.000001" min="-90" max="90"
                                                           placeholder="Ex: 48.8404">
                                                    <div class="form-text">Coordonnée GPS latitude</div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="longitude" class="form-label">
                                                        <i class="fas fa-globe me-1"></i>Longitude
                                                    </label>
                                                    <input type="number" class="form-control" id="longitude" name="longitude"
                                                           value="${gare.longitude}" step="0.000001" min="-180" max="180"
                                                           placeholder="Ex: 2.3188">
                                                    <div class="form-text">Coordonnée GPS longitude</div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Bouton pour obtenir les coordonnées -->
                                        <div class="mb-3">
                                            <button type="button" class="btn btn-outline-info btn-sm" id="getCoordinates">
                                                <i class="fas fa-map-marked-alt me-1"></i>
                                                Obtenir les coordonnées automatiquement
                                            </button>
                                            <div class="form-text">Utilise l'adresse pour trouver les coordonnées GPS</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Boutons d'action -->
                                <hr class="my-4">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <a href="${pageContext.request.contextPath}/admin/gares" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-1"></i>Annuler
                                        </a>
                                    </div>
                                    <div>
                                        <button type="reset" class="btn btn-outline-warning me-2">
                                            <i class="fas fa-undo me-1"></i>Réinitialiser
                                        </button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>
                                            <c:choose>
                                                <c:when test="${not empty gare && not empty gare.id}">Modifier la gare</c:when>
                                                <c:otherwise>Créer la gare</c:otherwise>
                                            </c:choose>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Validation du formulaire
            const form = document.getElementById('gareForm');
            const codeGareInput = document.getElementById('codeGare');
            const getCoordinatesBtn = document.getElementById('getCoordinates');

            // Convertir le code gare en majuscules
            codeGareInput.addEventListener('input', function() {
                this.value = this.value.toUpperCase();
            });

            // Validation du formulaire
            form.addEventListener('submit', function(e) {
                const nom = document.getElementById('nom').value.trim();
                const ville = document.getElementById('ville').value.trim();
                const codeGare = document.getElementById('codeGare').value.trim();

                if (!nom || !ville || !codeGare) {
                    e.preventDefault();
                    alert('Veuillez remplir tous les champs obligatoires (*)');
                    return false;
                }

                if (codeGare.length < 3) {
                    e.preventDefault();
                    alert('Le code gare doit contenir au moins 3 caractères');
                    return false;
                }

                // Confirmation pour la modification
                if (document.querySelector('input[name="id"]')) {
                    if (!confirm('Êtes-vous sûr de vouloir modifier cette gare ?')) {
                        e.preventDefault();
                        return false;
                    }
                }

                return true;
            });

            // Fonction pour obtenir les coordonnées GPS
            getCoordinatesBtn.addEventListener('click', function() {
                const adresse = document.getElementById('adresse').value.trim();
                const ville = document.getElementById('ville').value.trim();
                const codePostal = document.getElementById('codePostal').value.trim();

                if (!adresse && !ville) {
                    alert('Veuillez renseigner au moins l\'adresse ou la ville');
                    return;
                }

                // Construction de l'adresse complète
                let fullAddress = '';
                if (adresse) fullAddress += adresse + ', ';
                if (ville) fullAddress += ville + ', ';
                if (codePostal) fullAddress += codePostal + ', ';
                fullAddress += 'France';

                // Désactiver le bouton pendant la recherche
                getCoordinatesBtn.disabled = true;
                getCoordinatesBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Recherche en cours...';

                // Utiliser l'API de géocodage (exemple avec OpenStreetMap Nominatim)
                fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(fullAddress)}&limit=1`)
                    .then(response => response.json())
                    .then(data => {
                        if (data && data.length > 0) {
                            const result = data[0];
                            document.getElementById('latitude').value = parseFloat(result.lat).toFixed(6);
                            document.getElementById('longitude').value = parseFloat(result.lon).toFixed(6);

                            // Afficher un message de succès
                            showAlert('success', 'Coordonnées GPS trouvées et remplies automatiquement !');
                        } else {
                            showAlert('warning', 'Aucune coordonnée trouvée pour cette adresse. Veuillez les saisir manuellement.');
                        }
                    })
                    .catch(error => {
                        console.error('Erreur lors de la géolocalisation:', error);
                        showAlert('danger', 'Erreur lors de la recherche des coordonnées. Veuillez les saisir manuellement.');
                    })
                    .finally(() => {
                        // Réactiver le bouton
                        getCoordinatesBtn.disabled = false;
                        getCoordinatesBtn.innerHTML = '<i class="fas fa-map-marked-alt me-1"></i>Obtenir les coordonnées automatiquement';
                    });
            });

            // Fonction pour afficher des alertes
            function showAlert(type, message) {
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
                alertDiv.innerHTML = `
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'exclamation-circle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                // Insérer l'alerte au début du contenu
                const container = document.querySelector('.container-fluid');
                container.insertBefore(alertDiv, container.firstChild);

                // Supprimer automatiquement après 5 secondes
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }

            // Auto-complétion pour les villes françaises (optionnel)
            const villesCommunes = [
                'Paris', 'Lyon', 'Marseille', 'Toulouse', 'Nice', 'Nantes', 'Strasbourg',
                'Montpellier', 'Bordeaux', 'Lille', 'Rennes', 'Reims', 'Le Havre',
                'Saint-Étienne', 'Toulon', 'Grenoble', 'Dijon', 'Angers', 'Nîmes', 'Villeurbanne'
            ];

            const villeInput = document.getElementById('ville');
            villeInput.addEventListener('input', function() {
                const value = this.value.toLowerCase();
                if (value.length >= 2) {
                    const suggestions = villesCommunes.filter(ville =>
                        ville.toLowerCase().includes(value)
                    );

                    // Vous pouvez implémenter une liste déroulante ici si souhaité
                }
            });
        });
    </script>
</body>
</html>
