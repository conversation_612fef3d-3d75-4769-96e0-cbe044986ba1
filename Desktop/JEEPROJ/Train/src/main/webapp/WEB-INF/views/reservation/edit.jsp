<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modifier ma réservation - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .reservation-card {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            transition: all 0.3s ease;
        }
        .reservation-card:hover {
            border-color: #007bff;
            box-shadow: 0 4px 15px rgba(0,123,255,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/">
                <i class="fas fa-train"></i> TrainSystem
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">Bonjour ${sessionScope.user.prenom}</span>
                <a class="nav-link" href="${pageContext.request.contextPath}/reservation/">Mes réservations</a>
                <a class="nav-link" href="${pageContext.request.contextPath}/">Accueil</a>
            </div>
        </div>
    </nav>

    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- Header -->
                <div class="text-center mb-4">
                    <h2><i class="fas fa-edit text-warning"></i> Modifier ma réservation</h2>
                    <p class="text-muted">Modifiez le nombre de places de votre réservation</p>
                </div>

                <!-- Messages -->
                <c:if test="${not empty errorMessage}">
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle"></i> ${errorMessage}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </c:if>

                <!-- Informations de la réservation actuelle -->
                <div class="card reservation-card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-ticket-alt"></i> Réservation actuelle</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-route"></i> Trajet</h6>
                                <p class="mb-2">
                                    <strong>${reservation.voyage.trajet.gareDepart.ville}</strong>
                                    <i class="fas fa-arrow-right mx-2"></i>
                                    <strong>${reservation.voyage.trajet.gareArrivee.ville}</strong>
                                </p>
                                <p class="text-muted mb-3">
                                    <i class="fas fa-calendar"></i> 
                                    <fmt:formatDate value="${reservation.voyage.dateVoyage}" pattern="EEEE dd MMMM yyyy" />
                                    <br>
                                    <i class="fas fa-clock"></i> 
                                    ${reservation.voyage.trajet.heureDepart} → ${reservation.voyage.trajet.heureArrivee}
                                </p>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-info-circle"></i> Détails actuels</h6>
                                <p class="mb-1">
                                    <strong>Numéro :</strong> ${reservation.numeroReservation}
                                </p>
                                <p class="mb-1">
                                    <strong>Places actuelles :</strong> ${reservation.nombrePlaces}
                                </p>
                                <p class="mb-1">
                                    <strong>Prix actuel :</strong> 
                                    <fmt:formatNumber value="${reservation.prixTotal}" type="currency" currencySymbol="€" />
                                </p>
                                <p class="mb-0">
                                    <span class="badge bg-${reservation.statut == 'CONFIRMEE' ? 'success' : 'warning'}">
                                        ${reservation.statut}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Formulaire de modification -->
                <div class="card reservation-card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-edit"></i> Modifier le nombre de places</h5>
                    </div>
                    <div class="card-body">
                        <form action="${pageContext.request.contextPath}/reservation/edit/${reservation.id}" method="post">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="nombrePlaces" class="form-label">
                                        <i class="fas fa-users"></i> Nouveau nombre de places
                                    </label>
                                    <select class="form-select" id="nombrePlaces" name="nombrePlaces" required>
                                        <option value="">Choisissez le nombre de places</option>
                                        <c:forEach var="i" begin="1" end="${placesDisponibles + reservation.nombrePlaces > 10 ? 10 : placesDisponibles + reservation.nombrePlaces}">
                                            <option value="${i}" ${i == reservation.nombrePlaces ? 'selected' : ''}>
                                                ${i} place(s)
                                            </option>
                                        </c:forEach>
                                    </select>
                                    <div class="form-text">
                                        Places disponibles : ${placesDisponibles + reservation.nombrePlaces}
                                        (incluant vos places actuelles)
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-calculator"></i> Nouveau prix total
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">€</span>
                                        <input type="text" class="form-control" id="prixTotal" readonly 
                                               value="<fmt:formatNumber value='${reservation.prixTotal}' pattern='#,##0.00' />">
                                    </div>
                                    <div class="form-text">
                                        Prix par place : <fmt:formatNumber value="${reservation.voyage.trajet.prix}" type="currency" currencySymbol="€" />
                                    </div>
                                </div>
                            </div>

                            <!-- Résumé des changements -->
                            <div id="changementsSummary" class="alert alert-info d-none">
                                <h6><i class="fas fa-info-circle"></i> Résumé des modifications</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <strong>Places :</strong><br>
                                        <span id="placesChange">${reservation.nombrePlaces} → -</span>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>Prix :</strong><br>
                                        <span id="prixChange">
                                            <fmt:formatNumber value="${reservation.prixTotal}" type="currency" currencySymbol="€" /> → -
                                        </span>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>Différence :</strong><br>
                                        <span id="differenceChange" class="fw-bold">-</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Motif de modification -->
                            <div class="mb-3">
                                <label for="motifModification" class="form-label">
                                    <i class="fas fa-comment"></i> Motif de la modification (optionnel)
                                </label>
                                <textarea class="form-control" id="motifModification" name="motifModification" 
                                          rows="3" placeholder="Expliquez pourquoi vous modifiez votre réservation..."></textarea>
                            </div>

                            <!-- Boutons d'action -->
                            <div class="d-grid gap-2 d-md-flex justify-content-md-between">
                                <a href="${pageContext.request.contextPath}/reservation/" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left"></i> Retour à mes réservations
                                </a>
                                <button type="submit" class="btn btn-warning" id="submitBtn" disabled>
                                    <i class="fas fa-save"></i> Confirmer la modification
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Informations importantes -->
                <div class="alert alert-warning mt-4">
                    <h6><i class="fas fa-exclamation-triangle"></i> Informations importantes</h6>
                    <ul class="mb-0">
                        <li>Vous pouvez augmenter ou diminuer le nombre de places selon la disponibilité</li>
                        <li>Si vous diminuez le nombre de places, la différence vous sera remboursée</li>
                        <li>Si vous augmentez le nombre de places, vous devrez payer la différence</li>
                        <li>Les modifications sont soumises à validation administrative</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const nombrePlacesSelect = document.getElementById('nombrePlaces');
        const prixTotalInput = document.getElementById('prixTotal');
        const submitBtn = document.getElementById('submitBtn');
        const changementsSummary = document.getElementById('changementsSummary');
        
        const prixUnitaire = ${reservation.voyage.trajet.prix};
        const placesActuelles = ${reservation.nombrePlaces};
        const prixActuel = ${reservation.prixTotal};
        
        function updateCalculations() {
            const nouvellePlaces = parseInt(nombrePlacesSelect.value) || 0;
            const nouveauPrix = nouvellePlaces * prixUnitaire;
            const difference = nouveauPrix - prixActuel;
            
            // Mettre à jour le prix total
            prixTotalInput.value = nouveauPrix.toLocaleString('fr-FR', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
            
            // Afficher le résumé des changements
            if (nouvellePlaces > 0 && nouvellePlaces !== placesActuelles) {
                document.getElementById('placesChange').textContent = placesActuelles + ' → ' + nouvellePlaces;
                document.getElementById('prixChange').textContent = 
                    prixActuel.toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' }) + ' → ' +
                    nouveauPrix.toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' });
                
                const differenceText = (difference >= 0 ? '+' : '') + 
                    difference.toLocaleString('fr-FR', { style: 'currency', currency: 'EUR' });
                document.getElementById('differenceChange').textContent = differenceText;
                document.getElementById('differenceChange').className = 'fw-bold ' + 
                    (difference >= 0 ? 'text-danger' : 'text-success');
                
                changementsSummary.classList.remove('d-none');
                submitBtn.disabled = false;
            } else {
                changementsSummary.classList.add('d-none');
                submitBtn.disabled = true;
            }
        }
        
        nombrePlacesSelect.addEventListener('change', updateCalculations);
        
        // Initialisation
        updateCalculations();
    </script>
</body>
</html>
