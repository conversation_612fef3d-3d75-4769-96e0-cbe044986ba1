<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formulaire Trajet - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .admin-sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .admin-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="admin-sidebar">
                    <div class="p-3">
                        <h5 class="text-white mb-4">
                            <i class="fas fa-train me-2"></i>
                            TrainSystem Admin
                        </h5>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin">
                            <i class="fas fa-tachometer-alt me-2"></i> Tableau de bord
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/gares">
                            <i class="fas fa-building me-2"></i> Gares
                        </a>
                        <a class="nav-link text-white active" href="${pageContext.request.contextPath}/admin/trajets">
                            <i class="fas fa-route me-2"></i> Trajets
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/voyages">
                            <i class="fas fa-train me-2"></i> Voyages
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/users">
                            <i class="fas fa-users me-2"></i> Utilisateurs
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/reservations">
                            <i class="fas fa-ticket-alt me-2"></i> Réservations
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/payments">
                            <i class="fas fa-credit-card me-2"></i> Paiements
                        </a>
                        <hr class="text-white">
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/logout">
                            <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main content -->
            <div class="col-md-9 col-lg-10">
                <div class="admin-content p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h1 class="h3 mb-0">
                                <c:choose>
                                    <c:when test="${not empty trajet && not empty trajet.id}">Modifier le trajet</c:when>
                                    <c:otherwise>Nouveau trajet</c:otherwise>
                                </c:choose>
                            </h1>
                            <p class="text-muted">Créez ou modifiez un trajet entre deux gares</p>
                        </div>
                        <div>
                            <a href="${pageContext.request.contextPath}/admin/trajets" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-1"></i> Retour à la liste
                            </a>
                        </div>
                    </div>
                    
                    <!-- Messages d'alerte -->
                    <c:if test="${not empty error}">
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            ${error}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    </c:if>

                    <c:if test="${not empty sessionScope.success}">
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            ${sessionScope.success}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <c:remove var="success" scope="session"/>
                    </c:if>

                    <!-- Content -->
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-route me-2"></i>
                                <c:choose>
                                    <c:when test="${not empty trajet && not empty trajet.id}">Modifier le trajet</c:when>
                                    <c:otherwise>Nouveau trajet</c:otherwise>
                                </c:choose>
                            </h6>
                        </div>
                        <div class="card-body">
                            <form method="post" action="${pageContext.request.contextPath}/admin/trajets/save" id="trajetForm">
                                <!-- ID caché pour la modification -->
                                <c:if test="${not empty trajet && not empty trajet.id}">
                                    <input type="hidden" name="id" value="${trajet.id}">
                                </c:if>

                                <div class="row">
                                    <div class="col-md-6">
                                        <!-- Gare de départ -->
                                        <div class="mb-3">
                                            <label for="gareDepartId" class="form-label">
                                                <i class="fas fa-map-marker-alt me-1"></i>Gare de départ *
                                            </label>
                                            <select class="form-select" id="gareDepartId" name="gareDepartId" required>
                                                <option value="">Sélectionnez une gare de départ</option>
                                                <c:forEach var="gare" items="${gares}">
                                                    <option value="${gare.id}" 
                                                            ${trajet.gareDepart.id == gare.id ? 'selected' : ''}>
                                                        ${gare.ville} - ${gare.nom} (${gare.codeGare})
                                                    </option>
                                                </c:forEach>
                                            </select>
                                        </div>

                                        <!-- Gare d'arrivée -->
                                        <div class="mb-3">
                                            <label for="gareArriveeId" class="form-label">
                                                <i class="fas fa-flag-checkered me-1"></i>Gare d'arrivée *
                                            </label>
                                            <select class="form-select" id="gareArriveeId" name="gareArriveeId" required>
                                                <option value="">Sélectionnez une gare d'arrivée</option>
                                                <c:forEach var="gare" items="${gares}">
                                                    <option value="${gare.id}" 
                                                            ${trajet.gareArrivee.id == gare.id ? 'selected' : ''}>
                                                        ${gare.ville} - ${gare.nom} (${gare.codeGare})
                                                    </option>
                                                </c:forEach>
                                            </select>
                                        </div>

                                        <!-- Heure de départ -->
                                        <div class="mb-3">
                                            <label for="heureDepart" class="form-label">
                                                <i class="fas fa-clock me-1"></i>Heure de départ *
                                            </label>
                                            <input type="time" class="form-control" id="heureDepart" name="heureDepart" 
                                                   value="${trajet.heureDepart}" required>
                                        </div>

                                        <!-- Heure d'arrivée -->
                                        <div class="mb-3">
                                            <label for="heureArrivee" class="form-label">
                                                <i class="fas fa-clock me-1"></i>Heure d'arrivée *
                                            </label>
                                            <input type="time" class="form-control" id="heureArrivee" name="heureArrivee" 
                                                   value="${trajet.heureArrivee}" required>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <!-- Prix -->
                                        <div class="mb-3">
                                            <label for="prix" class="form-label">
                                                <i class="fas fa-euro-sign me-1"></i>Prix (€) *
                                            </label>
                                            <input type="number" class="form-control" id="prix" name="prix" 
                                                   value="${trajet.prix}" required min="0" step="0.01" max="1000"
                                                   placeholder="Ex: 25.50">
                                        </div>

                                        <!-- Nombre de places -->
                                        <div class="mb-3">
                                            <label for="nombrePlaces" class="form-label">
                                                <i class="fas fa-users me-1"></i>Nombre de places *
                                            </label>
                                            <input type="number" class="form-control" id="nombrePlaces" name="nombrePlaces" 
                                                   value="${trajet.nombrePlaces}" required min="1" max="1000"
                                                   placeholder="Ex: 200">
                                        </div>

                                        <!-- Type de train -->
                                        <div class="mb-3">
                                            <label for="typeTrain" class="form-label">
                                                <i class="fas fa-train me-1"></i>Type de train *
                                            </label>
                                            <select class="form-select" id="typeTrain" name="typeTrain" required>
                                                <option value="TER" ${trajet.typeTrain == 'TER' ? 'selected' : ''}>TER</option>
                                                <option value="INTERCITES" ${trajet.typeTrain == 'INTERCITES' ? 'selected' : ''}>INTERCITÉS</option>
                                                <option value="TGV" ${trajet.typeTrain == 'TGV' ? 'selected' : ''}>TGV</option>
                                                <option value="OUIGO" ${trajet.typeTrain == 'OUIGO' ? 'selected' : ''}>OUIGO</option>
                                            </select>
                                        </div>

                                        <!-- Statut -->
                                        <div class="mb-3">
                                            <label for="actif" class="form-label">
                                                <i class="fas fa-toggle-on me-1"></i>Statut
                                            </label>
                                            <select class="form-select" id="actif" name="actif">
                                                <option value="true" ${trajet.actif ? 'selected' : ''}>Actif</option>
                                                <option value="false" ${!trajet.actif ? 'selected' : ''}>Inactif</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Boutons d'action -->
                                <hr class="my-4">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <a href="${pageContext.request.contextPath}/admin/trajets" class="btn btn-outline-secondary">
                                            <i class="fas fa-arrow-left me-1"></i>Retour
                                        </a>
                                    </div>
                                    <div>
                                        <button type="reset" class="btn btn-outline-warning me-2">
                                            <i class="fas fa-undo me-1"></i>Réinitialiser
                                        </button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>
                                            <c:choose>
                                                <c:when test="${not empty trajet && not empty trajet.id}">Modifier</c:when>
                                                <c:otherwise>Créer</c:otherwise>
                                            </c:choose>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('trajetForm');
            const gareDepartSelect = document.getElementById('gareDepartId');
            const gareArriveeSelect = document.getElementById('gareArriveeId');
            const heureDepartInput = document.getElementById('heureDepart');
            const heureArriveeInput = document.getElementById('heureArrivee');

            // Empêcher de sélectionner la même gare pour départ et arrivée
            function validateGares() {
                if (gareDepartSelect.value && gareArriveeSelect.value && 
                    gareDepartSelect.value === gareArriveeSelect.value) {
                    alert('La gare de départ et d\'arrivée ne peuvent pas être identiques');
                    return false;
                }
                return true;
            }

            gareDepartSelect.addEventListener('change', validateGares);
            gareArriveeSelect.addEventListener('change', validateGares);

            // Validation du formulaire
            form.addEventListener('submit', function(e) {
                if (!validateGares()) {
                    e.preventDefault();
                    return false;
                }

                // Vérifier que l'heure d'arrivée est après l'heure de départ
                if (heureDepartInput.value && heureArriveeInput.value) {
                    const heureDepart = new Date('2000-01-01 ' + heureDepartInput.value);
                    const heureArrivee = new Date('2000-01-01 ' + heureArriveeInput.value);
                    
                    if (heureArrivee <= heureDepart) {
                        alert('L\'heure d\'arrivée doit être postérieure à l\'heure de départ');
                        e.preventDefault();
                        return false;
                    }
                }

                return true;
            });
        });
    </script>
</body>
</html>
