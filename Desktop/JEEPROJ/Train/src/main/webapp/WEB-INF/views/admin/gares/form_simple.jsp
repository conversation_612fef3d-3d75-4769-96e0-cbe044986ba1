<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formulaire Gare - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-building me-2"></i>
                            <c:choose>
                                <c:when test="${not empty gare && not empty gare.id}">Modifier la gare</c:when>
                                <c:otherwise>Nouvelle gare</c:otherwise>
                            </c:choose>
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Messages d'alerte -->
                        <c:if test="${not empty sessionScope.error}">
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                ${sessionScope.error}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        </c:if>

                        <c:if test="${not empty sessionScope.success}">
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                ${sessionScope.success}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        </c:if>

                        <form method="post" action="${pageContext.request.contextPath}/admin/gares/save" id="gareForm">
                            <!-- ID caché pour la modification -->
                            <c:if test="${not empty gare && not empty gare.id}">
                                <input type="hidden" name="id" value="${gare.id}">
                            </c:if>

                            <div class="row">
                                <div class="col-md-6">
                                    <!-- Nom de la gare -->
                                    <div class="mb-3">
                                        <label for="nom" class="form-label">
                                            <i class="fas fa-building me-1"></i>Nom de la gare *
                                        </label>
                                        <input type="text" class="form-control" id="nom" name="nom" 
                                               value="${gare.nom}" required maxlength="100"
                                               placeholder="Ex: Gare de Paris-Montparnasse">
                                    </div>

                                    <!-- Ville -->
                                    <div class="mb-3">
                                        <label for="ville" class="form-label">
                                            <i class="fas fa-city me-1"></i>Ville *
                                        </label>
                                        <input type="text" class="form-control" id="ville" name="ville" 
                                               value="${gare.ville}" required maxlength="100"
                                               placeholder="Ex: Paris">
                                    </div>

                                    <!-- Code gare -->
                                    <div class="mb-3">
                                        <label for="codeGare" class="form-label">
                                            <i class="fas fa-tag me-1"></i>Code de la gare *
                                        </label>
                                        <input type="text" class="form-control" id="codeGare" name="codeGare" 
                                               value="${gare.codeGare}" required maxlength="10"
                                               placeholder="Ex: PAR01" style="text-transform: uppercase;">
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <!-- Adresse -->
                                    <div class="mb-3">
                                        <label for="adresse" class="form-label">
                                            <i class="fas fa-map-pin me-1"></i>Adresse
                                        </label>
                                        <textarea class="form-control" id="adresse" name="adresse" rows="3" 
                                                  maxlength="255" placeholder="Ex: 17 Boulevard de Vaugirard">${gare.adresse}</textarea>
                                    </div>

                                    <!-- Code postal -->
                                    <div class="mb-3">
                                        <label for="codePostal" class="form-label">
                                            <i class="fas fa-mail-bulk me-1"></i>Code postal
                                        </label>
                                        <input type="text" class="form-control" id="codePostal" name="codePostal" 
                                               value="${gare.codePostal}" maxlength="10"
                                               placeholder="Ex: 75015">
                                    </div>

                                    <!-- Statut -->
                                    <div class="mb-3">
                                        <label for="active" class="form-label">
                                            <i class="fas fa-toggle-on me-1"></i>Statut
                                        </label>
                                        <select class="form-select" id="active" name="active">
                                            <option value="true" ${gare.active ? 'selected' : ''}>Active</option>
                                            <option value="false" ${!gare.active ? 'selected' : ''}>Inactive</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Coordonnées GPS -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="latitude" class="form-label">
                                            <i class="fas fa-globe me-1"></i>Latitude
                                        </label>
                                        <input type="number" class="form-control" id="latitude" name="latitude" 
                                               value="${gare.latitude}" step="0.000001" min="-90" max="90"
                                               placeholder="Ex: 48.8404">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="longitude" class="form-label">
                                            <i class="fas fa-globe me-1"></i>Longitude
                                        </label>
                                        <input type="number" class="form-control" id="longitude" name="longitude" 
                                               value="${gare.longitude}" step="0.000001" min="-180" max="180"
                                               placeholder="Ex: 2.3188">
                                    </div>
                                </div>
                            </div>

                            <!-- Boutons d'action -->
                            <hr class="my-4">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <a href="${pageContext.request.contextPath}/admin/gares" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>Retour
                                    </a>
                                </div>
                                <div>
                                    <button type="reset" class="btn btn-outline-warning me-2">
                                        <i class="fas fa-undo me-1"></i>Réinitialiser
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        <c:choose>
                                            <c:when test="${not empty gare && not empty gare.id}">Modifier</c:when>
                                            <c:otherwise>Créer</c:otherwise>
                                        </c:choose>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('gareForm');
            const codeGareInput = document.getElementById('codeGare');

            // Convertir le code gare en majuscules
            codeGareInput.addEventListener('input', function() {
                this.value = this.value.toUpperCase();
            });

            // Validation du formulaire
            form.addEventListener('submit', function(e) {
                const nom = document.getElementById('nom').value.trim();
                const ville = document.getElementById('ville').value.trim();
                const codeGare = document.getElementById('codeGare').value.trim();

                if (!nom || !ville || !codeGare) {
                    e.preventDefault();
                    alert('Veuillez remplir tous les champs obligatoires (*)');
                    return false;
                }

                if (codeGare.length < 3) {
                    e.preventDefault();
                    alert('Le code gare doit contenir au moins 3 caractères');
                    return false;
                }

                return true;
            });
        });
    </script>
</body>
</html>
