<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Gares - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .admin-sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .admin-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 admin-sidebar p-0">
                <div class="d-flex flex-column p-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-cogs"></i> Administration
                        </h4>
                        <small class="text-light">Bonjour ${sessionScope.user.prenom}</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin">
                            <i class="fas fa-tachometer-alt me-2"></i> Tableau de bord
                        </a>
                        <a class="nav-link text-white active" href="${pageContext.request.contextPath}/admin/gares">
                            <i class="fas fa-building me-2"></i> Gares
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/trajets">
                            <i class="fas fa-route me-2"></i> Trajets
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/voyages">
                            <i class="fas fa-train me-2"></i> Voyages
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/users">
                            <i class="fas fa-users me-2"></i> Utilisateurs
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/reservations">
                            <i class="fas fa-ticket-alt me-2"></i> Réservations
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/payments">
                            <i class="fas fa-credit-card me-2"></i> Paiements
                        </a>
                        
                        <hr class="text-white">
                        
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/">
                            <i class="fas fa-home me-2"></i> Retour au site
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/logout">
                            <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 admin-content">
                <div class="container-fluid py-4">
                    
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="h3 mb-0">
                            <i class="fas fa-building me-2"></i>
                            Gestion des Gares
                        </h1>
                        <div>
                            <a href="${pageContext.request.contextPath}/admin/gares/new" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i> Nouvelle gare
                            </a>
                            <button class="btn btn-outline-secondary" onclick="window.location.reload()">
                                <i class="fas fa-sync-alt me-1"></i> Actualiser
                            </button>
                        </div>
                    </div>
                    
                    <!-- Messages -->
                    <c:if test="${not empty sessionScope.successMessage}">
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            ${sessionScope.successMessage}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <c:remove var="successMessage" scope="session" />
                    </c:if>
                    
                    <c:if test="${not empty sessionScope.errorMessage}">
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            ${sessionScope.errorMessage}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <c:remove var="errorMessage" scope="session" />
                    </c:if>
                    
                    <c:if test="${not empty message}">
                        <div class="alert alert-info alert-dismissible fade show" role="alert">
                            <i class="fas fa-info-circle me-2"></i>
                            ${message}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    </c:if>
                    
                    <!-- Messages d'alerte -->
                    <c:if test="${not empty sessionScope.error}">
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            ${sessionScope.error}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <c:remove var="error" scope="session"/>
                    </c:if>

                    <c:if test="${not empty sessionScope.success}">
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            ${sessionScope.success}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <c:remove var="success" scope="session"/>
                    </c:if>

                    <!-- Statistiques -->
                    <c:if test="${not empty gares}">
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h6 class="card-title">Total Gares</h6>
                                                <h3 class="mb-0">${gares.size()}</h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-building fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h6 class="card-title">Gares Actives</h6>
                                                <h3 class="mb-0">${nbGaresActives}</h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-check-circle fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h6 class="card-title">Gares Inactives</h6>
                                                <h3 class="mb-0">${nbGaresInactives}</h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-times-circle fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </c:if>

                    <!-- Content -->
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Liste des Gares</h6>
                        </div>
                        <div class="card-body">
                            <c:choose>
                                <c:when test="${not empty gares}">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-hover">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>Code</th>
                                                    <th>Nom</th>
                                                    <th>Ville</th>
                                                    <th>Adresse</th>
                                                    <th>Coordonnées GPS</th>
                                                    <th>Statut</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <c:forEach var="gare" items="${gares}">
                                                    <tr>
                                                        <td><strong>${gare.codeGare}</strong></td>
                                                        <td>${gare.nom}</td>
                                                        <td>${gare.ville}</td>
                                                        <td>
                                                            <c:choose>
                                                                <c:when test="${not empty gare.adresse}">
                                                                    ${gare.adresse}
                                                                    <c:if test="${not empty gare.codePostal}">
                                                                        <br><small class="text-muted">${gare.codePostal}</small>
                                                                    </c:if>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <span class="text-muted">Non renseignée</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </td>
                                                        <td>
                                                            <c:choose>
                                                                <c:when test="${not empty gare.latitude and not empty gare.longitude}">
                                                                    <small>
                                                                        Lat: <fmt:formatNumber value="${gare.latitude}" maxFractionDigits="4"/><br>
                                                                        Lng: <fmt:formatNumber value="${gare.longitude}" maxFractionDigits="4"/>
                                                                    </small>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <span class="text-muted">Non renseignées</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </td>
                                                        <td>
                                                            <c:choose>
                                                                <c:when test="${gare.active}">
                                                                    <span class="badge bg-success">Active</span>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <span class="badge bg-danger">Inactive</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group" role="group">
                                                                <a href="${pageContext.request.contextPath}/admin/gares/edit/${gare.id}"
                                                                   class="btn btn-sm btn-outline-primary" title="Modifier">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                                <form method="post" action="${pageContext.request.contextPath}/admin/gares/toggle/${gare.id}"
                                                                      style="display: inline;" onsubmit="return confirm('Changer le statut de cette gare ?')">
                                                                    <button type="submit" class="btn btn-sm btn-outline-warning"
                                                                            title="${gare.active ? 'Désactiver' : 'Activer'}">
                                                                        <i class="fas fa-${gare.active ? 'eye-slash' : 'eye'}"></i>
                                                                    </button>
                                                                </form>
                                                                <form method="post" action="${pageContext.request.contextPath}/admin/gares/delete/${gare.id}"
                                                                      style="display: inline;" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cette gare ?')">
                                                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Supprimer">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                </form>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </c:forEach>
                                            </tbody>
                                        </table>
                                    </div>
                                </c:when>
                                <c:otherwise>
                                    <div class="text-center py-5">
                                        <i class="fas fa-building fa-4x text-muted mb-4"></i>
                                        <h4 class="text-muted">Aucune gare trouvée</h4>
                                        <p class="text-muted mb-4">
                                            Commencez par créer votre première gare.
                                        </p>
                                        <a href="${pageContext.request.contextPath}/admin/gares/new" class="btn btn-primary">
                                            <i class="fas fa-plus me-1"></i> Créer une gare
                                        </a>
                                    </div>
                                </c:otherwise>
                            </c:choose>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
