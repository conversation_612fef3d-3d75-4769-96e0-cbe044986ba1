<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Affectations - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .admin-sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .admin-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .affectation-card {
            border-left: 4px solid #007bff;
            transition: transform 0.2s;
        }
        .affectation-card:hover {
            transform: translateY(-2px);
        }
        .role-conducteur {
            border-left-color: #dc3545;
        }
        .role-controleur {
            border-left-color: #28a745;
        }
        .role-chef {
            border-left-color: #ffc107;
        }
        .role-commercial {
            border-left-color: #17a2b8;
        }
        .role-technicien {
            border-left-color: #6f42c1;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 admin-sidebar p-0">
                <div class="d-flex flex-column p-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-cogs"></i> Administration
                        </h4>
                        <small class="text-light">Bonjour ${sessionScope.user.prenom}</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin">
                            <i class="fas fa-tachometer-alt me-2"></i> Tableau de bord
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/gares">
                            <i class="fas fa-building me-2"></i> Gares
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/trajets">
                            <i class="fas fa-route me-2"></i> Trajets
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/voyages">
                            <i class="fas fa-train me-2"></i> Voyages
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/users">
                            <i class="fas fa-users me-2"></i> Utilisateurs
                        </a>
                        <a class="nav-link text-white active" href="${pageContext.request.contextPath}/admin/employees">
                            <i class="fas fa-user-tie me-2"></i> Employés
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/reservations">
                            <i class="fas fa-ticket-alt me-2"></i> Réservations
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/payments">
                            <i class="fas fa-credit-card me-2"></i> Paiements
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/cancellations">
                            <i class="fas fa-undo me-2"></i> Annulations
                        </a>
                        
                        <hr class="text-white">
                        
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/">
                            <i class="fas fa-home me-2"></i> Retour au site
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/logout">
                            <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 admin-content">
                <div class="container-fluid py-4">
                    
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="h3 mb-0">
                            <i class="fas fa-user-tie me-2"></i>
                            Gestion des Affectations d'Employés
                        </h1>
                        <div>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#nouvelleAffectationModal">
                                <i class="fas fa-plus me-1"></i> Nouvelle Affectation
                            </button>
                            <button class="btn btn-outline-secondary" onclick="window.location.reload()">
                                <i class="fas fa-sync-alt me-1"></i> Actualiser
                            </button>
                        </div>
                    </div>
                    
                    <!-- Messages -->
                    <c:if test="${not empty sessionScope.successMessage}">
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            ${sessionScope.successMessage}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <c:remove var="successMessage" scope="session" />
                    </c:if>
                    
                    <!-- Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center border-primary">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">${statistiques.totalAffectations}</h5>
                                    <p class="card-text">Total affectations</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center border-success">
                                <div class="card-body">
                                    <h5 class="card-title text-success">${statistiques.voyagesCompletementAffecter}</h5>
                                    <p class="card-text">Voyages complets</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center border-warning">
                                <div class="card-body">
                                    <h5 class="card-title text-warning">${statistiques.voyagesPartiellementAffecter}</h5>
                                    <p class="card-text">Voyages partiels</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center border-info">
                                <div class="card-body">
                                    <h5 class="card-title text-info">${statistiques.employesAffecter}</h5>
                                    <p class="card-text">Employés affectés</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Filters -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <form method="get" class="row g-3">
                                <div class="col-md-3">
                                    <label for="voyage" class="form-label">Voyage</label>
                                    <select class="form-select" id="voyage" name="voyage">
                                        <option value="">Tous les voyages</option>
                                        <c:forEach var="voyage" items="${voyages}">
                                            <option value="${voyage.id}">
                                                ${voyage.trajet.gareDepart.nom} → ${voyage.trajet.gareArrivee.nom}
                                                (${voyage.dateVoyage})
                                            </option>
                                        </c:forEach>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="employe" class="form-label">Employé</label>
                                    <select class="form-select" id="employe" name="employe">
                                        <option value="">Tous les employés</option>
                                        <c:forEach var="employe" items="${employes}">
                                            <option value="${employe.id}">
                                                ${employe.prenom} ${employe.nom}
                                            </option>
                                        </c:forEach>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="role" class="form-label">Rôle</label>
                                    <select class="form-select" id="role" name="role">
                                        <option value="">Tous les rôles</option>
                                        <option value="CONDUCTEUR">Conducteur</option>
                                        <option value="CONTROLEUR">Contrôleur</option>
                                        <option value="CHEF_DE_BORD">Chef de bord</option>
                                        <option value="AGENT_COMMERCIAL">Agent commercial</option>
                                        <option value="TECHNICIEN">Technicien</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="statut" class="form-label">Statut</label>
                                    <div class="input-group">
                                        <select class="form-select" id="statut" name="statut">
                                            <option value="">Tous</option>
                                            <option value="actif">Actif</option>
                                            <option value="inactif">Inactif</option>
                                        </select>
                                        <button class="btn btn-outline-secondary" type="submit">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Affectations List -->
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Liste des affectations</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <c:forEach var="affectation" items="${affectations}" varStatus="status">
                                    <div class="col-lg-6 mb-4">
                                        <div class="card affectation-card 
                                            <c:choose>
                                                <c:when test="${affectation.role == 'CONDUCTEUR'}">role-conducteur</c:when>
                                                <c:when test="${affectation.role == 'CONTROLEUR'}">role-controleur</c:when>
                                                <c:when test="${affectation.role == 'CHEF_DE_BORD'}">role-chef</c:when>
                                                <c:when test="${affectation.role == 'AGENT_COMMERCIAL'}">role-commercial</c:when>
                                                <c:when test="${affectation.role == 'TECHNICIEN'}">role-technicien</c:when>
                                            </c:choose>">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">Affectation #${affectation.id}</h6>
                                                <span class="badge ${affectation.actif ? 'bg-success' : 'bg-secondary'}">
                                                    ${affectation.actif ? 'Actif' : 'Inactif'}
                                                </span>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <p class="mb-1"><strong>Employé:</strong> ${affectation.nomCompletEmploye}</p>
                                                        <p class="mb-1"><strong>Rôle:</strong> 
                                                            <span class="badge bg-primary">${affectation.libelleRole}</span>
                                                        </p>
                                                        <p class="mb-1"><strong>Voyage:</strong> ${affectation.infoVoyage}</p>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <p class="mb-1"><strong>Date affectation:</strong> 
                                                            ${affectation.dateAffectation.toLocalDate()}
                                                        </p>
                                                        <c:if test="${affectation.dateModification != null}">
                                                            <p class="mb-1"><strong>Modifié le:</strong> 
                                                                ${affectation.dateModification.toLocalDate()}
                                                            </p>
                                                        </c:if>
                                                    </div>
                                                </div>
                                                <c:if test="${not empty affectation.commentaire}">
                                                    <div class="mt-3">
                                                        <p class="mb-2"><strong>Commentaire:</strong></p>
                                                        <p class="text-muted">${affectation.commentaire}</p>
                                                    </div>
                                                </c:if>
                                                <div class="mt-3">
                                                    <c:if test="${affectation.actif}">
                                                        <button class="btn btn-warning btn-sm me-2" onclick="desactiverAffectation('${affectation.id}')">
                                                            <i class="fas fa-pause me-1"></i> Désactiver
                                                        </button>
                                                    </c:if>
                                                    <button class="btn btn-danger btn-sm me-2" onclick="supprimerAffectation('${affectation.id}')">
                                                        <i class="fas fa-trash me-1"></i> Supprimer
                                                    </button>
                                                    <button class="btn btn-outline-info btn-sm" onclick="voirDetails('${affectation.id}')">
                                                        <i class="fas fa-eye me-1"></i> Détails
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </c:forEach>
                                
                                <c:if test="${empty affectations}">
                                    <div class="col-12">
                                        <div class="text-center py-5">
                                            <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">Aucune affectation</h5>
                                            <p class="text-muted">Il n'y a actuellement aucune affectation d'employé.</p>
                                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#nouvelleAffectationModal">
                                                <i class="fas fa-plus me-1"></i> Créer une affectation
                                            </button>
                                        </div>
                                    </div>
                                </c:if>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal Nouvelle Affectation -->
    <div class="modal fade" id="nouvelleAffectationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Nouvelle Affectation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="post" action="${pageContext.request.contextPath}/admin/employees/affecter">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="modalVoyage" class="form-label">Voyage *</label>
                                    <select class="form-select" id="modalVoyage" name="voyageId" required>
                                        <option value="">Sélectionner un voyage</option>
                                        <c:forEach var="voyage" items="${voyages}">
                                            <option value="${voyage.id}">
                                                ${voyage.trajet.gareDepart.nom} → ${voyage.trajet.gareArrivee.nom}
                                                (${voyage.dateVoyage} ${voyage.heureDepart})
                                            </option>
                                        </c:forEach>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="modalEmploye" class="form-label">Employé *</label>
                                    <select class="form-select" id="modalEmploye" name="employeId" required>
                                        <option value="">Sélectionner un employé</option>
                                        <c:forEach var="employe" items="${employes}">
                                            <option value="${employe.id}">
                                                ${employe.prenom} ${employe.nom} - ${employe.email}
                                            </option>
                                        </c:forEach>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="modalRole" class="form-label">Rôle *</label>
                                    <select class="form-select" id="modalRole" name="role" required>
                                        <option value="">Sélectionner un rôle</option>
                                        <option value="CONDUCTEUR">Conducteur</option>
                                        <option value="CONTROLEUR">Contrôleur</option>
                                        <option value="CHEF_DE_BORD">Chef de bord</option>
                                        <option value="AGENT_COMMERCIAL">Agent commercial</option>
                                        <option value="TECHNICIEN">Technicien</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="modalCommentaire" class="form-label">Commentaire</label>
                            <textarea class="form-control" id="modalCommentaire" name="commentaire" rows="3" 
                                      placeholder="Commentaire optionnel sur cette affectation..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Créer l'affectation
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function desactiverAffectation(id) {
            if (confirm('Êtes-vous sûr de vouloir désactiver cette affectation ?')) {
                // TODO: Implement deactivation logic
                alert('Affectation désactivée (fonctionnalité à implémenter)');
            }
        }
        
        function supprimerAffectation(id) {
            if (confirm('Êtes-vous sûr de vouloir supprimer cette affectation ? Cette action est irréversible.')) {
                // TODO: Implement deletion logic
                alert('Affectation supprimée (fonctionnalité à implémenter)');
            }
        }
        
        function voirDetails(id) {
            // TODO: Implement view details
            alert('Affichage des détails de l\'affectation ' + id + ' (fonctionnalité à implémenter)');
        }
    </script>
</body>
</html>
