<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Annulations - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .admin-sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .admin-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .cancellation-card {
            border-left: 4px solid #ffc107;
            transition: transform 0.2s;
        }
        .cancellation-card:hover {
            transform: translateY(-2px);
        }
        .status-pending {
            border-left-color: #ffc107;
        }
        .status-approved {
            border-left-color: #28a745;
        }
        .status-rejected {
            border-left-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 admin-sidebar p-0">
                <div class="d-flex flex-column p-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-cogs"></i> Administration
                        </h4>
                        <small class="text-light">Bonjour ${sessionScope.user.prenom}</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin">
                            <i class="fas fa-tachometer-alt me-2"></i> Tableau de bord
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/gares">
                            <i class="fas fa-building me-2"></i> Gares
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/trajets">
                            <i class="fas fa-route me-2"></i> Trajets
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/voyages">
                            <i class="fas fa-train me-2"></i> Voyages
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/users">
                            <i class="fas fa-users me-2"></i> Utilisateurs
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/reservations">
                            <i class="fas fa-ticket-alt me-2"></i> Réservations
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/payments">
                            <i class="fas fa-credit-card me-2"></i> Paiements
                        </a>
                        <a class="nav-link text-white active" href="${pageContext.request.contextPath}/admin/cancellations">
                            <i class="fas fa-undo me-2"></i> Annulations
                        </a>
                        
                        <hr class="text-white">
                        
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/">
                            <i class="fas fa-home me-2"></i> Retour au site
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/logout">
                            <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 admin-content">
                <div class="container-fluid py-4">
                    
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="h3 mb-0">
                            <i class="fas fa-undo me-2"></i>
                            Gestion des Annulations et Remboursements
                        </h1>
                        <div>
                            <button class="btn btn-outline-secondary" onclick="window.location.reload()">
                                <i class="fas fa-sync-alt me-1"></i> Actualiser
                            </button>
                        </div>
                    </div>
                    
                    <!-- Messages -->
                    <c:if test="${not empty sessionScope.successMessage}">
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            ${sessionScope.successMessage}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <c:remove var="successMessage" scope="session" />
                    </c:if>
                    
                    <c:if test="${not empty sessionScope.errorMessage}">
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            ${sessionScope.errorMessage}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <c:remove var="errorMessage" scope="session" />
                    </c:if>
                    
                    <!-- Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center border-warning">
                                <div class="card-body">
                                    <h5 class="card-title text-warning">12</h5>
                                    <p class="card-text">Demandes en attente</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center border-success">
                                <div class="card-body">
                                    <h5 class="card-title text-success">8</h5>
                                    <p class="card-text">Approuvées</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center border-danger">
                                <div class="card-body">
                                    <h5 class="card-title text-danger">3</h5>
                                    <p class="card-text">Rejetées</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center border-info">
                                <div class="card-body">
                                    <h5 class="card-title text-info">€ 2,450</h5>
                                    <p class="card-text">Montant à rembourser</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Filters -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <form method="get" class="row g-3">
                                <div class="col-md-3">
                                    <label for="status" class="form-label">Statut</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="">Tous les statuts</option>
                                        <option value="PENDING">En attente</option>
                                        <option value="APPROVED">Approuvée</option>
                                        <option value="REJECTED">Rejetée</option>
                                        <option value="PROCESSED">Traitée</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="dateFrom" class="form-label">Date de début</label>
                                    <input type="date" class="form-control" id="dateFrom" name="dateFrom">
                                </div>
                                <div class="col-md-3">
                                    <label for="dateTo" class="form-label">Date de fin</label>
                                    <input type="date" class="form-control" id="dateTo" name="dateTo">
                                </div>
                                <div class="col-md-3">
                                    <label for="search" class="form-label">Rechercher</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="search" name="search" 
                                               placeholder="Client, réservation...">
                                        <button class="btn btn-outline-secondary" type="submit">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Cancellation Requests -->
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Demandes d'annulation</h6>
                        </div>
                        <div class="card-body">
                            <!-- Demo data - Replace with actual data -->
                            <div class="row">
                                <!-- Pending Request -->
                                <div class="col-lg-6 mb-4">
                                    <div class="card cancellation-card status-pending">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">Demande #CR001</h6>
                                            <span class="badge bg-warning">En attente</span>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <p class="mb-1"><strong>Client:</strong> Marie Dubois</p>
                                                    <p class="mb-1"><strong>Réservation:</strong> #RES123</p>
                                                    <p class="mb-1"><strong>Voyage:</strong> Paris → Lyon</p>
                                                    <p class="mb-1"><strong>Date voyage:</strong> 15/06/2025</p>
                                                </div>
                                                <div class="col-md-6">
                                                    <p class="mb-1"><strong>Montant payé:</strong> €89.50</p>
                                                    <p class="mb-1"><strong>Frais annulation:</strong> €15.00</p>
                                                    <p class="mb-1"><strong>Montant remboursement:</strong> €74.50</p>
                                                    <p class="mb-1"><strong>Date demande:</strong> 10/06/2025</p>
                                                </div>
                                            </div>
                                            <div class="mt-3">
                                                <p class="mb-2"><strong>Motif:</strong></p>
                                                <p class="text-muted">Changement de programme personnel</p>
                                            </div>
                                            <div class="mt-3">
                                                <button class="btn btn-success btn-sm me-2" onclick="approveCancellation('CR001')">
                                                    <i class="fas fa-check me-1"></i> Approuver
                                                </button>
                                                <button class="btn btn-danger btn-sm me-2" onclick="rejectCancellation('CR001')">
                                                    <i class="fas fa-times me-1"></i> Rejeter
                                                </button>
                                                <button class="btn btn-outline-info btn-sm" onclick="viewDetails('CR001')">
                                                    <i class="fas fa-eye me-1"></i> Détails
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Approved Request -->
                                <div class="col-lg-6 mb-4">
                                    <div class="card cancellation-card status-approved">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">Demande #CR002</h6>
                                            <span class="badge bg-success">Approuvée</span>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <p class="mb-1"><strong>Client:</strong> Jean Martin</p>
                                                    <p class="mb-1"><strong>Réservation:</strong> #RES124</p>
                                                    <p class="mb-1"><strong>Voyage:</strong> Lyon → Marseille</p>
                                                    <p class="mb-1"><strong>Date voyage:</strong> 12/06/2025</p>
                                                </div>
                                                <div class="col-md-6">
                                                    <p class="mb-1"><strong>Montant payé:</strong> €65.00</p>
                                                    <p class="mb-1"><strong>Frais annulation:</strong> €10.00</p>
                                                    <p class="mb-1"><strong>Montant remboursé:</strong> €55.00</p>
                                                    <p class="mb-1"><strong>Date traitement:</strong> 08/06/2025</p>
                                                </div>
                                            </div>
                                            <div class="mt-3">
                                                <p class="mb-2"><strong>Motif:</strong></p>
                                                <p class="text-muted">Problème de santé</p>
                                            </div>
                                            <div class="mt-3">
                                                <span class="badge bg-success me-2">
                                                    <i class="fas fa-check me-1"></i> Remboursement effectué
                                                </span>
                                                <button class="btn btn-outline-info btn-sm" onclick="viewDetails('CR002')">
                                                    <i class="fas fa-eye me-1"></i> Détails
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Pagination -->
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" tabindex="-1">Précédent</a>
                                    </li>
                                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                                    <li class="page-item">
                                        <a class="page-link" href="#">Suivant</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function approveCancellation(id) {
            if (confirm('Êtes-vous sûr de vouloir approuver cette demande d\'annulation ?')) {
                // TODO: Implement approval logic
                alert('Demande approuvée (fonctionnalité à implémenter)');
            }
        }
        
        function rejectCancellation(id) {
            if (confirm('Êtes-vous sûr de vouloir rejeter cette demande d\'annulation ?')) {
                // TODO: Implement rejection logic
                alert('Demande rejetée (fonctionnalité à implémenter)');
            }
        }
        
        function viewDetails(id) {
            // TODO: Implement view details
            alert('Affichage des détails (fonctionnalité à implémenter)');
        }
    </script>
</body>
</html>
