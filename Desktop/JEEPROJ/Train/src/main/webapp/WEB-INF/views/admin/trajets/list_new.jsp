<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Trajets - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .admin-sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .admin-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="admin-sidebar">
                    <div class="p-3">
                        <h5 class="text-white mb-4">
                            <i class="fas fa-train me-2"></i>
                            TrainSystem Admin
                        </h5>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin">
                            <i class="fas fa-tachometer-alt me-2"></i> Tableau de bord
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/gares">
                            <i class="fas fa-building me-2"></i> Gares
                        </a>
                        <a class="nav-link text-white active" href="${pageContext.request.contextPath}/admin/trajets">
                            <i class="fas fa-route me-2"></i> Trajets
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/voyages">
                            <i class="fas fa-train me-2"></i> Voyages
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/users">
                            <i class="fas fa-users me-2"></i> Utilisateurs
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/reservations">
                            <i class="fas fa-ticket-alt me-2"></i> Réservations
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/payments">
                            <i class="fas fa-credit-card me-2"></i> Paiements
                        </a>
                        <hr class="text-white">
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/logout">
                            <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main content -->
            <div class="col-md-9 col-lg-10">
                <div class="admin-content p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h1 class="h3 mb-0">Gestion des Trajets</h1>
                            <p class="text-muted">Gérez les trajets entre les gares</p>
                        </div>
                        <div>
                            <a href="${pageContext.request.contextPath}/admin/trajets/new" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i> Nouveau Trajet
                            </a>
                        </div>
                    </div>
                    
                    <!-- Messages d'alerte -->
                    <c:if test="${not empty sessionScope.error}">
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            ${sessionScope.error}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <c:remove var="error" scope="session"/>
                    </c:if>

                    <c:if test="${not empty sessionScope.success}">
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            ${sessionScope.success}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <c:remove var="success" scope="session"/>
                    </c:if>
                    
                    <!-- Statistiques -->
                    <c:if test="${not empty trajets}">
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h6 class="card-title">Total Trajets</h6>
                                                <h3 class="mb-0">${trajets.size()}</h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-route fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h6 class="card-title">Trajets Actifs</h6>
                                                <h3 class="mb-0">${nbTrajetsActifs}</h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-check-circle fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h6 class="card-title">Trajets Inactifs</h6>
                                                <h3 class="mb-0">${nbTrajetsInactifs}</h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-times-circle fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </c:if>
                    
                    <!-- Content -->
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Liste des Trajets</h6>
                        </div>
                        <div class="card-body">
                            <c:choose>
                                <c:when test="${not empty trajets}">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-hover">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>Trajet</th>
                                                    <th>Horaires</th>
                                                    <th>Durée</th>
                                                    <th>Prix</th>
                                                    <th>Places</th>
                                                    <th>Type</th>
                                                    <th>Statut</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <c:forEach var="trajet" items="${trajets}">
                                                    <tr>
                                                        <td>
                                                            <strong>${trajet.gareDepart.ville}</strong>
                                                            <i class="fas fa-arrow-right mx-2 text-muted"></i>
                                                            <strong>${trajet.gareArrivee.ville}</strong>
                                                            <br>
                                                            <small class="text-muted">
                                                                ${trajet.gareDepart.nom} → ${trajet.gareArrivee.nom}
                                                            </small>
                                                        </td>
                                                        <td>
                                                            <div class="d-flex flex-column">
                                                                <span><i class="fas fa-clock me-1"></i> ${trajet.heureDepart}</span>
                                                                <span><i class="fas fa-clock me-1"></i> ${trajet.heureArrivee}</span>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-info">${trajet.dureeFormatee}</span>
                                                        </td>
                                                        <td>
                                                            <strong>${trajet.prix}€</strong>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-secondary">${trajet.nombrePlaces}</span>
                                                        </td>
                                                        <td>
                                                            <c:choose>
                                                                <c:when test="${trajet.typeTrain == 'TGV'}">
                                                                    <span class="badge bg-danger">${trajet.typeTrain}</span>
                                                                </c:when>
                                                                <c:when test="${trajet.typeTrain == 'INTERCITES'}">
                                                                    <span class="badge bg-warning">${trajet.typeTrain}</span>
                                                                </c:when>
                                                                <c:when test="${trajet.typeTrain == 'OUIGO'}">
                                                                    <span class="badge bg-primary">${trajet.typeTrain}</span>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <span class="badge bg-success">${trajet.typeTrain}</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </td>
                                                        <td>
                                                            <c:choose>
                                                                <c:when test="${trajet.actif}">
                                                                    <span class="badge bg-success">Actif</span>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <span class="badge bg-danger">Inactif</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group" role="group">
                                                                <a href="${pageContext.request.contextPath}/admin/trajets/edit/${trajet.id}" 
                                                                   class="btn btn-sm btn-outline-primary" title="Modifier">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                                <form method="post" action="${pageContext.request.contextPath}/admin/trajets/toggle/${trajet.id}" 
                                                                      style="display: inline;" onsubmit="return confirm('Changer le statut de ce trajet ?')">
                                                                    <button type="submit" class="btn btn-sm btn-outline-warning" 
                                                                            title="${trajet.actif ? 'Désactiver' : 'Activer'}">
                                                                        <i class="fas fa-${trajet.actif ? 'eye-slash' : 'eye'}"></i>
                                                                    </button>
                                                                </form>
                                                                <form method="post" action="${pageContext.request.contextPath}/admin/trajets/delete/${trajet.id}" 
                                                                      style="display: inline;" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer ce trajet ?')">
                                                                    <button type="submit" class="btn btn-sm btn-outline-danger" title="Supprimer">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                </form>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </c:forEach>
                                            </tbody>
                                        </table>
                                    </div>
                                </c:when>
                                <c:otherwise>
                                    <div class="text-center py-5">
                                        <i class="fas fa-route fa-4x text-muted mb-4"></i>
                                        <h4 class="text-muted">Aucun trajet trouvé</h4>
                                        <p class="text-muted mb-4">
                                            Commencez par créer votre premier trajet.
                                        </p>
                                        <a href="${pageContext.request.contextPath}/admin/trajets/new" class="btn btn-primary">
                                            <i class="fas fa-plus me-1"></i> Créer un trajet
                                        </a>
                                    </div>
                                </c:otherwise>
                            </c:choose>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
