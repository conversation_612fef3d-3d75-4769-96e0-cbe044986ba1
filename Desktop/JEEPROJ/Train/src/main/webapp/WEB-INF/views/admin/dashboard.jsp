<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administration - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .admin-sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .admin-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .stat-card {
            border-left: 4px solid #007bff;
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-2px);
        }
        .stat-card.success {
            border-left-color: #28a745;
        }
        .stat-card.warning {
            border-left-color: #ffc107;
        }
        .stat-card.danger {
            border-left-color: #dc3545;
        }
        .stat-card.info {
            border-left-color: #17a2b8;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 admin-sidebar p-0">
                <div class="d-flex flex-column p-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-cogs"></i> Administration
                        </h4>
                        <small class="text-light">Bonjour ${sessionScope.user.prenom}</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link text-white active" href="${pageContext.request.contextPath}/admin">
                            <i class="fas fa-tachometer-alt me-2"></i> Tableau de bord
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/gares">
                            <i class="fas fa-building me-2"></i> Gares
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/trajets">
                            <i class="fas fa-route me-2"></i> Trajets
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/voyages">
                            <i class="fas fa-train me-2"></i> Voyages
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/users">
                            <i class="fas fa-users me-2"></i> Utilisateurs
                        </a>
                        <!-- Employés temporairement désactivé jusqu'à création des tables DB -->
                        <!-- <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/employees">
                            <i class="fas fa-user-tie me-2"></i> Employés
                        </a> -->
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/reservations">
                            <i class="fas fa-ticket-alt me-2"></i> Réservations
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/payments">
                            <i class="fas fa-credit-card me-2"></i> Paiements
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/cancellations">
                            <i class="fas fa-undo me-2"></i> Annulations
                        </a>

                        <hr class="text-white">
                        
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/">
                            <i class="fas fa-home me-2"></i> Retour au site
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/logout">
                            <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 admin-content">
                <div class="container-fluid py-4">
                    
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="h3 mb-0">Tableau de bord</h1>
                        <div class="text-muted">
                            <i class="fas fa-calendar-alt me-1"></i>
                            <fmt:formatDate value="${now}" pattern="dd/MM/yyyy HH:mm" />
                        </div>
                    </div>
                    
                    <!-- Messages -->
                    <c:if test="${not empty sessionScope.successMessage}">
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            ${sessionScope.successMessage}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <c:remove var="successMessage" scope="session" />
                    </c:if>
                    
                    <c:if test="${not empty sessionScope.errorMessage}">
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            ${sessionScope.errorMessage}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <c:remove var="errorMessage" scope="session" />
                    </c:if>
                    
                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card stat-card info shadow">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                Utilisateurs
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                ${nbUtilisateurs}
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-users fa-2x text-info"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card stat-card success shadow">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                Clients
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                ${nbClients}
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-user-friends fa-2x text-success"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card stat-card warning shadow">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                Voyages
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                ${nbVoyages}
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-train fa-2x text-warning"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card stat-card danger shadow">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                                Réservations
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                ${nbReservations}
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-ticket-alt fa-2x text-danger"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card shadow">
                                <div class="card-header">
                                    <h6 class="m-0 font-weight-bold text-primary">Actions rapides</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3 mb-2">
                                            <a href="${pageContext.request.contextPath}/admin/gares/new" class="btn btn-outline-primary btn-block">
                                                <i class="fas fa-plus me-1"></i> Nouvelle gare
                                            </a>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <a href="${pageContext.request.contextPath}/admin/trajets/new" class="btn btn-outline-success btn-block">
                                                <i class="fas fa-plus me-1"></i> Nouveau trajet
                                            </a>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <a href="${pageContext.request.contextPath}/admin/voyages/new" class="btn btn-outline-warning btn-block">
                                                <i class="fas fa-plus me-1"></i> Nouveau voyage
                                            </a>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <a href="${pageContext.request.contextPath}/admin/users" class="btn btn-outline-info btn-block">
                                                <i class="fas fa-users me-1"></i> Gérer utilisateurs
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Data -->
                    <div class="row">
                        <!-- Recent Voyages -->
                        <div class="col-lg-6 mb-4">
                            <div class="card shadow">
                                <div class="card-header">
                                    <h6 class="m-0 font-weight-bold text-primary">Voyages à venir</h6>
                                </div>
                                <div class="card-body">
                                    <c:choose>
                                        <c:when test="${not empty voyagesRecents}">
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th>Date</th>
                                                            <th>Trajet</th>
                                                            <th>Places</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <c:forEach var="voyage" items="${voyagesRecents}">
                                                            <tr>
                                                                <td>
                                                                    ${voyage.dateVoyage.dayOfMonth}/${voyage.dateVoyage.monthValue}
                                                                </td>
                                                                <td>
                                                                    <small>${voyage.trajet.libelleTrajet}</small>
                                                                </td>
                                                                <td>
                                                                    <span class="badge bg-${voyage.placesDisponibles > 0 ? 'success' : 'danger'}">
                                                                        ${voyage.placesDisponibles}/${voyage.totalPlaces}
                                                                    </span>
                                                                </td>
                                                            </tr>
                                                        </c:forEach>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </c:when>
                                        <c:otherwise>
                                            <p class="text-muted">Aucun voyage programmé</p>
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Recent Reservations -->
                        <div class="col-lg-6 mb-4">
                            <div class="card shadow">
                                <div class="card-header">
                                    <h6 class="m-0 font-weight-bold text-primary">Réservations récentes</h6>
                                </div>
                                <div class="card-body">
                                    <c:choose>
                                        <c:when test="${not empty reservationsRecentes}">
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th>Client</th>
                                                            <th>Voyage</th>
                                                            <th>Statut</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <c:forEach var="reservation" items="${reservationsRecentes}">
                                                            <tr>
                                                                <td>
                                                                    <small>${reservation.utilisateur.prenom} ${reservation.utilisateur.nom}</small>
                                                                </td>
                                                                <td>
                                                                    <small>
                                                                        ${reservation.voyage.dateVoyage.dayOfMonth}/${reservation.voyage.dateVoyage.monthValue}
                                                                    </small>
                                                                </td>
                                                                <td>
                                                                    <span class="badge bg-${reservation.statut == 'CONFIRMEE' ? 'success' : 'warning'}">
                                                                        ${reservation.statut}
                                                                    </span>
                                                                </td>
                                                            </tr>
                                                        </c:forEach>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </c:when>
                                        <c:otherwise>
                                            <p class="text-muted">Aucune réservation récente</p>
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
