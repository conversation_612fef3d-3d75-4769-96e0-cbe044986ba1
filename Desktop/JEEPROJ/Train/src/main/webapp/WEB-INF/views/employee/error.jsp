<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Erreur - Espace Employé</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
                    </div>
                    
                    <h2 class="card-title text-danger">Erreur</h2>
                    
                    <c:choose>
                        <c:when test="${not empty errorMessage}">
                            <p class="card-text">${errorMessage}</p>
                        </c:when>
                        <c:otherwise>
                            <p class="card-text">Une erreur inattendue s'est produite.</p>
                        </c:otherwise>
                    </c:choose>
                    
                    <div class="mt-4">
                        <a href="${pageContext.request.contextPath}/employee" class="btn btn-primary">
                            <i class="fas fa-home"></i> Retour au Dashboard
                        </a>
                        <a href="${pageContext.request.contextPath}/logout" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-sign-out-alt"></i> Déconnexion
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
