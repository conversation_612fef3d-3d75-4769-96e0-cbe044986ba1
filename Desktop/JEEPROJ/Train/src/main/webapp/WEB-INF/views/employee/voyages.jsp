<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mes Voyages - Espace Employé</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .voyage-card {
            transition: transform 0.2s;
            border-left: 4px solid #007bff;
        }
        .voyage-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .status-badge {
            font-size: 0.8em;
        }
        .role-badge {
            font-size: 0.75em;
        }
    </style>
</head>
<body class="bg-light">

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
        <a class="navbar-brand" href="${pageContext.request.contextPath}/employee">
            <i class="fas fa-train"></i> TrainSystem - Employé
        </a>
        
        <div class="navbar-nav ms-auto">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle text-white" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user"></i> ${employe.prenom} ${employe.nom}
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/employee">
                        <i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/employee/voyages">
                        <i class="fas fa-route"></i> Mes Voyages</a></li>
                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/employee/incidents">
                        <i class="fas fa-exclamation-triangle"></i> Incidents</a></li>
                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/employee/rapport-incident">
                        <i class="fas fa-file-alt"></i> Nouveau Rapport</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/logout">
                        <i class="fas fa-sign-out-alt"></i> Déconnexion</a></li>
                </ul>
            </div>
        </div>
    </div>
</nav>

<div class="container mt-4">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-route text-primary"></i>
                        Mes Voyages
                    </h1>
                    <p class="text-muted">Gestion de vos affectations et voyages</p>
                </div>
                <a href="${pageContext.request.contextPath}/employee" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left"></i> Retour au Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Filtres -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">Statut</label>
                            <select class="form-select" id="filtreStatut">
                                <option value="">Tous les statuts</option>
                                <option value="AFFECTE">Affecté</option>
                                <option value="CONFIRME">Confirmé</option>
                                <option value="TERMINE">Terminé</option>
                                <option value="ANNULE">Annulé</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Rôle</label>
                            <select class="form-select" id="filtreRole">
                                <option value="">Tous les rôles</option>
                                <option value="CONDUCTEUR">Conducteur</option>
                                <option value="CONTROLEUR">Contrôleur</option>
                                <option value="CHEF_BORD">Chef de bord</option>
                                <option value="MAINTENANCE">Maintenance</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Période</label>
                            <select class="form-select" id="filtrePeriode">
                                <option value="">Toutes les périodes</option>
                                <option value="PASSE">Voyages passés</option>
                                <option value="AUJOURD_HUI">Aujourd'hui</option>
                                <option value="FUTUR">Voyages futurs</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button class="btn btn-primary w-100" onclick="appliquerFiltres()">
                                <i class="fas fa-filter"></i> Filtrer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des voyages -->
    <div class="row">
        <div class="col-12">
            <c:choose>
                <c:when test="${not empty mesAffectations}">
                    <div class="row" id="listeVoyages">
                        <c:forEach var="affectation" items="${mesAffectations}" varStatus="status">
                            <div class="col-lg-6 mb-4 voyage-item" 
                                 data-statut="${affectation.statut}" 
                                 data-role="${affectation.roleVoyage}" 
                                 data-date="${affectation.voyage.dateVoyage}">
                                <div class="card voyage-card h-100">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">
                                            <i class="fas fa-train text-primary"></i>
                                            ${affectation.voyage.trajet.gareDepart.nom} → ${affectation.voyage.trajet.gareArrivee.nom}
                                        </h6>
                                        <span class="badge bg-${affectation.statut == 'TERMINE' ? 'success' : affectation.statut == 'CONFIRME' ? 'info' : affectation.statut == 'ANNULE' ? 'danger' : 'warning'} status-badge">
                                            ${affectation.statut.libelle}
                                        </span>
                                    </div>
                                    <div class="card-body">
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <small class="text-muted">Date</small>
                                                <div class="fw-bold">${affectation.voyage.dateVoyage}</div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">Horaire</small>
                                                <div class="fw-bold">
                                                    ${affectation.voyage.trajet.heureDepart} - ${affectation.voyage.trajet.heureArrivee}
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <small class="text-muted">Mon rôle</small>
                                                <div>
                                                    <span class="badge bg-secondary role-badge">${affectation.roleVoyage.libelle}</span>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">Durée</small>
                                                <div class="fw-bold">${affectation.voyage.trajet.duree}</div>
                                            </div>
                                        </div>

                                        <c:if test="${not empty affectation.notes}">
                                            <div class="mb-3">
                                                <small class="text-muted">Notes</small>
                                                <div class="text-muted small">${affectation.notes}</div>
                                            </div>
                                        </c:if>

                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                Affecté le: ${affectation.dateAffectation.toLocalDate()}
                                            </small>
                                            <div class="btn-group" role="group">
                                                <c:if test="${affectation.statut == 'AFFECTE'}">
                                                    <button class="btn btn-sm btn-success" onclick="confirmerAffectation(${affectation.id})">
                                                        <i class="fas fa-check"></i> Confirmer
                                                    </button>
                                                </c:if>
                                                <button class="btn btn-sm btn-outline-primary" onclick="voirDetails(${affectation.id})">
                                                    <i class="fas fa-eye"></i> Détails
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </c:forEach>
                    </div>
                </c:when>
                <c:otherwise>
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-times fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">Aucune affectation</h4>
                        <p class="text-muted">Vous n'avez actuellement aucun voyage assigné.</p>
                        <a href="${pageContext.request.contextPath}/employee" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i> Retour au Dashboard
                        </a>
                    </div>
                </c:otherwise>
            </c:choose>
        </div>
    </div>
</div>

<!-- Modal Détails -->
<div class="modal fade" id="modalDetails" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Détails du Voyage</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="modalDetailsContent">
                <!-- Contenu chargé dynamiquement -->
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
function appliquerFiltres() {
    const statut = document.getElementById('filtreStatut').value;
    const role = document.getElementById('filtreRole').value;
    const periode = document.getElementById('filtrePeriode').value;
    
    const voyages = document.querySelectorAll('.voyage-item');
    const aujourd_hui = new Date().toISOString().split('T')[0];
    
    voyages.forEach(voyage => {
        let afficher = true;
        
        // Filtre par statut
        if (statut && voyage.dataset.statut !== statut) {
            afficher = false;
        }
        
        // Filtre par rôle
        if (role && voyage.dataset.role !== role) {
            afficher = false;
        }
        
        // Filtre par période
        if (periode) {
            const dateVoyage = voyage.dataset.date;
            if (periode === 'PASSE' && dateVoyage >= aujourd_hui) {
                afficher = false;
            } else if (periode === 'AUJOURD_HUI' && dateVoyage !== aujourd_hui) {
                afficher = false;
            } else if (periode === 'FUTUR' && dateVoyage <= aujourd_hui) {
                afficher = false;
            }
        }
        
        voyage.style.display = afficher ? 'block' : 'none';
    });
}

function confirmerAffectation(id) {
    if (confirm('Confirmer cette affectation ?')) {
        // Simulation de confirmation
        alert('Affectation confirmée avec succès !');
        location.reload();
    }
}

function voirDetails(id) {
    // Simulation des détails
    document.getElementById('modalDetailsContent').innerHTML = `
        <div class="text-center">
            <i class="fas fa-info-circle fa-3x text-primary mb-3"></i>
            <h5>Détails du voyage #${id}</h5>
            <p class="text-muted">Fonctionnalité en cours de développement</p>
        </div>
    `;
    new bootstrap.Modal(document.getElementById('modalDetails')).show();
}
</script>
</body>
</html>
