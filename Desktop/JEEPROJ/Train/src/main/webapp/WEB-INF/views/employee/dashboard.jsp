<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Espace Employé - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .employee-sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .employee-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .voyage-card {
            border-left: 4px solid #28a745;
            transition: transform 0.2s;
        }
        .voyage-card:hover {
            transform: translateY(-2px);
        }
        .voyage-today {
            border-left-color: #dc3545;
            background-color: #fff5f5;
        }
        .voyage-upcoming {
            border-left-color: #ffc107;
        }
        .voyage-completed {
            border-left-color: #6c757d;
            opacity: 0.8;
        }
        .role-badge {
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 employee-sidebar p-0">
                <div class="d-flex flex-column p-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-user-tie"></i> Espace Employé
                        </h4>
                        <small class="text-light">Bonjour ${sessionScope.user.prenom}</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link text-white active" href="${pageContext.request.contextPath}/employee">
                            <i class="fas fa-tachometer-alt me-2"></i> Tableau de bord
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/employee/voyages">
                            <i class="fas fa-train me-2"></i> Mes Voyages
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/employee/planning">
                            <i class="fas fa-calendar me-2"></i> Planning
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/employee/profile">
                            <i class="fas fa-user me-2"></i> Mon Profil
                        </a>
                        
                        <hr class="text-white">
                        
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/">
                            <i class="fas fa-home me-2"></i> Retour au site
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/logout">
                            <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 employee-content">
                <div class="container-fluid py-4">
                    
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h1 class="h3 mb-0">
                                <i class="fas fa-user-tie me-2"></i>
                                Tableau de Bord Employé
                            </h1>
                            <p class="text-muted mb-0">Bienvenue ${sessionScope.user.prenom} ${sessionScope.user.nom}</p>
                        </div>
                        <div>
                            <button class="btn btn-outline-secondary" onclick="window.location.reload()">
                                <i class="fas fa-sync-alt me-1"></i> Actualiser
                            </button>
                        </div>
                    </div>
                    
                    <!-- Messages -->
                    <c:if test="${not empty sessionScope.successMessage}">
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            ${sessionScope.successMessage}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <c:remove var="successMessage" scope="session" />
                    </c:if>
                    
                    <!-- Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center border-primary">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">${statistiques.voyagesTotal}</h5>
                                    <p class="card-text">Voyages assignés</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center border-danger">
                                <div class="card-body">
                                    <h5 class="card-title text-danger">${statistiques.voyagesAujourdhui}</h5>
                                    <p class="card-text">Aujourd'hui</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center border-warning">
                                <div class="card-body">
                                    <h5 class="card-title text-warning">${statistiques.voyagesProchains}</h5>
                                    <p class="card-text">Prochains 7 jours</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center border-success">
                                <div class="card-body">
                                    <h5 class="card-title text-success">${statistiques.voyagesTermines}</h5>
                                    <p class="card-text">Terminés ce mois</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Voyage du jour -->
                    <c:if test="${not empty voyageAujourdhui}">
                        <div class="alert alert-warning border-left-warning shadow mb-4">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        Voyage d'aujourd'hui
                                    </h6>
                                    <p class="mb-0">
                                        <strong>${voyageAujourdhui.trajet.gareDepart.nom} → ${voyageAujourdhui.trajet.gareArrivee.nom}</strong>
                                        - Départ: ${voyageAujourdhui.heureDepart} - Rôle: <span class="badge bg-primary">${voyageAujourdhui.monRole}</span>
                                    </p>
                                </div>
                                <div class="col-auto">
                                    <a href="${pageContext.request.contextPath}/employee/voyage/${voyageAujourdhui.id}" class="btn btn-warning">
                                        <i class="fas fa-eye me-1"></i> Voir détails
                                    </a>
                                </div>
                            </div>
                        </div>
                    </c:if>
                    
                    <!-- Mes Prochains Voyages -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-train me-2"></i>
                                Mes Prochains Voyages
                            </h6>
                        </div>
                        <div class="card-body">
                            <c:choose>
                                <c:when test="${not empty prochainsVoyages}">
                                    <div class="row">
                                        <c:forEach var="voyage" items="${prochainsVoyages}" varStatus="status">
                                            <div class="col-lg-6 mb-3">
                                                <div class="card voyage-card voyage-upcoming">
                                                    <div class="card-body">
                                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                                            <h6 class="card-title mb-0">
                                                                ${voyage.trajet.gareDepart.nom} → ${voyage.trajet.gareArrivee.nom}
                                                            </h6>
                                                            <span class="badge bg-primary role-badge">${voyage.monRole}</span>
                                                        </div>
                                                        <div class="row text-muted small">
                                                            <div class="col-6">
                                                                <i class="fas fa-calendar me-1"></i>
                                                                ${voyage.dateVoyage}
                                                            </div>
                                                            <div class="col-6">
                                                                <i class="fas fa-clock me-1"></i>
                                                                ${voyage.heureDepart} - ${voyage.heureArrivee}
                                                            </div>
                                                        </div>
                                                        <div class="row text-muted small mt-1">
                                                            <div class="col-6">
                                                                <i class="fas fa-train me-1"></i>
                                                                Train ${voyage.numeroTrain}
                                                            </div>
                                                            <div class="col-6">
                                                                <i class="fas fa-users me-1"></i>
                                                                ${voyage.placesReservees}/${voyage.placesDisponibles} places
                                                            </div>
                                                        </div>
                                                        <div class="mt-3">
                                                            <a href="${pageContext.request.contextPath}/employee/voyage/${voyage.id}" 
                                                               class="btn btn-sm btn-outline-primary">
                                                                <i class="fas fa-eye me-1"></i> Détails
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </c:forEach>
                                    </div>
                                    <div class="text-center mt-3">
                                        <a href="${pageContext.request.contextPath}/employee/voyages" class="btn btn-primary">
                                            <i class="fas fa-list me-1"></i> Voir tous mes voyages
                                        </a>
                                    </div>
                                </c:when>
                                <c:otherwise>
                                    <div class="text-center py-4">
                                        <i class="fas fa-train fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">Aucun voyage assigné</h5>
                                        <p class="text-muted">Vous n'avez actuellement aucun voyage assigné pour les prochains jours.</p>
                                    </div>
                                </c:otherwise>
                            </c:choose>
                        </div>
                    </div>
                    
                    <!-- Planning de la semaine -->
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-calendar-week me-2"></i>
                                Planning de la Semaine
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Jour</th>
                                            <th>Date</th>
                                            <th>Voyage</th>
                                            <th>Rôle</th>
                                            <th>Horaires</th>
                                            <th>Statut</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Demo data - Replace with actual planning -->
                                        <tr>
                                            <td><strong>Lundi</strong></td>
                                            <td>29/05/2025</td>
                                            <td>Paris → Lyon</td>
                                            <td><span class="badge bg-danger">Conducteur</span></td>
                                            <td>08:30 - 11:45</td>
                                            <td><span class="badge bg-warning">Programmé</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Mardi</strong></td>
                                            <td>30/05/2025</td>
                                            <td>Lyon → Marseille</td>
                                            <td><span class="badge bg-success">Contrôleur</span></td>
                                            <td>14:15 - 16:30</td>
                                            <td><span class="badge bg-warning">Programmé</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Mercredi</strong></td>
                                            <td>31/05/2025</td>
                                            <td colspan="4" class="text-muted text-center">Repos</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Jeudi</strong></td>
                                            <td>01/06/2025</td>
                                            <td>Marseille → Nice</td>
                                            <td><span class="badge bg-danger">Conducteur</span></td>
                                            <td>09:00 - 11:30</td>
                                            <td><span class="badge bg-warning">Programmé</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Vendredi</strong></td>
                                            <td>02/06/2025</td>
                                            <td>Nice → Paris</td>
                                            <td><span class="badge bg-danger">Conducteur</span></td>
                                            <td>16:45 - 21:15</td>
                                            <td><span class="badge bg-warning">Programmé</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Samedi</strong></td>
                                            <td>03/06/2025</td>
                                            <td colspan="4" class="text-muted text-center">Repos</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Dimanche</strong></td>
                                            <td>04/06/2025</td>
                                            <td colspan="4" class="text-muted text-center">Repos</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="${pageContext.request.contextPath}/employee/planning" class="btn btn-outline-primary">
                                    <i class="fas fa-calendar me-1"></i> Voir planning complet
                                </a>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
