<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Employé - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .dashboard-card {
            transition: transform 0.2s;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .stat-card-success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }
        .stat-card-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .stat-card-info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .voyage-card {
            border-left: 4px solid #007bff;
        }
        .navbar-brand {
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-light">

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
        <a class="navbar-brand" href="${pageContext.request.contextPath}/employee">
            <i class="fas fa-train"></i> TrainSystem - Employé
        </a>
        
        <div class="navbar-nav ms-auto">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle text-white" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user"></i> ${employe.prenom} ${employe.nom}
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/employee/voyages">
                        <i class="fas fa-route"></i> Mes Voyages</a></li>
                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/employee/incidents">
                        <i class="fas fa-exclamation-triangle"></i> Incidents</a></li>
                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/employee/rapport-incident">
                        <i class="fas fa-file-alt"></i> Nouveau Rapport</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/logout">
                        <i class="fas fa-sign-out-alt"></i> Déconnexion</a></li>
                </ul>
            </div>
        </div>
    </div>
</nav>

<div class="container mt-4">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">
                <i class="fas fa-tachometer-alt text-primary"></i>
                Dashboard Employé
            </h1>
            <p class="text-muted">Bienvenue ${employe.prenom}, voici un aperçu de vos activités</p>
        </div>
    </div>

    <!-- Statistiques -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card dashboard-card stat-card">
                <div class="card-body text-center">
                    <i class="fas fa-route fa-2x mb-2"></i>
                    <h4 class="mb-0">${totalAffectations}</h4>
                    <small>Affectations Totales</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card dashboard-card stat-card-success">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h4 class="mb-0">${voyagesTermines}</h4>
                    <small>Voyages Terminés</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card dashboard-card stat-card-warning">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-day fa-2x mb-2"></i>
                    <h4 class="mb-0">${voyagesAujourdhui.size()}</h4>
                    <small>Voyages Aujourd'hui</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card dashboard-card stat-card-info">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <h4 class="mb-0">${incidentsOuverts}</h4>
                    <small>Incidents Ouverts</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Voyages d'aujourd'hui -->
        <div class="col-lg-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-day"></i> Voyages d'Aujourd'hui
                    </h5>
                </div>
                <div class="card-body">
                    <c:choose>
                        <c:when test="${not empty voyagesAujourdhui}">
                            <c:forEach var="affectation" items="${voyagesAujourdhui}">
                                <div class="voyage-card card mb-3">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="card-title mb-1">
                                                    ${affectation.voyage.trajet.gareDepart.nom} → 
                                                    ${affectation.voyage.trajet.gareArrivee.nom}
                                                </h6>
                                                <p class="text-muted mb-1">
                                                    <i class="fas fa-clock"></i> 
                                                    ${affectation.voyage.trajet.heureDepart}
                                                </p>
                                                <small class="text-muted">
                                                    Rôle: ${affectation.roleVoyage.libelle}
                                                </small>
                                            </div>
                                            <span class="badge bg-success">${affectation.statut.libelle}</span>
                                        </div>
                                    </div>
                                </div>
                            </c:forEach>
                        </c:when>
                        <c:otherwise>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-calendar-times fa-3x mb-3"></i>
                                <p>Aucun voyage prévu aujourd'hui</p>
                            </div>
                        </c:otherwise>
                    </c:choose>
                </div>
            </div>
        </div>

        <!-- Prochains voyages -->
        <div class="col-lg-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt"></i> Prochains Voyages
                    </h5>
                </div>
                <div class="card-body">
                    <c:choose>
                        <c:when test="${not empty prochainsVoyages}">
                            <c:forEach var="affectation" items="${prochainsVoyages}" varStatus="status">
                                <c:if test="${status.index < 5}">
                                    <div class="d-flex justify-content-between align-items-center py-2 ${status.index < prochainsVoyages.size() - 1 ? 'border-bottom' : ''}">
                                        <div>
                                            <h6 class="mb-0">
                                                ${affectation.voyage.trajet.gareDepart.nom} → 
                                                ${affectation.voyage.trajet.gareArrivee.nom}
                                            </h6>
                                            <small class="text-muted">
                                                ${affectation.voyage.dateVoyage} - ${affectation.roleVoyage.libelle}
                                            </small>
                                        </div>
                                        <span class="badge bg-primary">${affectation.statut.libelle}</span>
                                    </div>
                                </c:if>
                            </c:forEach>
                            <div class="text-center mt-3">
                                <a href="${pageContext.request.contextPath}/employee/voyages" class="btn btn-outline-success btn-sm">
                                    Voir tous mes voyages
                                </a>
                            </div>
                        </c:when>
                        <c:otherwise>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-calendar-plus fa-3x mb-3"></i>
                                <p>Aucun voyage programmé</p>
                            </div>
                        </c:otherwise>
                    </c:choose>
                </div>
            </div>
        </div>
    </div>

    <!-- Incidents récents -->
    <div class="row">
        <div class="col-12">
            <div class="card dashboard-card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle"></i> Incidents Récents
                    </h5>
                </div>
                <div class="card-body">
                    <c:choose>
                        <c:when test="${not empty incidentsRecents}">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Type</th>
                                            <th>Titre</th>
                                            <th>Gravité</th>
                                            <th>Statut</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <c:forEach var="incident" items="${incidentsRecents}">
                                            <tr>
                                                <td>
                                                    <span class="badge bg-secondary">${incident.typeIncident.libelle}</span>
                                                </td>
                                                <td>${incident.titre}</td>
                                                <td>
                                                    <span class="badge bg-${incident.gravite.cssClass}">${incident.gravite.libelle}</span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-${incident.statut.cssClass}">${incident.statut.libelle}</span>
                                                </td>
                                                <td>
                                                    ${incident.dateIncident.toLocalDate()} ${incident.dateIncident.toLocalTime().toString().substring(0,5)}
                                                </td>
                                            </tr>
                                        </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-3">
                                <a href="${pageContext.request.contextPath}/employee/incidents" class="btn btn-outline-warning btn-sm">
                                    Voir tous les incidents
                                </a>
                                <a href="${pageContext.request.contextPath}/employee/rapport-incident" class="btn btn-warning btn-sm ms-2">
                                    <i class="fas fa-plus"></i> Nouveau Rapport
                                </a>
                            </div>
                        </c:when>
                        <c:otherwise>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                                <p>Aucun incident récent</p>
                                <a href="${pageContext.request.contextPath}/employee/rapport-incident" class="btn btn-warning">
                                    <i class="fas fa-plus"></i> Signaler un Incident
                                </a>
                            </div>
                        </c:otherwise>
                    </c:choose>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
