<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${voyage != null ? 'Modifier' : 'Créer'} un Voyage - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .admin-sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .admin-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .form-section {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="admin-sidebar">
                    <div class="p-3">
                        <h5 class="text-white mb-4">
                            <i class="fas fa-train me-2"></i>
                            TrainSystem Admin
                        </h5>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin">
                            <i class="fas fa-tachometer-alt me-2"></i> Tableau de bord
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/gares">
                            <i class="fas fa-building me-2"></i> Gares
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/trajets">
                            <i class="fas fa-route me-2"></i> Trajets
                        </a>
                        <a class="nav-link text-white active" href="${pageContext.request.contextPath}/admin/voyages">
                            <i class="fas fa-train me-2"></i> Voyages
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/users">
                            <i class="fas fa-users me-2"></i> Utilisateurs
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/reservations">
                            <i class="fas fa-ticket-alt me-2"></i> Réservations
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/payments">
                            <i class="fas fa-credit-card me-2"></i> Paiements
                        </a>
                        <hr class="text-white">
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/logout">
                            <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main content -->
            <div class="col-md-9 col-lg-10">
                <div class="admin-content p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h1 class="h3 mb-0">
                                <c:choose>
                                    <c:when test="${voyage != null}">
                                        <i class="fas fa-edit me-2"></i>Modifier le Voyage #${voyage.id}
                                    </c:when>
                                    <c:otherwise>
                                        <i class="fas fa-plus me-2"></i>Créer un Nouveau Voyage
                                    </c:otherwise>
                                </c:choose>
                            </h1>
                            <p class="text-muted">Planifiez un voyage sur un trajet existant</p>
                        </div>
                        <div>
                            <a href="${pageContext.request.contextPath}/admin/voyages" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-1"></i> Retour à la liste
                            </a>
                        </div>
                    </div>
                    
                    <!-- Messages d'alerte -->
                    <c:if test="${not empty error}">
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            ${error}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    </c:if>

                    <!-- Formulaire -->
                    <div class="form-section">
                        <form action="${pageContext.request.contextPath}/admin/voyages/save" method="post" id="voyageForm">
                            <c:if test="${voyage != null}">
                                <input type="hidden" name="id" value="${voyage.id}">
                            </c:if>
                            
                            <div class="row">
                                <!-- Sélection du trajet -->
                                <div class="col-md-6 mb-3">
                                    <label for="trajetId" class="form-label">
                                        <i class="fas fa-route me-1"></i>Trajet <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="trajetId" name="trajetId" required>
                                        <option value="">Sélectionnez un trajet</option>
                                        <c:forEach var="trajet" items="${trajets}">
                                            <option value="${trajet.id}" 
                                                    data-depart="${trajet.heureDepart}" 
                                                    data-arrivee="${trajet.heureArrivee}"
                                                    data-prix="${trajet.prix}"
                                                    data-places="${trajet.nombrePlaces}"
                                                    data-type="${trajet.typeTrain}"
                                                    ${voyage != null && voyage.trajet.id == trajet.id ? 'selected' : ''}>
                                                ${trajet.libelleTrajet} - ${trajet.heureDepart} → ${trajet.heureArrivee} (${trajet.prix}€)
                                            </option>
                                        </c:forEach>
                                    </select>
                                    <div class="form-text">Choisissez le trajet pour ce voyage</div>
                                </div>

                                <!-- Date du voyage -->
                                <div class="col-md-6 mb-3">
                                    <label for="dateVoyage" class="form-label">
                                        <i class="fas fa-calendar me-1"></i>Date du voyage <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" class="form-control" id="dateVoyage" name="dateVoyage" 
                                           value="${voyage != null ? voyage.dateVoyage : ''}" required>
                                    <div class="form-text">Date à laquelle le voyage aura lieu</div>
                                </div>
                            </div>

                            <div class="row">
                                <!-- Places disponibles -->
                                <div class="col-md-6 mb-3">
                                    <label for="placesDisponibles" class="form-label">
                                        <i class="fas fa-users me-1"></i>Places disponibles
                                    </label>
                                    <input type="number" class="form-control" id="placesDisponibles" name="placesDisponibles" 
                                           value="${voyage != null ? voyage.placesDisponibles : ''}" 
                                           min="1" max="1000">
                                    <div class="form-text">Laissez vide pour utiliser la capacité du trajet</div>
                                </div>

                                <!-- Statut -->
                                <div class="col-md-6 mb-3">
                                    <label for="statut" class="form-label">
                                        <i class="fas fa-flag me-1"></i>Statut
                                    </label>
                                    <select class="form-select" id="statut" name="statut">
                                        <option value="PROGRAMME" ${voyage == null || voyage.statut == 'PROGRAMME' ? 'selected' : ''}>
                                            Programmé
                                        </option>
                                        <option value="EN_COURS" ${voyage != null && voyage.statut == 'EN_COURS' ? 'selected' : ''}>
                                            En cours
                                        </option>
                                        <option value="TERMINE" ${voyage != null && voyage.statut == 'TERMINE' ? 'selected' : ''}>
                                            Terminé
                                        </option>
                                        <option value="ANNULE" ${voyage != null && voyage.statut == 'ANNULE' ? 'selected' : ''}>
                                            Annulé
                                        </option>
                                        <option value="RETARDE" ${voyage != null && voyage.statut == 'RETARDE' ? 'selected' : ''}>
                                            Retardé
                                        </option>
                                    </select>
                                    <div class="form-text">Statut actuel du voyage</div>
                                </div>
                            </div>

                            <!-- Informations du trajet sélectionné -->
                            <div id="trajetInfo" class="alert alert-info d-none">
                                <h6><i class="fas fa-info-circle me-1"></i>Informations du trajet sélectionné</h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>Horaires:</strong><br>
                                        <span id="infoHoraires">-</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Prix:</strong><br>
                                        <span id="infoPrix">-</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Capacité:</strong><br>
                                        <span id="infoPlaces">-</span> places
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Type:</strong><br>
                                        <span id="infoType">-</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Boutons d'action -->
                            <div class="d-flex justify-content-between">
                                <a href="${pageContext.request.contextPath}/admin/voyages" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    ${voyage != null ? 'Modifier' : 'Créer'} le voyage
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const trajetSelect = document.getElementById('trajetId');
            const trajetInfo = document.getElementById('trajetInfo');
            const placesInput = document.getElementById('placesDisponibles');
            
            // Afficher les informations du trajet sélectionné
            trajetSelect.addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                
                if (selectedOption.value) {
                    const depart = selectedOption.dataset.depart;
                    const arrivee = selectedOption.dataset.arrivee;
                    const prix = selectedOption.dataset.prix;
                    const places = selectedOption.dataset.places;
                    const type = selectedOption.dataset.type;
                    
                    document.getElementById('infoHoraires').textContent = depart + ' → ' + arrivee;
                    document.getElementById('infoPrix').textContent = prix + '€';
                    document.getElementById('infoPlaces').textContent = places;
                    document.getElementById('infoType').textContent = type;
                    
                    // Mettre à jour le placeholder des places
                    placesInput.placeholder = places + ' (capacité du trajet)';
                    
                    trajetInfo.classList.remove('d-none');
                } else {
                    trajetInfo.classList.add('d-none');
                    placesInput.placeholder = '';
                }
            });
            
            // Déclencher l'événement si un trajet est déjà sélectionné (mode modification)
            if (trajetSelect.value) {
                trajetSelect.dispatchEvent(new Event('change'));
            }
            
            // Validation du formulaire
            document.getElementById('voyageForm').addEventListener('submit', function(e) {
                const trajetId = document.getElementById('trajetId').value;
                const dateVoyage = document.getElementById('dateVoyage').value;
                
                if (!trajetId) {
                    e.preventDefault();
                    alert('Veuillez sélectionner un trajet');
                    return;
                }
                
                if (!dateVoyage) {
                    e.preventDefault();
                    alert('Veuillez sélectionner une date');
                    return;
                }
            });
        });
    </script>
</body>
</html>
