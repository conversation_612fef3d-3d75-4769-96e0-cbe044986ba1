<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Utilisateurs - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .admin-sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .admin-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #007bff, #6610f2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 admin-sidebar p-0">
                <div class="d-flex flex-column p-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-cogs"></i> Administration
                        </h4>
                        <small class="text-light">Bonjour ${sessionScope.user.prenom}</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin">
                            <i class="fas fa-tachometer-alt me-2"></i> Tableau de bord
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/gares">
                            <i class="fas fa-building me-2"></i> Gares
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/trajets">
                            <i class="fas fa-route me-2"></i> Trajets
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/voyages">
                            <i class="fas fa-train me-2"></i> Voyages
                        </a>
                        <a class="nav-link text-white active" href="${pageContext.request.contextPath}/admin/users">
                            <i class="fas fa-users me-2"></i> Utilisateurs
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/reservations">
                            <i class="fas fa-ticket-alt me-2"></i> Réservations
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/payments">
                            <i class="fas fa-credit-card me-2"></i> Paiements
                        </a>
                        
                        <hr class="text-white">
                        
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/">
                            <i class="fas fa-home me-2"></i> Retour au site
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/logout">
                            <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 admin-content">
                <div class="container-fluid py-4">
                    
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="h3 mb-0">
                            <i class="fas fa-users me-2"></i>
                            Gestion des Utilisateurs
                        </h1>
                        <div>
                            <button class="btn btn-outline-secondary" onclick="window.location.reload()">
                                <i class="fas fa-sync-alt me-1"></i> Actualiser
                            </button>
                        </div>
                    </div>
                    
                    <!-- Messages -->
                    <c:if test="${not empty sessionScope.successMessage}">
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            ${sessionScope.successMessage}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <c:remove var="successMessage" scope="session" />
                    </c:if>
                    
                    <c:if test="${not empty sessionScope.errorMessage}">
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            ${sessionScope.errorMessage}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <c:remove var="errorMessage" scope="session" />
                    </c:if>
                    
                    <!-- Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">${utilisateurs.size()}</h5>
                                    <p class="card-text">Total utilisateurs</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-success">
                                        <c:set var="nbClients" value="0" />
                                        <c:forEach var="user" items="${utilisateurs}">
                                            <c:if test="${user.typeUtilisateur == 'CLIENT'}">
                                                <c:set var="nbClients" value="${nbClients + 1}" />
                                            </c:if>
                                        </c:forEach>
                                        ${nbClients}
                                    </h5>
                                    <p class="card-text">Clients</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-warning">
                                        <c:set var="nbAdmins" value="0" />
                                        <c:forEach var="user" items="${utilisateurs}">
                                            <c:if test="${user.typeUtilisateur == 'ADMINISTRATEUR'}">
                                                <c:set var="nbAdmins" value="${nbAdmins + 1}" />
                                            </c:if>
                                        </c:forEach>
                                        ${nbAdmins}
                                    </h5>
                                    <p class="card-text">Administrateurs</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-info">
                                        <c:set var="nbActifs" value="0" />
                                        <c:forEach var="user" items="${utilisateurs}">
                                            <c:if test="${user.actif}">
                                                <c:set var="nbActifs" value="${nbActifs + 1}" />
                                            </c:if>
                                        </c:forEach>
                                        ${nbActifs}
                                    </h5>
                                    <p class="card-text">Comptes actifs</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Users Table -->
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Liste des utilisateurs</h6>
                        </div>
                        <div class="card-body">
                            <c:choose>
                                <c:when test="${not empty utilisateurs}">
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>Utilisateur</th>
                                                    <th>Email</th>
                                                    <th>Type</th>
                                                    <th>Statut</th>
                                                    <th>Date création</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <c:forEach var="user" items="${utilisateurs}">
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <div class="user-avatar me-3">
                                                                    ${user.prenom.substring(0,1).toUpperCase()}${user.nom.substring(0,1).toUpperCase()}
                                                                </div>
                                                                <div>
                                                                    <div class="fw-bold">${user.prenom} ${user.nom}</div>
                                                                    <c:if test="${not empty user.telephone}">
                                                                        <small class="text-muted">${user.telephone}</small>
                                                                    </c:if>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>${user.email}</td>
                                                        <td>
                                                            <span class="badge bg-${user.typeUtilisateur == 'ADMINISTRATEUR' ? 'danger' : 
                                                                                   user.typeUtilisateur == 'EMPLOYE' ? 'warning' : 'primary'}">
                                                                ${user.typeUtilisateur}
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-${user.actif ? 'success' : 'secondary'}">
                                                                <i class="fas fa-${user.actif ? 'check' : 'times'} me-1"></i>
                                                                ${user.actif ? 'Actif' : 'Inactif'}
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <c:choose>
                                                                <c:when test="${user.dateCreation != null}">
                                                                    ${user.dateCreation.toLocalDate()}
                                                                </c:when>
                                                                <c:otherwise>
                                                                    -
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </td>
                                                        <td>
                                                            <c:if test="${user.id != sessionScope.user.id}">
                                                                <form method="post" action="${pageContext.request.contextPath}/admin/users/toggle/${user.id}" 
                                                                      style="display: inline;" 
                                                                      onsubmit="return confirm('Êtes-vous sûr de vouloir ${user.actif ? 'désactiver' : 'activer'} ce compte ?')">
                                                                    <button type="submit" class="btn btn-sm btn-${user.actif ? 'warning' : 'success'}">
                                                                        <i class="fas fa-${user.actif ? 'ban' : 'check'} me-1"></i>
                                                                        ${user.actif ? 'Désactiver' : 'Activer'}
                                                                    </button>
                                                                </form>
                                                            </c:if>
                                                            <c:if test="${user.id == sessionScope.user.id}">
                                                                <span class="text-muted">
                                                                    <i class="fas fa-user-shield me-1"></i>
                                                                    Vous
                                                                </span>
                                                            </c:if>
                                                        </td>
                                                    </tr>
                                                </c:forEach>
                                            </tbody>
                                        </table>
                                    </div>
                                </c:when>
                                <c:otherwise>
                                    <div class="text-center py-4">
                                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Aucun utilisateur trouvé</p>
                                    </div>
                                </c:otherwise>
                            </c:choose>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
