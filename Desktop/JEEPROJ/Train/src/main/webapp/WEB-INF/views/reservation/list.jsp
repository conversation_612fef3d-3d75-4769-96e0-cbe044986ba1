<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mes Réservations - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/">
                <i class="fas fa-train"></i> TrainSystem
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">Bonjour ${sessionScope.user.prenom}</span>
                <a class="nav-link" href="${pageContext.request.contextPath}/search">Rechercher</a>
                <a class="nav-link" href="${pageContext.request.contextPath}/">Accueil</a>
            </div>
        </div>
    </nav>

    <div class="container my-5">
        <h2><i class="fas fa-ticket-alt"></i> Mes Réservations</h2>

        <!-- Messages -->
        <c:if test="${not empty sessionScope.successMessage}">
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> ${sessionScope.successMessage}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <c:remove var="successMessage" scope="session"/>
        </c:if>

        <c:if test="${not empty errorMessage}">
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i> ${errorMessage}
            </div>
        </c:if>

        <c:choose>
            <c:when test="${empty reservations}">
                <div class="text-center py-5">
                    <i class="fas fa-ticket-alt fa-4x text-muted mb-3"></i>
                    <h3>Aucune réservation</h3>
                    <p class="text-muted mb-4">Vous n'avez pas encore effectué de réservation.</p>
                    <a href="${pageContext.request.contextPath}/search" class="btn btn-primary">
                        <i class="fas fa-search"></i> Rechercher un voyage
                    </a>
                </div>
            </c:when>
            <c:otherwise>
                <div class="row">
                    <c:forEach var="reservation" items="${reservations}">
                        <div class="col-12 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-md-8">
                                            <h5 class="card-title">
                                                ${reservation.voyage.trajet.gareDepart.ville} → ${reservation.voyage.trajet.gareArrivee.ville}
                                                <span class="badge bg-${reservation.statut == 'CONFIRMEE' ? 'success' : reservation.statut == 'EN_ATTENTE' ? 'warning' : 'danger'} ms-2">
                                                    ${reservation.statut}
                                                </span>
                                            </h5>
                                            <p class="card-text mb-1">
                                                <i class="fas fa-calendar"></i> 
                                                <fmt:formatDate value="${reservation.voyage.dateVoyage}" pattern="EEEE dd MMMM yyyy" />
                                                <span class="ms-3">
                                                    <i class="fas fa-clock"></i> 
                                                    <fmt:formatDate value="${reservation.voyage.trajet.heureDepart}" pattern="HH:mm" type="time" />
                                                </span>
                                            </p>
                                            <p class="card-text mb-1">
                                                <i class="fas fa-users"></i> ${reservation.nombrePlaces} place(s)
                                                <span class="ms-3">
                                                    <i class="fas fa-euro-sign"></i> 
                                                    <fmt:formatNumber value="${reservation.prixTotal}" type="currency" currencySymbol="€" />
                                                </span>
                                            </p>
                                            <small class="text-muted">
                                                Réservation n° ${reservation.numeroReservation}
                                            </small>
                                        </div>
                                        <div class="col-md-4 text-end">
                                            <div class="btn-group" role="group">
                                                <!-- Voir détails -->
                                                <a href="${pageContext.request.contextPath}/reservation/details/${reservation.id}"
                                                   class="btn btn-outline-primary btn-sm" title="Voir détails">
                                                    <i class="fas fa-eye"></i>
                                                </a>

                                                <!-- Télécharger PDF -->
                                                <c:if test="${reservation.statut == 'CONFIRMEE'}">
                                                    <a href="${pageContext.request.contextPath}/reservation/pdf/${reservation.id}"
                                                       class="btn btn-outline-success btn-sm" title="Télécharger billet PDF">
                                                        <i class="fas fa-download"></i>
                                                    </a>
                                                </c:if>

                                                <!-- Modifier -->
                                                <c:if test="${reservation.statut == 'CONFIRMEE' or reservation.statut == 'EN_ATTENTE'}">
                                                    <a href="${pageContext.request.contextPath}/reservation/edit/${reservation.id}"
                                                       class="btn btn-outline-warning btn-sm" title="Modifier réservation">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </c:if>

                                                <!-- Annuler -->
                                                <c:if test="${reservation.statut == 'CONFIRMEE' or reservation.statut == 'EN_ATTENTE'}">
                                                    <button class="btn btn-outline-danger btn-sm"
                                                            onclick="annulerReservation('${reservation.id}')" title="Demander annulation">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </c:if>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </c:forEach>
                </div>
            </c:otherwise>
        </c:choose>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function annulerReservation(reservationId) {
            if (confirm('Êtes-vous sûr de vouloir demander l\'annulation de cette réservation ?\n\nUne demande sera envoyée à l\'administrateur pour validation.')) {
                // Rediriger vers la page d'annulation
                window.location.href = '${pageContext.request.contextPath}/reservation/cancel/' + reservationId;
            }
        }
    </script>
</body>
</html>
