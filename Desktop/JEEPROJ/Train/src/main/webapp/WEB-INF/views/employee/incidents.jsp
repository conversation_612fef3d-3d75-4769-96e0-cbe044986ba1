<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mes Incidents - Espace Employé</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .incident-card {
            transition: transform 0.2s;
            border-left: 4px solid #ffc107;
        }
        .incident-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .incident-card.critique {
            border-left-color: #dc3545;
        }
        .incident-card.elevee {
            border-left-color: #fd7e14;
        }
        .incident-card.moyenne {
            border-left-color: #ffc107;
        }
        .incident-card.faible {
            border-left-color: #28a745;
        }
    </style>
</head>
<body class="bg-light">

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
        <a class="navbar-brand" href="${pageContext.request.contextPath}/employee">
            <i class="fas fa-train"></i> TrainSystem - Employé
        </a>
        
        <div class="navbar-nav ms-auto">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle text-white" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user"></i> ${employe.prenom} ${employe.nom}
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/employee">
                        <i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/employee/voyages">
                        <i class="fas fa-route"></i> Mes Voyages</a></li>
                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/employee/incidents">
                        <i class="fas fa-exclamation-triangle"></i> Incidents</a></li>
                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/employee/rapport-incident">
                        <i class="fas fa-file-alt"></i> Nouveau Rapport</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/logout">
                        <i class="fas fa-sign-out-alt"></i> Déconnexion</a></li>
                </ul>
            </div>
        </div>
    </div>
</nav>

<div class="container mt-4">
    <!-- Messages -->
    <c:if test="${not empty sessionScope.successMessage}">
        <div class="row mb-3">
            <div class="col-12">
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i> ${sessionScope.successMessage}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
        <c:remove var="successMessage" scope="session"/>
    </c:if>

    <c:if test="${not empty errorMessage}">
        <div class="row mb-3">
            <div class="col-12">
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle"></i> ${errorMessage}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
    </c:if>

    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                        Mes Rapports d'Incidents
                    </h1>
                    <p class="text-muted">Historique de vos rapports d'incidents</p>
                </div>
                <div>
                    <a href="${pageContext.request.contextPath}/employee/rapport-incident" class="btn btn-warning me-2">
                        <i class="fas fa-plus"></i> Nouveau Rapport
                    </a>
                    <a href="${pageContext.request.contextPath}/employee" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left"></i> Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-file-alt fa-2x text-primary mb-2"></i>
                    <h4 class="mb-0">${mesIncidents.size()}</h4>
                    <small class="text-muted">Total Rapports</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                    <h4 class="mb-0">
                        <c:set var="ouverts" value="0"/>
                        <c:forEach var="incident" items="${mesIncidents}">
                            <c:if test="${incident.ouvert}">
                                <c:set var="ouverts" value="${ouverts + 1}"/>
                            </c:if>
                        </c:forEach>
                        ${ouverts}
                    </h4>
                    <small class="text-muted">En Cours</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h4 class="mb-0">
                        <c:set var="resolus" value="0"/>
                        <c:forEach var="incident" items="${mesIncidents}">
                            <c:if test="${incident.statut == 'RESOLU' || incident.statut == 'FERME'}">
                                <c:set var="resolus" value="${resolus + 1}"/>
                            </c:if>
                        </c:forEach>
                        ${resolus}
                    </h4>
                    <small class="text-muted">Résolus</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="fas fa-exclamation-circle fa-2x text-danger mb-2"></i>
                    <h4 class="mb-0">
                        <c:set var="critiques" value="0"/>
                        <c:forEach var="incident" items="${mesIncidents}">
                            <c:if test="${incident.gravite == 'CRITIQUE' || incident.gravite == 'ELEVEE'}">
                                <c:set var="critiques" value="${critiques + 1}"/>
                            </c:if>
                        </c:forEach>
                        ${critiques}
                    </h4>
                    <small class="text-muted">Prioritaires</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">Type d'incident</label>
                            <select class="form-select" id="filtreType">
                                <option value="">Tous les types</option>
                                <option value="RETARD">Retard</option>
                                <option value="PANNE">Panne technique</option>
                                <option value="ACCIDENT">Accident</option>
                                <option value="SECURITE">Sécurité</option>
                                <option value="AUTRE">Autre</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Gravité</label>
                            <select class="form-select" id="filtreGravite">
                                <option value="">Toutes les gravités</option>
                                <option value="CRITIQUE">Critique</option>
                                <option value="ELEVEE">Élevée</option>
                                <option value="MOYENNE">Moyenne</option>
                                <option value="FAIBLE">Faible</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Statut</label>
                            <select class="form-select" id="filtreStatut">
                                <option value="">Tous les statuts</option>
                                <option value="OUVERT">Ouvert</option>
                                <option value="EN_COURS">En cours</option>
                                <option value="RESOLU">Résolu</option>
                                <option value="FERME">Fermé</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button class="btn btn-primary w-100" onclick="appliquerFiltres()">
                                <i class="fas fa-filter"></i> Filtrer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des incidents -->
    <div class="row">
        <div class="col-12">
            <c:choose>
                <c:when test="${not empty mesIncidents}">
                    <div class="row" id="listeIncidents">
                        <c:forEach var="incident" items="${mesIncidents}" varStatus="status">
                            <div class="col-lg-6 mb-4 incident-item" 
                                 data-type="${incident.typeIncident}" 
                                 data-gravite="${incident.gravite}" 
                                 data-statut="${incident.statut}">
                                <div class="card incident-card ${incident.gravite.toString().toLowerCase()} h-100">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <div>
                                            <span class="badge bg-secondary me-2">${incident.typeIncident.libelle}</span>
                                            <span class="badge bg-${incident.gravite.cssClass}">${incident.gravite.libelle}</span>
                                        </div>
                                        <span class="badge bg-${incident.statut.cssClass}">${incident.statut.libelle}</span>
                                    </div>
                                    <div class="card-body">
                                        <h6 class="card-title">${incident.titre}</h6>
                                        <p class="card-text text-muted small">
                                            ${incident.description.length() > 100 ? incident.description.substring(0, 100) + '...' : incident.description}
                                        </p>
                                        
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <small class="text-muted">Date incident</small>
                                                <div class="fw-bold">
                                                    ${incident.dateIncident.toLocalDate()} ${incident.dateIncident.toLocalTime().toString().substring(0,5)}
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">Rapport créé</small>
                                                <div class="fw-bold">
                                                    ${incident.dateRapport.toLocalDate()} ${incident.dateRapport.toLocalTime().toString().substring(0,5)}
                                                </div>
                                            </div>
                                        </div>

                                        <c:if test="${not empty incident.actionsPrises}">
                                            <div class="mb-3">
                                                <small class="text-muted">Actions prises</small>
                                                <div class="text-success small">${incident.actionsPrises}</div>
                                            </div>
                                        </c:if>

                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                ID: #${incident.id}
                                            </small>
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary" onclick="voirDetailsIncident(${incident.id})">
                                                    <i class="fas fa-eye"></i> Détails
                                                </button>
                                                <c:if test="${incident.ouvert}">
                                                    <button class="btn btn-sm btn-outline-warning" onclick="modifierIncident(${incident.id})">
                                                        <i class="fas fa-edit"></i> Modifier
                                                    </button>
                                                </c:if>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </c:forEach>
                    </div>
                </c:when>
                <c:otherwise>
                    <div class="text-center py-5">
                        <i class="fas fa-clipboard-check fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">Aucun incident rapporté</h4>
                        <p class="text-muted">Vous n'avez encore signalé aucun incident.</p>
                        <a href="${pageContext.request.contextPath}/employee/rapport-incident" class="btn btn-warning">
                            <i class="fas fa-plus"></i> Signaler un Incident
                        </a>
                    </div>
                </c:otherwise>
            </c:choose>
        </div>
    </div>
</div>

<!-- Modal Détails Incident -->
<div class="modal fade" id="modalDetailsIncident" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Détails de l'Incident</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="modalDetailsIncidentContent">
                <!-- Contenu chargé dynamiquement -->
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
function appliquerFiltres() {
    const type = document.getElementById('filtreType').value;
    const gravite = document.getElementById('filtreGravite').value;
    const statut = document.getElementById('filtreStatut').value;
    
    const incidents = document.querySelectorAll('.incident-item');
    
    incidents.forEach(incident => {
        let afficher = true;
        
        if (type && incident.dataset.type !== type) {
            afficher = false;
        }
        
        if (gravite && incident.dataset.gravite !== gravite) {
            afficher = false;
        }
        
        if (statut && incident.dataset.statut !== statut) {
            afficher = false;
        }
        
        incident.style.display = afficher ? 'block' : 'none';
    });
}

function voirDetailsIncident(id) {
    document.getElementById('modalDetailsIncidentContent').innerHTML = `
        <div class="text-center">
            <i class="fas fa-info-circle fa-3x text-primary mb-3"></i>
            <h5>Détails de l'incident #${id}</h5>
            <p class="text-muted">Fonctionnalité en cours de développement</p>
        </div>
    `;
    new bootstrap.Modal(document.getElementById('modalDetailsIncident')).show();
}

function modifierIncident(id) {
    if (confirm('Rediriger vers la modification de l\'incident ?')) {
        window.location.href = '${pageContext.request.contextPath}/employee/rapport-incident?edit=' + id;
    }
}
</script>
</body>
</html>
