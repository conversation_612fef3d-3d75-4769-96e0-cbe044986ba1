<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mon Espace Client - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }
        .stat-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card.primary { border-left: 5px solid #007bff; }
        .stat-card.success { border-left: 5px solid #28a745; }
        .stat-card.warning { border-left: 5px solid #ffc107; }
        .stat-card.danger { border-left: 5px solid #dc3545; }
        .action-card {
            border: none;
            border-radius: 15px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .action-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .voyage-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .voyage-card:hover {
            border-color: #007bff;
            box-shadow: 0 4px 15px rgba(0,123,255,0.1);
        }
        .quick-action-btn {
            width: 100%;
            height: 120px;
            border-radius: 15px;
            border: 2px dashed #dee2e6;
            transition: all 0.3s ease;
        }
        .quick-action-btn:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/">
                <i class="fas fa-train"></i> TrainSystem
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="${pageContext.request.contextPath}/">
                            <i class="fas fa-home"></i> Tableau de bord
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="${pageContext.request.contextPath}/search">
                            <i class="fas fa-search"></i> Rechercher
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="${pageContext.request.contextPath}/reservation/">
                            <i class="fas fa-ticket-alt"></i> Mes Réservations
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> ${user.prenom}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="${pageContext.request.contextPath}/profile">
                                <i class="fas fa-user-edit"></i> Mon Profil
                            </a></li>
                            <li><a class="dropdown-item" href="${pageContext.request.contextPath}/reservation/">
                                <i class="fas fa-history"></i> Historique des voyages
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="${pageContext.request.contextPath}/logout">
                                <i class="fas fa-sign-out-alt"></i> Déconnexion
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Header Dashboard -->
    <section class="dashboard-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-5 fw-bold mb-2">
                        <i class="fas fa-tachometer-alt"></i> Bienvenue, ${user.prenom} !
                    </h1>
                    <p class="lead mb-0">Gérez vos voyages et réservations en toute simplicité</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="text-white-50">
                        <i class="fas fa-calendar"></i> 
                        <fmt:formatDate value="<%= new java.util.Date() %>" pattern="EEEE dd MMMM yyyy" />
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Messages -->
    <c:if test="${not empty sessionScope.successMessage}">
        <div class="container mt-3">
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle"></i> ${sessionScope.successMessage}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
        <c:remove var="successMessage" scope="session"/>
    </c:if>

    <!-- Statistiques personnelles -->
    <section class="py-4">
        <div class="container">
            <h3 class="mb-4"><i class="fas fa-chart-bar"></i> Mes Statistiques</h3>
            <div class="row g-4">
                <div class="col-xl-3 col-md-6">
                    <div class="card stat-card primary h-100">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        Total Réservations
                                    </div>
                                    <div class="h4 mb-0 font-weight-bold text-gray-800">
                                        ${nbReservationsTotal}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-ticket-alt fa-2x text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6">
                    <div class="card stat-card success h-100">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Confirmées
                                    </div>
                                    <div class="h4 mb-0 font-weight-bold text-gray-800">
                                        ${nbReservationsConfirmees}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check-circle fa-2x text-success"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6">
                    <div class="card stat-card warning h-100">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        En Attente
                                    </div>
                                    <div class="h4 mb-0 font-weight-bold text-gray-800">
                                        ${nbReservationsEnAttente}
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-clock fa-2x text-warning"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6">
                    <div class="card stat-card danger h-100">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        Montant Total
                                    </div>
                                    <div class="h4 mb-0 font-weight-bold text-gray-800">
                                        <fmt:formatNumber value="${montantTotal}" type="currency" currencySymbol="€" />
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-euro-sign fa-2x text-success"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Actions rapides -->
    <section class="py-4 bg-white">
        <div class="container">
            <h3 class="mb-4"><i class="fas fa-bolt"></i> Actions Rapides</h3>
            <div class="row g-4">
                <div class="col-lg-3 col-md-6">
                    <a href="${pageContext.request.contextPath}/search" class="text-decoration-none">
                        <div class="card action-card h-100 text-center p-3">
                            <div class="card-body">
                                <i class="fas fa-search fa-3x text-primary mb-3"></i>
                                <h5 class="card-title">Rechercher un voyage</h5>
                                <p class="card-text text-muted">Trouvez votre prochain trajet</p>
                            </div>
                        </div>
                    </a>
                </div>

                <div class="col-lg-3 col-md-6">
                    <a href="${pageContext.request.contextPath}/reservation/" class="text-decoration-none">
                        <div class="card action-card h-100 text-center p-3">
                            <div class="card-body">
                                <i class="fas fa-history fa-3x text-success mb-3"></i>
                                <h5 class="card-title">Historique des voyages</h5>
                                <p class="card-text text-muted">Consultez vos billets utilisés</p>
                            </div>
                        </div>
                    </a>
                </div>

                <div class="col-lg-3 col-md-6">
                    <a href="${pageContext.request.contextPath}/reservation/" class="text-decoration-none">
                        <div class="card action-card h-100 text-center p-3">
                            <div class="card-body">
                                <i class="fas fa-edit fa-3x text-warning mb-3"></i>
                                <h5 class="card-title">Modifier réservations</h5>
                                <p class="card-text text-muted">Modifiez vos billets achetés</p>
                            </div>
                        </div>
                    </a>
                </div>

                <div class="col-lg-3 col-md-6">
                    <a href="${pageContext.request.contextPath}/reservation/" class="text-decoration-none">
                        <div class="card action-card h-100 text-center p-3">
                            <div class="card-body">
                                <i class="fas fa-download fa-3x text-info mb-3"></i>
                                <h5 class="card-title">Télécharger billets PDF</h5>
                                <p class="card-text text-muted">Obtenez vos billets électroniques</p>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Prochains voyages -->
    <section class="py-4">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <h3 class="mb-4"><i class="fas fa-calendar-check"></i> Mes Prochains Voyages</h3>
                    <c:choose>
                        <c:when test="${not empty prochainsVoyages}">
                            <c:forEach var="reservation" items="${prochainsVoyages}">
                                <div class="card voyage-card mb-3">
                                    <div class="card-body">
                                        <div class="row align-items-center">
                                            <div class="col-md-6">
                                                <h5 class="card-title mb-1">
                                                    ${reservation.voyage.trajet.gareDepart.ville}
                                                    <i class="fas fa-arrow-right mx-2"></i>
                                                    ${reservation.voyage.trajet.gareArrivee.ville}
                                                </h5>
                                                <p class="text-muted mb-2">
                                                    <i class="fas fa-calendar"></i>
                                                    <fmt:formatDate value="${reservation.voyage.dateVoyage}" pattern="dd/MM/yyyy" />
                                                    <i class="fas fa-clock ms-3"></i>
                                                    ${reservation.voyage.trajet.heureDepart} → ${reservation.voyage.trajet.heureArrivee}
                                                </p>
                                                <p class="mb-0">
                                                    <span class="badge bg-success">${reservation.statut}</span>
                                                    <span class="ms-2">${reservation.nombrePlaces} place(s)</span>
                                                </p>
                                            </div>
                                            <div class="col-md-3 text-center">
                                                <h6 class="text-muted">Prix</h6>
                                                <h4 class="text-success">
                                                    <fmt:formatNumber value="${reservation.prixTotal}" type="currency" currencySymbol="€" />
                                                </h4>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="btn-group-vertical w-100" role="group">
                                                    <a href="${pageContext.request.contextPath}/reservation/details/${reservation.id}"
                                                       class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-eye"></i> Détails
                                                    </a>
                                                    <c:if test="${reservation.statut == 'CONFIRMEE'}">
                                                        <a href="${pageContext.request.contextPath}/download-ticket?id=${reservation.id}"
                                                           class="btn btn-outline-success btn-sm" target="_blank">
                                                            <i class="fas fa-download"></i> PDF
                                                        </a>
                                                    </c:if>
                                                    <c:if test="${reservation.statut != 'CONFIRMEE'}">
                                                        <button class="btn btn-outline-secondary btn-sm" disabled title="Disponible uniquement pour les réservations confirmées">
                                                            <i class="fas fa-download"></i> PDF
                                                        </button>
                                                    </c:if>
                                                    <a href="${pageContext.request.contextPath}/reservation/edit/${reservation.id}"
                                                       class="btn btn-outline-warning btn-sm">
                                                        <i class="fas fa-edit"></i> Modifier
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </c:forEach>
                        </c:when>
                        <c:otherwise>
                            <div class="card">
                                <div class="card-body text-center py-5">
                                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Aucun voyage prévu</h5>
                                    <p class="text-muted">Vous n'avez aucun voyage confirmé à venir.</p>
                                    <a href="${pageContext.request.contextPath}/search" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Rechercher un voyage
                                    </a>
                                </div>
                            </div>
                        </c:otherwise>
                    </c:choose>
                </div>

                <!-- Sidebar avec actions rapides -->
                <div class="col-lg-4">
                    <h3 class="mb-4"><i class="fas fa-tools"></i> Gestion des Réservations</h3>

                    <!-- Fonctionnalités principales -->
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0"><i class="fas fa-cogs"></i> Mes Fonctionnalités</h6>
                        </div>
                        <div class="list-group list-group-flush">
                            <a href="${pageContext.request.contextPath}/reservation/"
                               class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <div>
                                    <i class="fas fa-history text-primary"></i>
                                    <strong class="ms-2">Historique des voyages</strong>
                                    <br><small class="text-muted">Consultez tous vos billets utilisés</small>
                                </div>
                                <span class="badge bg-primary rounded-pill">${nbReservationsTotal}</span>
                            </a>

                            <a href="${pageContext.request.contextPath}/reservation/"
                               class="list-group-item list-group-item-action">
                                <i class="fas fa-edit text-warning"></i>
                                <strong class="ms-2">Modifier mes réservations</strong>
                                <br><small class="text-muted">Modifiez vos billets achetés</small>
                            </a>

                            <a href="${pageContext.request.contextPath}/reservation/"
                               class="list-group-item list-group-item-action">
                                <i class="fas fa-times-circle text-danger"></i>
                                <strong class="ms-2">Annuler mes réservations</strong>
                                <br><small class="text-muted">Demande de confirmation admin</small>
                            </a>

                            <a href="${pageContext.request.contextPath}/reservation/"
                               class="list-group-item list-group-item-action">
                                <i class="fas fa-download text-success"></i>
                                <strong class="ms-2">Télécharger billets PDF</strong>
                                <br><small class="text-muted">Billets électroniques confirmés</small>
                            </a>
                        </div>
                    </div>

                    <!-- Réservations récentes -->
                    <div class="card">
                        <div class="card-header bg-secondary text-white">
                            <h6 class="mb-0"><i class="fas fa-clock"></i> Activité Récente</h6>
                        </div>
                        <div class="card-body">
                            <c:choose>
                                <c:when test="${not empty reservationsRecentes}">
                                    <c:forEach var="reservation" items="${reservationsRecentes}" varStatus="status">
                                        <c:if test="${status.index < 3}">
                                            <div class="d-flex align-items-center mb-3">
                                                <div class="flex-shrink-0">
                                                    <span class="badge bg-${reservation.statut == 'CONFIRMEE' ? 'success' : reservation.statut == 'EN_ATTENTE' ? 'warning' : 'danger'}">
                                                        <i class="fas fa-ticket-alt"></i>
                                                    </span>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <h6 class="mb-0">${reservation.voyage.trajet.gareDepart.ville} → ${reservation.voyage.trajet.gareArrivee.ville}</h6>
                                                    <small class="text-muted">
                                                        <fmt:formatDate value="${reservation.dateReservation}" pattern="dd/MM/yyyy" />
                                                    </small>
                                                </div>
                                            </div>
                                        </c:if>
                                    </c:forEach>
                                    <div class="text-center">
                                        <a href="${pageContext.request.contextPath}/reservation/" class="btn btn-outline-secondary btn-sm">
                                            Voir tout l'historique
                                        </a>
                                    </div>
                                </c:when>
                                <c:otherwise>
                                    <p class="text-muted text-center">Aucune réservation récente</p>
                                </c:otherwise>
                            </c:choose>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Suggestions de voyages -->
    <section class="py-4 bg-white">
        <div class="container">
            <h3 class="mb-4"><i class="fas fa-lightbulb"></i> Voyages Disponibles</h3>
            <div class="row">
                <c:choose>
                    <c:when test="${not empty voyagesDisponibles}">
                        <c:forEach var="voyage" items="${voyagesDisponibles}" varStatus="status">
                            <c:if test="${status.index < 6}">
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="card voyage-card h-100">
                                        <div class="card-body">
                                            <h5 class="card-title">
                                                ${voyage.trajet.gareDepart.ville}
                                                <i class="fas fa-arrow-right"></i>
                                                ${voyage.trajet.gareArrivee.ville}
                                            </h5>
                                            <p class="card-text">
                                                <i class="fas fa-calendar"></i>
                                                <fmt:formatDate value="${voyage.dateVoyage}" pattern="dd/MM/yyyy" />
                                                <br>
                                                <i class="fas fa-clock"></i>
                                                ${voyage.trajet.heureDepart} → ${voyage.trajet.heureArrivee}
                                                <br>
                                                <i class="fas fa-train"></i> ${voyage.trajet.typeTrain}
                                            </p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="text-success mb-0">
                                                        <fmt:formatNumber value="${voyage.trajet.prix}" type="currency" currencySymbol="€" />
                                                    </h6>
                                                    <small class="text-muted">${voyage.placesDisponibles} places</small>
                                                </div>
                                                <a href="${pageContext.request.contextPath}/reservation/create/${voyage.id}"
                                                   class="btn btn-primary btn-sm">
                                                    <i class="fas fa-plus"></i> Réserver
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </c:if>
                        </c:forEach>
                    </c:when>
                    <c:otherwise>
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="fas fa-train fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Aucun voyage disponible</h5>
                                <p class="text-muted">Aucun voyage n'est actuellement disponible.</p>
                            </div>
                        </div>
                    </c:otherwise>
                </c:choose>
            </div>

            <div class="text-center mt-4">
                <a href="${pageContext.request.contextPath}/search" class="btn btn-outline-primary">
                    <i class="fas fa-search"></i> Voir tous les voyages disponibles
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-train"></i> TrainSystem</h5>
                    <p class="mb-0">Votre plateforme de réservation de voyages en train</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">
                        <i class="fas fa-phone"></i> 3635 |
                        <i class="fas fa-envelope"></i> <EMAIL>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
