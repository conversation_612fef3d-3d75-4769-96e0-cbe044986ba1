<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rapport d'Incident - Espace Employé</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .form-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .required {
            color: #dc3545;
        }
        .gravite-card {
            cursor: pointer;
            transition: all 0.3s;
            border: 2px solid transparent;
        }
        .gravite-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .gravite-card.selected {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
        .type-card {
            cursor: pointer;
            transition: all 0.3s;
            border: 2px solid transparent;
        }
        .type-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .type-card.selected {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
    </style>
</head>
<body class="bg-light">

<!-- Navigation -->
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
        <a class="navbar-brand" href="${pageContext.request.contextPath}/employee">
            <i class="fas fa-train"></i> TrainSystem - Employé
        </a>
        
        <div class="navbar-nav ms-auto">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle text-white" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user"></i> ${employe.prenom} ${employe.nom}
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/employee">
                        <i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/employee/voyages">
                        <i class="fas fa-route"></i> Mes Voyages</a></li>
                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/employee/incidents">
                        <i class="fas fa-exclamation-triangle"></i> Incidents</a></li>
                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/employee/rapport-incident">
                        <i class="fas fa-file-alt"></i> Nouveau Rapport</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="${pageContext.request.contextPath}/logout">
                        <i class="fas fa-sign-out-alt"></i> Déconnexion</a></li>
                </ul>
            </div>
        </div>
    </div>
</nav>

<div class="container mt-4">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-file-alt text-warning"></i>
                        Nouveau Rapport d'Incident
                    </h1>
                    <p class="text-muted">Signaler un incident ou un problème rencontré</p>
                </div>
                <div>
                    <a href="${pageContext.request.contextPath}/employee/incidents" class="btn btn-outline-warning me-2">
                        <i class="fas fa-list"></i> Mes Incidents
                    </a>
                    <a href="${pageContext.request.contextPath}/employee" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left"></i> Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulaire -->
    <form id="formRapportIncident" onsubmit="soumettreRapport(event)">
        <!-- Section 1: Informations générales -->
        <div class="form-section">
            <h5 class="mb-3">
                <i class="fas fa-info-circle text-primary"></i>
                Informations Générales
            </h5>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label">Date et heure de l'incident <span class="required">*</span></label>
                    <input type="datetime-local" class="form-control" id="dateIncident" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">Voyage concerné</label>
                    <select class="form-select" id="voyageConcerne">
                        <option value="">Sélectionner un voyage (optionnel)</option>
                        <c:forEach var="affectation" items="${mesVoyages}">
                            <option value="${affectation.voyage.id}">
                                ${affectation.voyage.trajet.gareDepart.nom} → ${affectation.voyage.trajet.gareArrivee.nom} 
                                (${affectation.voyage.dateVoyage})
                            </option>
                        </c:forEach>
                    </select>
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label">Titre de l'incident <span class="required">*</span></label>
                <input type="text" class="form-control" id="titreIncident" placeholder="Résumé court de l'incident" required maxlength="100">
                <div class="form-text">Maximum 100 caractères</div>
            </div>
        </div>

        <!-- Section 2: Type d'incident -->
        <div class="form-section">
            <h5 class="mb-3">
                <i class="fas fa-tags text-primary"></i>
                Type d'Incident <span class="required">*</span>
            </h5>
            
            <div class="row">
                <div class="col-md-4 mb-3">
                    <div class="card type-card" onclick="selectionnerType('RETARD')">
                        <div class="card-body text-center">
                            <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                            <h6>Retard</h6>
                            <small class="text-muted">Retard de train ou correspondance</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card type-card" onclick="selectionnerType('PANNE')">
                        <div class="card-body text-center">
                            <i class="fas fa-tools fa-2x text-danger mb-2"></i>
                            <h6>Panne Technique</h6>
                            <small class="text-muted">Problème mécanique ou technique</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card type-card" onclick="selectionnerType('ACCIDENT')">
                        <div class="card-body text-center">
                            <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                            <h6>Accident</h6>
                            <small class="text-muted">Accident ou collision</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card type-card" onclick="selectionnerType('SECURITE')">
                        <div class="card-body text-center">
                            <i class="fas fa-shield-alt fa-2x text-info mb-2"></i>
                            <h6>Sécurité</h6>
                            <small class="text-muted">Problème de sécurité</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card type-card" onclick="selectionnerType('AUTRE')">
                        <div class="card-body text-center">
                            <i class="fas fa-question fa-2x text-secondary mb-2"></i>
                            <h6>Autre</h6>
                            <small class="text-muted">Autre type d'incident</small>
                        </div>
                    </div>
                </div>
            </div>
            <input type="hidden" id="typeIncident" required>
        </div>

        <!-- Section 3: Gravité -->
        <div class="form-section">
            <h5 class="mb-3">
                <i class="fas fa-thermometer-half text-primary"></i>
                Niveau de Gravité <span class="required">*</span>
            </h5>
            
            <div class="row">
                <div class="col-md-3 mb-3">
                    <div class="card gravite-card" onclick="selectionnerGravite('FAIBLE')">
                        <div class="card-body text-center">
                            <i class="fas fa-circle fa-2x text-success mb-2"></i>
                            <h6>Faible</h6>
                            <small class="text-muted">Impact minimal</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card gravite-card" onclick="selectionnerGravite('MOYENNE')">
                        <div class="card-body text-center">
                            <i class="fas fa-circle fa-2x text-warning mb-2"></i>
                            <h6>Moyenne</h6>
                            <small class="text-muted">Impact modéré</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card gravite-card" onclick="selectionnerGravite('ELEVEE')">
                        <div class="card-body text-center">
                            <i class="fas fa-circle fa-2x text-danger mb-2"></i>
                            <h6>Élevée</h6>
                            <small class="text-muted">Impact important</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card gravite-card" onclick="selectionnerGravite('CRITIQUE')">
                        <div class="card-body text-center">
                            <i class="fas fa-circle fa-2x text-dark mb-2"></i>
                            <h6>Critique</h6>
                            <small class="text-muted">Impact majeur</small>
                        </div>
                    </div>
                </div>
            </div>
            <input type="hidden" id="graviteIncident" required>
        </div>

        <!-- Section 4: Description -->
        <div class="form-section">
            <h5 class="mb-3">
                <i class="fas fa-file-text text-primary"></i>
                Description Détaillée <span class="required">*</span>
            </h5>
            
            <div class="mb-3">
                <label class="form-label">Description de l'incident</label>
                <textarea class="form-control" id="descriptionIncident" rows="5" 
                          placeholder="Décrivez en détail ce qui s'est passé, les circonstances, les conséquences..." 
                          required maxlength="1000"></textarea>
                <div class="form-text">Maximum 1000 caractères</div>
            </div>

            <div class="mb-3">
                <label class="form-label">Actions immédiates prises (optionnel)</label>
                <textarea class="form-control" id="actionsPrises" rows="3" 
                          placeholder="Décrivez les actions que vous avez prises pour résoudre ou atténuer l'incident..." 
                          maxlength="500"></textarea>
                <div class="form-text">Maximum 500 caractères</div>
            </div>
        </div>

        <!-- Boutons -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-outline-secondary" onclick="reinitialiserFormulaire()">
                        <i class="fas fa-undo"></i> Réinitialiser
                    </button>
                    <div>
                        <button type="button" class="btn btn-outline-primary me-2" onclick="sauvegarderBrouillon()">
                            <i class="fas fa-save"></i> Sauvegarder en brouillon
                        </button>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-paper-plane"></i> Soumettre le Rapport
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Modal Confirmation -->
<div class="modal fade" id="modalConfirmation" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="modalConfirmationContent">
                <!-- Contenu dynamique -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-warning" id="btnConfirmerSoumission">Confirmer</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
// Initialiser la date/heure actuelle
document.getElementById('dateIncident').value = new Date().toISOString().slice(0, 16);

function selectionnerType(type) {
    // Retirer la sélection précédente
    document.querySelectorAll('.type-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // Ajouter la sélection
    event.currentTarget.classList.add('selected');
    document.getElementById('typeIncident').value = type;
}

function selectionnerGravite(gravite) {
    // Retirer la sélection précédente
    document.querySelectorAll('.gravite-card').forEach(card => {
        card.classList.remove('selected');
    });
    
    // Ajouter la sélection
    event.currentTarget.classList.add('selected');
    document.getElementById('graviteIncident').value = gravite;
}

function soumettreRapport(event) {
    event.preventDefault();
    
    // Validation
    if (!document.getElementById('typeIncident').value) {
        alert('Veuillez sélectionner un type d\'incident');
        return;
    }
    
    if (!document.getElementById('graviteIncident').value) {
        alert('Veuillez sélectionner un niveau de gravité');
        return;
    }
    
    // Afficher la confirmation
    const titre = document.getElementById('titreIncident').value;
    const type = document.getElementById('typeIncident').value;
    const gravite = document.getElementById('graviteIncident').value;
    
    document.getElementById('modalConfirmationContent').innerHTML = `
        <p>Vous êtes sur le point de soumettre le rapport d'incident suivant :</p>
        <ul>
            <li><strong>Titre :</strong> ${titre}</li>
            <li><strong>Type :</strong> ${type}</li>
            <li><strong>Gravité :</strong> ${gravite}</li>
        </ul>
        <p>Une fois soumis, le rapport sera transmis à l'administration pour traitement.</p>
    `;
    
    document.getElementById('btnConfirmerSoumission').onclick = function() {
        // Simulation de soumission
        alert('Rapport d\'incident soumis avec succès !');
        window.location.href = '${pageContext.request.contextPath}/employee/incidents';
    };
    
    new bootstrap.Modal(document.getElementById('modalConfirmation')).show();
}

function sauvegarderBrouillon() {
    alert('Brouillon sauvegardé ! (Fonctionnalité en développement)');
}

function reinitialiserFormulaire() {
    if (confirm('Êtes-vous sûr de vouloir réinitialiser le formulaire ?')) {
        document.getElementById('formRapportIncident').reset();
        document.querySelectorAll('.type-card, .gravite-card').forEach(card => {
            card.classList.remove('selected');
        });
        document.getElementById('dateIncident').value = new Date().toISOString().slice(0, 16);
    }
}
</script>
</body>
</html>
