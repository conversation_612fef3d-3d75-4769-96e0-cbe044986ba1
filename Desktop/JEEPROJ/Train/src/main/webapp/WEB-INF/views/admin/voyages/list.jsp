<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Voyages - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .admin-sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .admin-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .voyage-card {
            transition: transform 0.2s;
        }
        .voyage-card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 admin-sidebar p-0">
                <div class="d-flex flex-column p-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-cogs"></i> Administration
                        </h4>
                        <small class="text-light">Bonjour ${sessionScope.user.prenom}</small>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin">
                            <i class="fas fa-tachometer-alt me-2"></i> Tableau de bord
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/gares">
                            <i class="fas fa-building me-2"></i> Gares
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/trajets">
                            <i class="fas fa-route me-2"></i> Trajets
                        </a>
                        <a class="nav-link text-white active" href="${pageContext.request.contextPath}/admin/voyages">
                            <i class="fas fa-train me-2"></i> Voyages
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/users">
                            <i class="fas fa-users me-2"></i> Utilisateurs
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/reservations">
                            <i class="fas fa-ticket-alt me-2"></i> Réservations
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/payments">
                            <i class="fas fa-credit-card me-2"></i> Paiements
                        </a>
                        
                        <hr class="text-white">
                        
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/">
                            <i class="fas fa-home me-2"></i> Retour au site
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/logout">
                            <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 admin-content">
                <div class="container-fluid py-4">
                    
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="h3 mb-0">
                            <i class="fas fa-train me-2"></i>
                            Gestion des Voyages
                        </h1>
                        <div>
                            <a href="${pageContext.request.contextPath}/admin/voyages/new" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i> Nouveau voyage
                            </a>
                            <button class="btn btn-outline-secondary" onclick="window.location.reload()">
                                <i class="fas fa-sync-alt me-1"></i> Actualiser
                            </button>
                        </div>
                    </div>
                    
                    <!-- Messages -->
                    <c:if test="${not empty sessionScope.successMessage}">
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            ${sessionScope.successMessage}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <c:remove var="successMessage" scope="session" />
                    </c:if>
                    
                    <c:if test="${not empty sessionScope.errorMessage}">
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            ${sessionScope.errorMessage}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <c:remove var="errorMessage" scope="session" />
                    </c:if>
                    
                    <!-- Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-primary">${voyages.size()}</h5>
                                    <p class="card-text">Total voyages</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-success">
                                        <c:set var="nbProgrammes" value="0" />
                                        <c:forEach var="voyage" items="${voyages}">
                                            <c:if test="${voyage.statut == 'PROGRAMME'}">
                                                <c:set var="nbProgrammes" value="${nbProgrammes + 1}" />
                                            </c:if>
                                        </c:forEach>
                                        ${nbProgrammes}
                                    </h5>
                                    <p class="card-text">Programmés</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-info">
                                        <c:set var="nbTermines" value="0" />
                                        <c:forEach var="voyage" items="${voyages}">
                                            <c:if test="${voyage.statut == 'TERMINE'}">
                                                <c:set var="nbTermines" value="${nbTermines + 1}" />
                                            </c:if>
                                        </c:forEach>
                                        ${nbTermines}
                                    </h5>
                                    <p class="card-text">Terminés</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title text-danger">
                                        <c:set var="nbAnnules" value="0" />
                                        <c:forEach var="voyage" items="${voyages}">
                                            <c:if test="${voyage.statut == 'ANNULE'}">
                                                <c:set var="nbAnnules" value="${nbAnnules + 1}" />
                                            </c:if>
                                        </c:forEach>
                                        ${nbAnnules}
                                    </h5>
                                    <p class="card-text">Annulés</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Voyages Table -->
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Liste des voyages</h6>
                        </div>
                        <div class="card-body">
                            <c:choose>
                                <c:when test="${not empty voyages}">
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>Trajet</th>
                                                    <th>Date</th>
                                                    <th>Horaires</th>
                                                    <th>Places</th>
                                                    <th>Taux d'occupation</th>
                                                    <th>Statut</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <c:forEach var="voyage" items="${voyages}">
                                                    <tr>
                                                        <td>
                                                            <div>
                                                                <div class="fw-bold">
                                                                    <c:choose>
                                                                        <c:when test="${not empty voyage.trajet}">
                                                                            ${voyage.trajet.libelleTrajet}
                                                                        </c:when>
                                                                        <c:otherwise>
                                                                            ${voyage.villeDepart} → ${voyage.villeArrivee}
                                                                        </c:otherwise>
                                                                    </c:choose>
                                                                </div>
                                                                <small class="text-muted">
                                                                    <c:choose>
                                                                        <c:when test="${not empty voyage.trajet and not empty voyage.trajet.gareDepart}">
                                                                            ${voyage.trajet.gareDepart.ville} → ${voyage.trajet.gareArrivee.ville}
                                                                        </c:when>
                                                                        <c:otherwise>
                                                                            ${voyage.villeDepart} → ${voyage.villeArrivee}
                                                                        </c:otherwise>
                                                                    </c:choose>
                                                                </small>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div>
                                                                <fmt:formatDate value="${voyage.dateVoyage}" pattern="dd/MM/yyyy" />
                                                                <br>
                                                                <small class="text-muted">
                                                                    <c:choose>
                                                                        <c:when test="${voyage.passe}">
                                                                            <i class="fas fa-clock text-muted"></i> Passé
                                                                        </c:when>
                                                                        <c:when test="${voyage.aujourdhui}">
                                                                            <i class="fas fa-clock text-warning"></i> Aujourd'hui
                                                                        </c:when>
                                                                        <c:otherwise>
                                                                            <i class="fas fa-clock text-success"></i> À venir
                                                                        </c:otherwise>
                                                                    </c:choose>
                                                                </small>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div>
                                                                <c:choose>
                                                                    <c:when test="${not empty voyage.trajet}">
                                                                        <strong>${voyage.trajet.heureDepart}</strong>
                                                                        <i class="fas fa-arrow-right mx-1"></i>
                                                                        <strong>${voyage.trajet.heureArrivee}</strong>
                                                                        <br>
                                                                        <small class="text-muted">${voyage.trajet.dureeFormatee}</small>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <strong>${voyage.heureDepart}</strong>
                                                                        <i class="fas fa-arrow-right mx-1"></i>
                                                                        <strong>${voyage.heureArrivee}</strong>
                                                                        <br>
                                                                        <small class="text-muted">${voyage.dureeFormatee}</small>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div>
                                                                <span class="badge bg-${voyage.placesDisponibles > 0 ? 'success' : 'danger'}">
                                                                    ${voyage.placesDisponibles} disponibles
                                                                </span>
                                                                <br>
                                                                <small class="text-muted">
                                                                    ${voyage.placesReservees} réservées / ${voyage.totalPlaces} total
                                                                </small>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="progress" style="height: 20px;">
                                                                <div class="progress-bar bg-${voyage.tauxOccupation > 80 ? 'danger' : 
                                                                                              voyage.tauxOccupation > 60 ? 'warning' : 'success'}" 
                                                                     role="progressbar" 
                                                                     style="width: ${voyage.tauxOccupation}%">
                                                                    <fmt:formatNumber value="${voyage.tauxOccupation}" maxFractionDigits="1" />%
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-${voyage.statut == 'PROGRAMME' ? 'success' : 
                                                                                   voyage.statut == 'TERMINE' ? 'info' : 
                                                                                   voyage.statut == 'ANNULE' ? 'danger' : 'secondary'}">
                                                                ${voyage.statut}
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group" role="group">
                                                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                                                        data-bs-toggle="modal" 
                                                                        data-bs-target="#detailsModal${voyage.id}">
                                                                    <i class="fas fa-eye"></i>
                                                                </button>
                                                                <c:if test="${voyage.statut == 'PROGRAMME' && !voyage.passe}">
                                                                    <button type="button" class="btn btn-sm btn-outline-warning" 
                                                                            onclick="modifierVoyage(${voyage.id})">
                                                                        <i class="fas fa-edit"></i>
                                                                    </button>
                                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                                            onclick="annulerVoyage(${voyage.id})">
                                                                        <i class="fas fa-times"></i>
                                                                    </button>
                                                                </c:if>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    
                                                    <!-- Modal de détails -->
                                                    <div class="modal fade" id="detailsModal${voyage.id}" tabindex="-1">
                                                        <div class="modal-dialog modal-lg">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title">Détails du voyage</h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    <div class="row">
                                                                        <div class="col-md-6">
                                                                            <h6>Informations générales</h6>
                                                                            <table class="table table-sm">
                                                                                <tr>
                                                                                    <td><strong>Trajet:</strong></td>
                                                                                    <td>
                                                                                        <c:choose>
                                                                                            <c:when test="${not empty voyage.trajet}">
                                                                                                ${voyage.trajet.libelleTrajet}
                                                                                            </c:when>
                                                                                            <c:otherwise>
                                                                                                ${voyage.villeDepart} → ${voyage.villeArrivee}
                                                                                            </c:otherwise>
                                                                                        </c:choose>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td><strong>Date:</strong></td>
                                                                                    <td><fmt:formatDate value="${voyage.dateVoyage}" pattern="dd/MM/yyyy" /></td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td><strong>Départ:</strong></td>
                                                                                    <td>
                                                                                        <c:choose>
                                                                                            <c:when test="${not empty voyage.trajet}">
                                                                                                ${voyage.trajet.heureDepart}
                                                                                            </c:when>
                                                                                            <c:otherwise>
                                                                                                ${voyage.heureDepart}
                                                                                            </c:otherwise>
                                                                                        </c:choose>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td><strong>Arrivée:</strong></td>
                                                                                    <td>
                                                                                        <c:choose>
                                                                                            <c:when test="${not empty voyage.trajet}">
                                                                                                ${voyage.trajet.heureArrivee}
                                                                                            </c:when>
                                                                                            <c:otherwise>
                                                                                                ${voyage.heureArrivee}
                                                                                            </c:otherwise>
                                                                                        </c:choose>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td><strong>Durée:</strong></td>
                                                                                    <td>
                                                                                        <c:choose>
                                                                                            <c:when test="${not empty voyage.trajet}">
                                                                                                ${voyage.trajet.dureeFormatee}
                                                                                            </c:when>
                                                                                            <c:otherwise>
                                                                                                ${voyage.dureeFormatee}
                                                                                            </c:otherwise>
                                                                                        </c:choose>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td><strong>Prix:</strong></td>
                                                                                    <td>
                                                                                        <c:choose>
                                                                                            <c:when test="${not empty voyage.trajet}">
                                                                                                <fmt:formatNumber value="${voyage.trajet.prix}" type="currency" currencySymbol="€" />
                                                                                            </c:when>
                                                                                            <c:otherwise>
                                                                                                <fmt:formatNumber value="${voyage.prix}" type="currency" currencySymbol="€" />
                                                                                            </c:otherwise>
                                                                                        </c:choose>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </div>
                                                                        <div class="col-md-6">
                                                                            <h6>Gestion des places</h6>
                                                                            <table class="table table-sm">
                                                                                <tr>
                                                                                    <td><strong>Places totales:</strong></td>
                                                                                    <td>${voyage.totalPlaces}</td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td><strong>Places réservées:</strong></td>
                                                                                    <td>${voyage.placesReservees}</td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td><strong>Places disponibles:</strong></td>
                                                                                    <td>${voyage.placesDisponibles}</td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td><strong>Taux d'occupation:</strong></td>
                                                                                    <td>
                                                                                        <fmt:formatNumber value="${voyage.tauxOccupation}" maxFractionDigits="1" />%
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td><strong>Statut:</strong></td>
                                                                                    <td>
                                                                                        <span class="badge bg-${voyage.statut == 'PROGRAMME' ? 'success' : 
                                                                                                               voyage.statut == 'TERMINE' ? 'info' : 
                                                                                                               voyage.statut == 'ANNULE' ? 'danger' : 'secondary'}">
                                                                                            ${voyage.statut}
                                                                                        </span>
                                                                                    </td>
                                                                                </tr>
                                                                            </table>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                                                                    <c:if test="${voyage.statut == 'PROGRAMME' && !voyage.passe}">
                                                                        <button type="button" class="btn btn-primary" onclick="modifierVoyage(${voyage.id})">
                                                                            <i class="fas fa-edit me-1"></i> Modifier
                                                                        </button>
                                                                    </c:if>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </c:forEach>
                                            </tbody>
                                        </table>
                                    </div>
                                </c:when>
                                <c:otherwise>
                                    <div class="text-center py-4">
                                        <i class="fas fa-train fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Aucun voyage trouvé</p>
                                        <a href="${pageContext.request.contextPath}/admin/voyages/new" class="btn btn-primary">
                                            <i class="fas fa-plus me-1"></i> Créer le premier voyage
                                        </a>
                                    </div>
                                </c:otherwise>
                            </c:choose>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function modifierVoyage(voyageId) {
            // TODO: Implémenter la modification de voyage
            alert('Fonctionnalité en cours d\'implémentation');
        }
        
        function annulerVoyage(voyageId) {
            if (confirm('Êtes-vous sûr de vouloir annuler ce voyage ? Cette action est irréversible.')) {
                // TODO: Implémenter l'annulation de voyage
                alert('Fonctionnalité en cours d\'implémentation');
            }
        }
    </script>
</body>
</html>
