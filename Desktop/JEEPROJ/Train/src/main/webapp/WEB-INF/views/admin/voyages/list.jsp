<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Voyages - TrainSystem</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .admin-sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .admin-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="admin-sidebar">
                    <div class="p-3">
                        <h5 class="text-white mb-4">
                            <i class="fas fa-train me-2"></i>
                            TrainSystem Admin
                        </h5>
                    </div>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin">
                            <i class="fas fa-tachometer-alt me-2"></i> Tableau de bord
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/gares">
                            <i class="fas fa-building me-2"></i> Gares
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/trajets">
                            <i class="fas fa-route me-2"></i> Trajets
                        </a>
                        <a class="nav-link text-white active" href="${pageContext.request.contextPath}/admin/voyages">
                            <i class="fas fa-train me-2"></i> Voyages
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/users">
                            <i class="fas fa-users me-2"></i> Utilisateurs
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/reservations">
                            <i class="fas fa-ticket-alt me-2"></i> Réservations
                        </a>
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/admin/payments">
                            <i class="fas fa-credit-card me-2"></i> Paiements
                        </a>
                        <hr class="text-white">
                        <a class="nav-link text-white" href="${pageContext.request.contextPath}/logout">
                            <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main content -->
            <div class="col-md-9 col-lg-10">
                <div class="admin-content p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h1 class="h3 mb-0">Gestion des Voyages</h1>
                            <p class="text-muted">Planifiez et gérez les voyages</p>
                        </div>
                        <div>
                            <a href="${pageContext.request.contextPath}/admin/voyages/new" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i> Nouveau Voyage
                            </a>
                        </div>
                    </div>
                    
                    <!-- Messages d'alerte -->
                    <c:if test="${not empty sessionScope.error}">
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            ${sessionScope.error}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <c:remove var="error" scope="session"/>
                    </c:if>

                    <c:if test="${not empty sessionScope.success}">
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            ${sessionScope.success}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <c:remove var="success" scope="session"/>
                    </c:if>
                    
                    <!-- Content -->
                    <div class="card shadow">
                        <div class="card-header">
                            <h6 class="m-0 font-weight-bold text-primary">Liste des Voyages</h6>
                        </div>
                        <div class="card-body">
                            <c:choose>
                                <c:when test="${not empty voyages}">
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Trajet</th>
                                                    <th>Informations</th>
                                                    <th>Statut</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <c:forEach var="voyage" items="${voyages}">
                                                    <tr>
                                                        <td>
                                                            <strong>#${voyage.id}</strong>
                                                        </td>
                                                        <td>
                                                            <div>
                                                                <div class="fw-bold">
                                                                    <c:choose>
                                                                        <c:when test="${not empty voyage.trajet and not empty voyage.trajet.libelleTrajet}">
                                                                            ${voyage.trajet.libelleTrajet}
                                                                        </c:when>
                                                                        <c:otherwise>
                                                                            Trajet de démonstration
                                                                        </c:otherwise>
                                                                    </c:choose>
                                                                </div>
                                                                <small class="text-muted">
                                                                    <c:choose>
                                                                        <c:when test="${not empty voyage.trajet and not empty voyage.trajet.gareDepart and not empty voyage.trajet.gareArrivee}">
                                                                            ${voyage.trajet.gareDepart.ville} → ${voyage.trajet.gareArrivee.ville}
                                                                        </c:when>
                                                                        <c:otherwise>
                                                                            Liaison de test
                                                                        </c:otherwise>
                                                                    </c:choose>
                                                                </small>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div>
                                                                <c:choose>
                                                                    <c:when test="${not empty voyage.trajet}">
                                                                        <c:if test="${not empty voyage.trajet.heureDepart and not empty voyage.trajet.heureArrivee}">
                                                                            <strong>Horaires:</strong> ${voyage.trajet.heureDepart} → ${voyage.trajet.heureArrivee}<br>
                                                                        </c:if>
                                                                        <c:if test="${not empty voyage.trajet.prix}">
                                                                            <strong>Prix:</strong> ${voyage.trajet.prix}€<br>
                                                                        </c:if>
                                                                        <c:if test="${not empty voyage.trajet.typeTrain}">
                                                                            <strong>Type:</strong> 
                                                                            <span class="badge bg-secondary">${voyage.trajet.typeTrain}</span>
                                                                        </c:if>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <span class="text-muted">Informations non disponibles</span>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <c:choose>
                                                                <c:when test="${not empty voyage.statut}">
                                                                    <span class="badge bg-primary">${voyage.statut}</span>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <span class="badge bg-secondary">PROGRAMME</span>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group" role="group">
                                                                <a href="${pageContext.request.contextPath}/admin/voyages/edit/${voyage.id}"
                                                                   class="btn btn-sm btn-outline-warning" title="Modifier">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                                <c:choose>
                                                                    <c:when test="${voyage.statut == 'PROGRAMME'}">
                                                                        <button type="button" class="btn btn-sm btn-outline-info"
                                                                                onclick="annulerVoyage(${voyage.id})" title="Annuler">
                                                                            <i class="fas fa-ban"></i>
                                                                        </button>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                                                onclick="supprimerVoyage(${voyage.id})" title="Supprimer">
                                                                            <i class="fas fa-trash"></i>
                                                                        </button>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </c:forEach>
                                            </tbody>
                                        </table>
                                    </div>
                                </c:when>
                                <c:otherwise>
                                    <div class="text-center py-5">
                                        <i class="fas fa-train fa-4x text-muted mb-4"></i>
                                        <h4 class="text-muted">Aucun voyage trouvé</h4>
                                        <p class="text-muted mb-4">
                                            La liste des voyages est vide. Commencez par créer des trajets, puis planifiez vos premiers voyages.
                                        </p>
                                        <div class="d-flex justify-content-center gap-2">
                                            <a href="${pageContext.request.contextPath}/admin/trajets" class="btn btn-outline-primary">
                                                <i class="fas fa-route me-1"></i> Gérer les trajets
                                            </a>
                                            <a href="${pageContext.request.contextPath}/admin/voyages/new" class="btn btn-primary">
                                                <i class="fas fa-plus me-1"></i> Créer un voyage
                                            </a>
                                        </div>
                                    </div>
                                </c:otherwise>
                            </c:choose>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function annulerVoyage(voyageId) {
            if (confirm('Êtes-vous sûr de vouloir annuler ce voyage ?\n\nCette action changera le statut du voyage à "ANNULE".')) {
                // Créer un formulaire pour envoyer la requête POST
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '${pageContext.request.contextPath}/admin/voyages/cancel/' + voyageId;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function supprimerVoyage(voyageId) {
            if (confirm('Êtes-vous sûr de vouloir supprimer définitivement ce voyage ?\n\nCette action est irréversible !')) {
                // Créer un formulaire pour envoyer la requête POST
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '${pageContext.request.contextPath}/admin/voyages/delete/' + voyageId;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Auto-refresh des alertes
        document.addEventListener('DOMContentLoaded', function() {
            // Masquer automatiquement les alertes après 5 secondes
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                setTimeout(function() {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        });
    </script>
</body>
</html>
