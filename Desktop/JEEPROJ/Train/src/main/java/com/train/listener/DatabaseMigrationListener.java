package com.train.listener;

import com.train.migration.DatabaseMigration;
import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

/**
 * Listener pour exécuter les migrations de base de données au démarrage de l'application
 */
@WebListener
public class DatabaseMigrationListener implements ServletContextListener {
    
    @Override
    public void contextInitialized(ServletContextEvent sce) {
        System.out.println("🚀 Démarrage de l'application TrainSystem...");
        System.out.println("🔄 Vérification et exécution des migrations de base de données...");
        
        try {
            // Exécuter les migrations automatiquement
            DatabaseMigration.executerMigrations();
            System.out.println("✅ Application prête ! Base de données à jour.");
        } catch (Exception e) {
            System.err.println("❌ Erreur critique lors des migrations : " + e.getMessage());
            e.printStackTrace();
            // L'application peut continuer même si les migrations échouent
            // mais il faut investiguer le problème
        }
    }
    
    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        System.out.println("🛑 Arrêt de l'application TrainSystem...");
    }
}
