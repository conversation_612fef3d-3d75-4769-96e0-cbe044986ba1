package com.train.service.impl;

import com.train.model.*;
import com.train.service.PaiementService;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * Implémentation du service de gestion des paiements avec données de démonstration
 */
public class PaiementServiceImpl implements PaiementService {
    
    private final Map<Long, Paiement> paiements = new HashMap<>();
    private final AtomicLong idGenerator = new AtomicLong(1);
    
    public PaiementServiceImpl() {
        initialiserDonneesDemo();
    }
    
    private void initialiserDonneesDemo() {
        // Paiements confirmés
        creerPaiementDemo(1L, new BigDecimal("89.50"), StatutPaiement.CONFIRME, TypePaiement.PAIEMENT, "CARTE", "TXN001");
        creerPaiementDemo(2L, new BigDecimal("65.00"), StatutPaiement.CONFIRME, TypePaiement.PAIEMENT, "PAYPAL", "TXN002");
        creerPaiementDemo(3L, new BigDecimal("120.00"), StatutPaiement.CONFIRME, TypePaiement.PAIEMENT, "CARTE", "TXN003");
        
        // Paiements en attente
        creerPaiementDemo(4L, new BigDecimal("75.50"), StatutPaiement.EN_ATTENTE, TypePaiement.PAIEMENT, "CARTE", null);
        creerPaiementDemo(5L, new BigDecimal("95.00"), StatutPaiement.EN_ATTENTE, TypePaiement.PAIEMENT, "VIREMENT", null);
        
        // Remboursements
        creerPaiementDemo(6L, new BigDecimal("-55.00"), StatutPaiement.CONFIRME, TypePaiement.REMBOURSEMENT, "CARTE", "REF001");
        creerPaiementDemo(7L, new BigDecimal("-40.00"), StatutPaiement.EN_ATTENTE, TypePaiement.REMBOURSEMENT, "PAYPAL", null);
        
        // Paiements échoués
        creerPaiementDemo(8L, new BigDecimal("110.00"), StatutPaiement.ECHEC, TypePaiement.PAIEMENT, "CARTE", null);
    }
    
    private void creerPaiementDemo(Long reservationId, BigDecimal montant, StatutPaiement statut, 
                                   TypePaiement type, String methode, String transactionId) {
        Paiement paiement = new Paiement();
        paiement.setId(idGenerator.getAndIncrement());
        paiement.setReservationId(reservationId);
        paiement.setMontant(montant);
        paiement.setStatut(statut);
        paiement.setTypePaiement(type);
        paiement.setMethodePaiement(methode);
        paiement.setTransactionId(transactionId);
        paiement.setDateCreation(LocalDateTime.now().minusDays(new Random().nextInt(30)));
        
        if (StatutPaiement.CONFIRME.equals(statut)) {
            paiement.setDatePaiement(paiement.getDateCreation().plusMinutes(new Random().nextInt(60)));
        }
        
        paiements.put(paiement.getId(), paiement);
    }
    
    @Override
    public Paiement creerPaiement(Paiement paiement) {
        if (paiement.getId() == null) {
            paiement.setId(idGenerator.getAndIncrement());
        }
        paiement.setDateCreation(LocalDateTime.now());
        paiements.put(paiement.getId(), paiement);
        return paiement;
    }
    
    @Override
    public Optional<Paiement> obtenirPaiementParId(Long id) {
        return Optional.ofNullable(paiements.get(id));
    }
    
    @Override
    public List<Paiement> obtenirTousLesPaiements() {
        return new ArrayList<>(paiements.values());
    }
    
    @Override
    public List<Paiement> obtenirPaiementsParReservation(Long reservationId) {
        return paiements.values().stream()
                .filter(p -> Objects.equals(p.getReservationId(), reservationId))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Paiement> obtenirPaiementsParStatut(StatutPaiement statut) {
        return paiements.values().stream()
                .filter(p -> Objects.equals(p.getStatut(), statut))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Paiement> obtenirPaiementsParPeriode(LocalDateTime dateDebut, LocalDateTime dateFin) {
        return paiements.values().stream()
                .filter(p -> p.getDateCreation().isAfter(dateDebut) && p.getDateCreation().isBefore(dateFin))
                .collect(Collectors.toList());
    }
    
    @Override
    public Paiement mettreAJourPaiement(Paiement paiement) {
        if (paiement.getId() != null && paiements.containsKey(paiement.getId())) {
            paiement.setDateModification(LocalDateTime.now());
            paiements.put(paiement.getId(), paiement);
            return paiement;
        }
        throw new IllegalArgumentException("Paiement non trouvé");
    }
    
    @Override
    public boolean confirmerPaiement(Long paiementId, String transactionId) {
        Optional<Paiement> optPaiement = obtenirPaiementParId(paiementId);
        if (optPaiement.isPresent()) {
            Paiement paiement = optPaiement.get();
            paiement.setStatut(StatutPaiement.CONFIRME);
            paiement.setTransactionId(transactionId);
            paiement.setDatePaiement(LocalDateTime.now());
            mettreAJourPaiement(paiement);
            return true;
        }
        return false;
    }
    
    @Override
    public boolean marquerPaiementEchec(Long paiementId, String motif) {
        Optional<Paiement> optPaiement = obtenirPaiementParId(paiementId);
        if (optPaiement.isPresent()) {
            Paiement paiement = optPaiement.get();
            paiement.setStatut(StatutPaiement.ECHEC);
            paiement.setCommentaire(motif);
            mettreAJourPaiement(paiement);
            return true;
        }
        return false;
    }
    
    @Override
    public boolean annulerPaiement(Long paiementId, String motif) {
        Optional<Paiement> optPaiement = obtenirPaiementParId(paiementId);
        if (optPaiement.isPresent()) {
            Paiement paiement = optPaiement.get();
            paiement.setStatut(StatutPaiement.ANNULE);
            paiement.setCommentaire(motif);
            mettreAJourPaiement(paiement);
            return true;
        }
        return false;
    }
    
    @Override
    public Paiement creerRemboursement(Long reservationId, BigDecimal montant, String motif) {
        Paiement remboursement = new Paiement();
        remboursement.setReservationId(reservationId);
        remboursement.setMontant(montant.negate()); // Montant négatif pour remboursement
        remboursement.setTypePaiement(TypePaiement.REMBOURSEMENT);
        remboursement.setStatut(StatutPaiement.EN_ATTENTE);
        remboursement.setCommentaire(motif);
        return creerPaiement(remboursement);
    }
    
    @Override
    public BigDecimal calculerRevenuTotal() {
        return paiements.values().stream()
                .filter(p -> StatutPaiement.CONFIRME.equals(p.getStatut()))
                .filter(p -> TypePaiement.PAIEMENT.equals(p.getTypePaiement()))
                .map(Paiement::getMontant)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    @Override
    public BigDecimal calculerRevenuParPeriode(LocalDateTime dateDebut, LocalDateTime dateFin) {
        return paiements.values().stream()
                .filter(p -> StatutPaiement.CONFIRME.equals(p.getStatut()))
                .filter(p -> TypePaiement.PAIEMENT.equals(p.getTypePaiement()))
                .filter(p -> p.getDatePaiement() != null)
                .filter(p -> p.getDatePaiement().isAfter(dateDebut) && p.getDatePaiement().isBefore(dateFin))
                .map(Paiement::getMontant)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    @Override
    public long compterPaiementsParStatut(StatutPaiement statut) {
        return paiements.values().stream()
                .filter(p -> Objects.equals(p.getStatut(), statut))
                .count();
    }
    
    @Override
    public StatistiquesPaiement obtenirStatistiques() {
        StatistiquesPaiement stats = new StatistiquesPaiement();
        
        // Revenus total
        stats.setRevenuTotal(calculerRevenuTotal());
        
        // Revenus du mois
        LocalDateTime debutMois = YearMonth.now().atDay(1).atStartOfDay();
        LocalDateTime finMois = YearMonth.now().atEndOfMonth().atTime(23, 59, 59);
        stats.setRevenuMois(calculerRevenuParPeriode(debutMois, finMois));
        
        // Paiements en attente
        BigDecimal paiementsEnAttente = paiements.values().stream()
                .filter(p -> StatutPaiement.EN_ATTENTE.equals(p.getStatut()))
                .filter(p -> TypePaiement.PAIEMENT.equals(p.getTypePaiement()))
                .map(Paiement::getMontant)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        stats.setPaiementsEnAttente(paiementsEnAttente);
        
        // Remboursements à traiter
        BigDecimal remboursementsATraiter = paiements.values().stream()
                .filter(p -> StatutPaiement.EN_ATTENTE.equals(p.getStatut()))
                .filter(p -> TypePaiement.REMBOURSEMENT.equals(p.getTypePaiement()))
                .map(p -> p.getMontant().abs())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        stats.setRemboursementsATraiter(remboursementsATraiter);
        
        // Remboursements effectués
        BigDecimal remboursementsEffectues = paiements.values().stream()
                .filter(p -> StatutPaiement.CONFIRME.equals(p.getStatut()))
                .filter(p -> TypePaiement.REMBOURSEMENT.equals(p.getTypePaiement()))
                .map(p -> p.getMontant().abs())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        stats.setRemboursementsEffectues(remboursementsEffectues);
        
        // Nombres
        stats.setNombrePaiements(paiements.values().stream()
                .filter(p -> TypePaiement.PAIEMENT.equals(p.getTypePaiement()))
                .count());
        
        stats.setNombreRemboursements(paiements.values().stream()
                .filter(p -> TypePaiement.REMBOURSEMENT.equals(p.getTypePaiement()))
                .count());
        
        return stats;
    }
}
