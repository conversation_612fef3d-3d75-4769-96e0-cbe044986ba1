package com.train.service;

import com.train.model.Gare;

import java.util.List;

/**
 * Interface du service pour la gestion des gares
 */
public interface GareService {
    
    /**
     * Crée une nouvelle gare
     * @param gare la gare à créer
     * @return la gare créée avec son ID
     * @throws IllegalArgumentException si les données sont invalides
     */
    Gare creerGare(Gare gare);
    
    /**
     * Modifie une gare existante
     * @param gare la gare à modifier
     * @return la gare modifiée
     * @throws IllegalArgumentException si la gare n'existe pas ou les données sont invalides
     */
    Gare modifierGare(Gare gare);
    
    /**
     * Supprime une gare
     * @param gareId l'ID de la gare à supprimer
     * @return true si la suppression a réussi
     * @throws IllegalStateException si la gare est utilisée dans des trajets
     */
    boolean supprimerGare(Long gareId);
    
    /**
     * Obtient une gare par son ID
     * @param gareId l'ID de la gare
     * @return la gare trouvée ou null si non trouvée
     */
    Gare obtenirGareParId(Long gareId);
    
    /**
     * Obtient une gare par son code
     * @param codeGare le code de la gare
     * @return la gare trouvée ou null si non trouvée
     */
    Gare obtenirGareParCode(String codeGare);
    
    /**
     * Obtient toutes les gares
     * @return liste de toutes les gares
     */
    List<Gare> obtenirToutesLesGares();
    
    /**
     * Obtient les gares actives
     * @return liste des gares actives
     */
    List<Gare> obtenirGaresActives();
    
    /**
     * Obtient les gares d'une ville
     * @param ville le nom de la ville
     * @return liste des gares de la ville
     */
    List<Gare> obtenirGaresParVille(String ville);
    
    /**
     * Recherche des gares par nom (recherche partielle)
     * @param nom le nom ou partie du nom à rechercher
     * @return liste des gares correspondantes
     */
    List<Gare> rechercherGaresParNom(String nom);
    
    /**
     * Obtient toutes les villes ayant des gares
     * @return liste des noms de villes
     */
    List<String> obtenirToutesLesVilles();
    
    /**
     * Vérifie si un code de gare existe déjà
     * @param codeGare le code à vérifier
     * @return true si le code existe, false sinon
     */
    boolean codeGareExiste(String codeGare);
    
    /**
     * Active ou désactive une gare
     * @param gareId l'ID de la gare
     * @param active true pour activer, false pour désactiver
     * @return true si la modification a réussi
     */
    boolean changerStatutGare(Long gareId, boolean active);
    
    /**
     * Valide les données d'une gare
     * @param gare la gare à valider
     * @throws IllegalArgumentException si les données sont invalides
     */
    void validerGare(Gare gare);
}
