package com.train.service;

import com.train.model.DemandeAnnulation;
import com.train.model.StatutAnnulation;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Interface pour la gestion des annulations
 */
public interface AnnulationService {
    
    /**
     * Créer une nouvelle demande d'annulation
     */
    DemandeAnnulation creerDemandeAnnulation(DemandeAnnulation demande);
    
    /**
     * Obtenir une demande d'annulation par son ID
     */
    Optional<DemandeAnnulation> obtenirDemandeParId(Long id);
    
    /**
     * Obtenir toutes les demandes d'annulation
     */
    List<DemandeAnnulation> obtenirToutesLesDemandes();
    
    /**
     * Obtenir les demandes par utilisateur
     */
    List<DemandeAnnulation> obtenirDemandesParUtilisateur(Long utilisateurId);
    
    /**
     * Obtenir les demandes par statut
     */
    List<DemandeAnnulation> obtenirDemandesParStatut(StatutAnnulation statut);
    
    /**
     * Obtenir les demandes entre deux dates
     */
    List<DemandeAnnulation> obtenirDemandesParPeriode(LocalDateTime dateDebut, LocalDateTime dateFin);
    
    /**
     * Mettre à jour une demande d'annulation
     */
    DemandeAnnulation mettreAJourDemande(DemandeAnnulation demande);
    
    /**
     * Approuver une demande d'annulation
     */
    boolean approuverDemande(Long demandeId, Long adminId, String commentaire);
    
    /**
     * Rejeter une demande d'annulation
     */
    boolean rejeterDemande(Long demandeId, Long adminId, String motif);
    
    /**
     * Marquer une demande comme traitée
     */
    boolean marquerTraitee(Long demandeId, Long adminId);
    
    /**
     * Calculer les frais d'annulation
     */
    BigDecimal calculerFraisAnnulation(Long reservationId);
    
    /**
     * Calculer le montant de remboursement
     */
    BigDecimal calculerMontantRemboursement(Long reservationId, BigDecimal fraisAnnulation);
    
    /**
     * Vérifier si une réservation peut être annulée
     */
    boolean peutEtreAnnulee(Long reservationId);
    
    /**
     * Compter les demandes par statut
     */
    long compterDemandesParStatut(StatutAnnulation statut);
    
    /**
     * Obtenir les statistiques d'annulation
     */
    StatistiquesAnnulation obtenirStatistiques();
    
    /**
     * Classe pour les statistiques d'annulation
     */
    class StatistiquesAnnulation {
        private long demandesEnAttente;
        private long demandesApprouvees;
        private long demandesRejetees;
        private long demandesTraitees;
        private BigDecimal montantTotalARembouser;
        private BigDecimal montantTotalRembourse;
        private BigDecimal fraisTotalAnnulation;
        
        // Constructeurs
        public StatistiquesAnnulation() {}
        
        // Getters et Setters
        public long getDemandesEnAttente() { return demandesEnAttente; }
        public void setDemandesEnAttente(long demandesEnAttente) { this.demandesEnAttente = demandesEnAttente; }
        
        public long getDemandesApprouvees() { return demandesApprouvees; }
        public void setDemandesApprouvees(long demandesApprouvees) { this.demandesApprouvees = demandesApprouvees; }
        
        public long getDemandesRejetees() { return demandesRejetees; }
        public void setDemandesRejetees(long demandesRejetees) { this.demandesRejetees = demandesRejetees; }
        
        public long getDemandesTraitees() { return demandesTraitees; }
        public void setDemandesTraitees(long demandesTraitees) { this.demandesTraitees = demandesTraitees; }
        
        public BigDecimal getMontantTotalARembouser() { return montantTotalARembouser; }
        public void setMontantTotalARembouser(BigDecimal montantTotalARembouser) { this.montantTotalARembouser = montantTotalARembouser; }
        
        public BigDecimal getMontantTotalRembourse() { return montantTotalRembourse; }
        public void setMontantTotalRembourse(BigDecimal montantTotalRembourse) { this.montantTotalRembourse = montantTotalRembourse; }
        
        public BigDecimal getFraisTotalAnnulation() { return fraisTotalAnnulation; }
        public void setFraisTotalAnnulation(BigDecimal fraisTotalAnnulation) { this.fraisTotalAnnulation = fraisTotalAnnulation; }
    }
}
