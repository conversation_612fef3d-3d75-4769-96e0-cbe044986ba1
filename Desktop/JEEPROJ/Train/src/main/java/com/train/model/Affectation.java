package com.train.model;

import java.time.LocalDateTime;

/**
 * <PERSON><PERSON><PERSON><PERSON> représentant l'affectation d'un employé à un voyage
 */
public class Affectation {
    
    private Long id;
    private Long voyageId;
    private Voyage voyage;
    private Long employeId;
    private Utilisateur employe;
    private RoleVoyage role;
    private LocalDateTime dateAffectation;
    private LocalDateTime dateModification;
    private boolean actif;
    private String commentaire;
    
    // Constructeurs
    public Affectation() {
        this.dateAffectation = LocalDateTime.now();
        this.actif = true;
    }
    
    public Affectation(Long voyageId, Long employeId, RoleVoyage role) {
        this();
        this.voyageId = voyageId;
        this.employeId = employeId;
        this.role = role;
    }
    
    // Getters et Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getVoyageId() {
        return voyageId;
    }
    
    public void setVoyageId(Long voyageId) {
        this.voyageId = voyageId;
    }
    
    public Voyage getVoyage() {
        return voyage;
    }
    
    public void setVoyage(Voyage voyage) {
        this.voyage = voyage;
        if (voyage != null) {
            this.voyageId = voyage.getId();
        }
    }
    
    public Long getEmployeId() {
        return employeId;
    }
    
    public void setEmployeId(Long employeId) {
        this.employeId = employeId;
    }
    
    public Utilisateur getEmploye() {
        return employe;
    }
    
    public void setEmploye(Utilisateur employe) {
        this.employe = employe;
        if (employe != null) {
            this.employeId = employe.getId();
        }
    }
    
    public RoleVoyage getRole() {
        return role;
    }
    
    public void setRole(RoleVoyage role) {
        this.role = role;
    }
    
    public LocalDateTime getDateAffectation() {
        return dateAffectation;
    }
    
    public void setDateAffectation(LocalDateTime dateAffectation) {
        this.dateAffectation = dateAffectation;
    }
    
    public LocalDateTime getDateModification() {
        return dateModification;
    }
    
    public void setDateModification(LocalDateTime dateModification) {
        this.dateModification = dateModification;
    }
    
    public boolean isActif() {
        return actif;
    }
    
    public void setActif(boolean actif) {
        this.actif = actif;
        this.dateModification = LocalDateTime.now();
    }
    
    public String getCommentaire() {
        return commentaire;
    }
    
    public void setCommentaire(String commentaire) {
        this.commentaire = commentaire;
    }
    
    // Méthodes utilitaires
    public String getLibelleRole() {
        if (role == null) return "Non défini";
        
        switch (role) {
            case CONDUCTEUR:
                return "Conducteur";
            case CONTROLEUR:
                return "Contrôleur";
            case CHEF_DE_BORD:
                return "Chef de bord";
            case AGENT_COMMERCIAL:
                return "Agent commercial";
            case TECHNICIEN:
                return "Technicien";
            default:
                return role.toString();
        }
    }
    
    public boolean isPrincipal() {
        return RoleVoyage.CONDUCTEUR.equals(role) || RoleVoyage.CHEF_DE_BORD.equals(role);
    }
    
    public String getNomCompletEmploye() {
        if (employe != null) {
            return employe.getPrenom() + " " + employe.getNom();
        }
        return "Employé #" + employeId;
    }
    
    public String getInfoVoyage() {
        if (voyage != null && voyage.getTrajet() != null) {
            return voyage.getTrajet().getGareDepart().getNom() + " → " + 
                   voyage.getTrajet().getGareArrivee().getNom();
        }
        return "Voyage #" + voyageId;
    }
    
    @Override
    public String toString() {
        return "Affectation{" +
                "id=" + id +
                ", voyageId=" + voyageId +
                ", employeId=" + employeId +
                ", role=" + role +
                ", actif=" + actif +
                ", dateAffectation=" + dateAffectation +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        Affectation that = (Affectation) o;
        
        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (voyageId != null ? !voyageId.equals(that.voyageId) : that.voyageId != null) return false;
        if (employeId != null ? !employeId.equals(that.employeId) : that.employeId != null) return false;
        return role == that.role;
    }
    
    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (voyageId != null ? voyageId.hashCode() : 0);
        result = 31 * result + (employeId != null ? employeId.hashCode() : 0);
        result = 31 * result + (role != null ? role.hashCode() : 0);
        return result;
    }
}
