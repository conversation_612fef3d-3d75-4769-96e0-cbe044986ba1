package com.train.model;

import java.time.LocalDateTime;

/**
 * Modèle pour la gestion des sièges
 */
public class Siege {
    
    private Long id;
    private Long voyageId;
    private String numeroSiege;
    private ClasseSiege classe;
    private Long reservationId;
    private StatutSiege statut;
    private LocalDateTime dateReservation;
    
    // Relations
    private Voyage voyage;
    private Reservation reservation;
    
    public enum ClasseSiege {
        PREMIERE("Première classe"),
        SECONDE("Seconde classe"),
        ECONOMIQUE("Classe économique");
        
        private final String libelle;
        
        ClasseSiege(String libelle) {
            this.libelle = libelle;
        }
        
        public String getLibelle() {
            return libelle;
        }
    }
    
    public enum StatutSiege {
        LIBRE("Libre"),
        RESERVE("Réservé"),
        OCCUPE("Occupé");
        
        private final String libelle;
        
        StatutSiege(String libelle) {
            this.libelle = libelle;
        }
        
        public String getLibelle() {
            return libelle;
        }
    }
    
    // Constructeurs
    public Siege() {}
    
    public Siege(Long voyageId, String numeroSiege, ClasseSiege classe) {
        this.voyageId = voyageId;
        this.numeroSiege = numeroSiege;
        this.classe = classe;
        this.statut = StatutSiege.LIBRE;
    }
    
    // Getters et Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getVoyageId() {
        return voyageId;
    }
    
    public void setVoyageId(Long voyageId) {
        this.voyageId = voyageId;
    }
    
    public String getNumeroSiege() {
        return numeroSiege;
    }
    
    public void setNumeroSiege(String numeroSiege) {
        this.numeroSiege = numeroSiege;
    }
    
    public ClasseSiege getClasse() {
        return classe;
    }
    
    public void setClasse(ClasseSiege classe) {
        this.classe = classe;
    }
    
    public Long getReservationId() {
        return reservationId;
    }
    
    public void setReservationId(Long reservationId) {
        this.reservationId = reservationId;
    }
    
    public StatutSiege getStatut() {
        return statut;
    }
    
    public void setStatut(StatutSiege statut) {
        this.statut = statut;
    }
    
    public LocalDateTime getDateReservation() {
        return dateReservation;
    }
    
    public void setDateReservation(LocalDateTime dateReservation) {
        this.dateReservation = dateReservation;
    }
    
    public Voyage getVoyage() {
        return voyage;
    }
    
    public void setVoyage(Voyage voyage) {
        this.voyage = voyage;
    }
    
    public Reservation getReservation() {
        return reservation;
    }
    
    public void setReservation(Reservation reservation) {
        this.reservation = reservation;
    }
    
    // Méthodes utilitaires
    
    /**
     * Vérifie si le siège est disponible
     */
    public boolean isDisponible() {
        return statut == StatutSiege.LIBRE;
    }
    
    /**
     * Réserve le siège pour une réservation
     */
    public void reserver(Long reservationId) {
        this.reservationId = reservationId;
        this.statut = StatutSiege.RESERVE;
        this.dateReservation = LocalDateTime.now();
    }
    
    /**
     * Libère le siège
     */
    public void liberer() {
        this.reservationId = null;
        this.statut = StatutSiege.LIBRE;
        this.dateReservation = null;
    }
    
    /**
     * Marque le siège comme occupé
     */
    public void occuper() {
        this.statut = StatutSiege.OCCUPE;
    }
    
    /**
     * Obtient la position du siège (rangée et colonne)
     */
    public SiegePosition getPosition() {
        if (numeroSiege == null || numeroSiege.length() < 2) {
            return null;
        }
        
        try {
            int rangee = Integer.parseInt(numeroSiege.substring(0, numeroSiege.length() - 1));
            char colonne = numeroSiege.charAt(numeroSiege.length() - 1);
            return new SiegePosition(rangee, colonne);
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    /**
     * Classe interne pour représenter la position d'un siège
     */
    public static class SiegePosition {
        private final int rangee;
        private final char colonne;
        
        public SiegePosition(int rangee, char colonne) {
            this.rangee = rangee;
            this.colonne = colonne;
        }
        
        public int getRangee() {
            return rangee;
        }
        
        public char getColonne() {
            return colonne;
        }
        
        public boolean isCouloir() {
            return colonne == 'C' || colonne == 'D';
        }
        
        public boolean isFenetre() {
            return colonne == 'A' || colonne == 'F';
        }
        
        @Override
        public String toString() {
            return rangee + String.valueOf(colonne);
        }
    }
    
    @Override
    public String toString() {
        return "Siege{" +
                "id=" + id +
                ", numeroSiege='" + numeroSiege + '\'' +
                ", classe=" + classe +
                ", statut=" + statut +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        Siege siege = (Siege) o;
        
        if (!voyageId.equals(siege.voyageId)) return false;
        if (!numeroSiege.equals(siege.numeroSiege)) return false;
        return classe == siege.classe;
    }
    
    @Override
    public int hashCode() {
        int result = voyageId.hashCode();
        result = 31 * result + numeroSiege.hashCode();
        result = 31 * result + classe.hashCode();
        return result;
    }
}
