package com.train.model;

import java.time.LocalDateTime;

/**
 * Entité représentant une gare
 */
public class Gare {

    private Long id;
    private String nom;
    private String ville;
    private String codeGare;
    private String adresse;
    private String codePostal;
    private Double latitude;
    private Double longitude;
    private boolean active;
    private LocalDateTime dateCreation;
    private LocalDateTime dateModification;
    
    // Constructeurs
    public Gare() {}
    
    public Gare(String nom, String ville, String codeGare, String adresse, String codePostal) {
        this.nom = nom;
        this.ville = ville;
        this.codeGare = codeGare;
        this.adresse = adresse;
        this.codePostal = codePostal;
        this.active = true;
        this.dateCreation = LocalDateTime.now();
    }

    public Gare(String nom, String ville, String codeGare, String adresse, String codePostal, Double latitude, Double longitude) {
        this(nom, ville, codeGare, adresse, codePostal);
        this.latitude = latitude;
        this.longitude = longitude;
    }
    
    // Getters et Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getNom() {
        return nom;
    }
    
    public void setNom(String nom) {
        this.nom = nom;
    }
    
    public String getVille() {
        return ville;
    }
    
    public void setVille(String ville) {
        this.ville = ville;
    }
    
    public String getCodeGare() {
        return codeGare;
    }
    
    public void setCodeGare(String codeGare) {
        this.codeGare = codeGare;
    }
    
    public String getAdresse() {
        return adresse;
    }
    
    public void setAdresse(String adresse) {
        this.adresse = adresse;
    }
    
    public String getCodePostal() {
        return codePostal;
    }
    
    public void setCodePostal(String codePostal) {
        this.codePostal = codePostal;
    }
    
    public boolean isActive() {
        return active;
    }
    
    public void setActive(boolean active) {
        this.active = active;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public LocalDateTime getDateCreation() {
        return dateCreation;
    }

    public void setDateCreation(LocalDateTime dateCreation) {
        this.dateCreation = dateCreation;
    }

    public LocalDateTime getDateModification() {
        return dateModification;
    }

    public void setDateModification(LocalDateTime dateModification) {
        this.dateModification = dateModification;
    }
    
    // Méthodes utilitaires
    public String getNomComplet() {
        return nom + " (" + ville + ")";
    }

    public boolean hasCoordinates() {
        return latitude != null && longitude != null;
    }

    public String getCoordinatesString() {
        if (hasCoordinates()) {
            return String.format("%.6f, %.6f", latitude, longitude);
        }
        return "Non renseignées";
    }

    public String getStatutText() {
        return active ? "Active" : "Inactive";
    }
    
    @Override
    public String toString() {
        return "Gare{" +
                "id=" + id +
                ", nom='" + nom + '\'' +
                ", ville='" + ville + '\'' +
                ", codeGare='" + codeGare + '\'' +
                ", active=" + active +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Gare gare = (Gare) obj;
        return id != null && id.equals(gare.id);
    }
    
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
