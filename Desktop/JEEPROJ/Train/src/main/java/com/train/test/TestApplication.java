package com.train.test;

import com.train.util.DatabaseConnection;
import com.train.util.PasswordUtil;
import com.train.service.UtilisateurService;
import com.train.service.impl.UtilisateurServiceImpl;
import com.train.model.Utilisateur;
import com.train.model.TypeUtilisateur;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Optional;

/**
 * Classe de test pour vérifier le bon fonctionnement de l'application
 */
public class TestApplication {
    
    public static void main(String[] args) {
        System.out.println("=== TEST DE L'APPLICATION TRAIN ===\n");
        
        // Test 1: Connexion à la base de données
        testDatabaseConnection();
        
        // Test 2: Utilitaire de mot de passe
        testPasswordUtil();
        
        // Test 3: Service utilisateur (si la DB est disponible)
        testUserService();
        
        System.out.println("\n=== FIN DES TESTS ===");
    }
    
    private static void testDatabaseConnection() {
        System.out.println("1. Test de connexion à la base de données:");
        System.out.println("   Tentative de connexion...");
        
        try {
            Connection conn = DatabaseConnection.getConnection();
            
            if (conn != null && !conn.isClosed()) {
                System.out.println("   ✅ Connexion réussie !");
                System.out.println("   📊 URL: " + conn.getMetaData().getURL());
                System.out.println("   🔧 Driver: " + conn.getMetaData().getDriverName());
                
                DatabaseConnection.closeConnection(conn);
                System.out.println("   ✅ Connexion fermée proprement");
            } else {
                System.out.println("   ❌ Échec de la connexion");
            }
            
        } catch (SQLException e) {
            System.out.println("   ❌ Erreur de connexion: " + e.getMessage());
            System.out.println("   💡 Vérifiez que:");
            System.out.println("      - MySQL est démarré");
            System.out.println("      - La base 'train' existe");
            System.out.println("      - Les paramètres dans database.properties sont corrects");
        } catch (Exception e) {
            System.out.println("   ❌ Erreur inattendue: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    private static void testPasswordUtil() {
        System.out.println("2. Test des utilitaires de mot de passe:");
        
        try {
            String motDePasse = "TestPassword123!";
            System.out.println("   🔐 Test avec le mot de passe: " + motDePasse);
            
            // Test de hachage
            String hash = PasswordUtil.hashPassword(motDePasse);
            System.out.println("   ✅ Hachage réussi (longueur: " + hash.length() + ")");
            
            // Test de vérification
            boolean isValid = PasswordUtil.verifyPassword(motDePasse, hash);
            System.out.println("   ✅ Vérification: " + (isValid ? "SUCCÈS" : "ÉCHEC"));
            
            // Test de force du mot de passe
            boolean isStrong = PasswordUtil.isStrongPassword(motDePasse);
            System.out.println("   ✅ Force du mot de passe: " + (isStrong ? "FORT" : "FAIBLE"));
            
            // Test avec un mauvais mot de passe
            boolean isBadValid = PasswordUtil.verifyPassword("mauvais", hash);
            System.out.println("   ✅ Test sécurité: " + (!isBadValid ? "SÉCURISÉ" : "PROBLÈME"));
            
        } catch (Exception e) {
            System.out.println("   ❌ Erreur dans les tests de mot de passe: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    private static void testUserService() {
        System.out.println("3. Test du service utilisateur:");
        
        try {
            UtilisateurService userService = new UtilisateurServiceImpl();
            
            // Test de validation d'email
            System.out.println("   📧 Test de validation d'email...");
            
            // Créer un utilisateur de test
            Utilisateur testUser = new Utilisateur();
            testUser.setNom("Test");
            testUser.setPrenom("Utilisateur");
            testUser.setEmail("<EMAIL>");
            testUser.setTelephone("0123456789");
            testUser.setTypeUtilisateur(TypeUtilisateur.CLIENT);
            
            try {
                userService.validerUtilisateur(testUser, "TestPassword123!");
                System.out.println("   ✅ Validation utilisateur: SUCCÈS");
            } catch (Exception e) {
                System.out.println("   ❌ Validation utilisateur: " + e.getMessage());
            }
            
            // Test de vérification d'email existant (nécessite la DB)
            try {
                boolean emailExists = userService.emailExiste("<EMAIL>");
                System.out.println("   ✅ Vérification email existant: " + (emailExists ? "TROUVÉ" : "NON TROUVÉ"));
            } catch (Exception e) {
                System.out.println("   ⚠️  Test email existant ignoré (DB non disponible): " + e.getMessage());
            }
            
        } catch (Exception e) {
            System.out.println("   ❌ Erreur dans les tests de service: " + e.getMessage());
        }
        
        System.out.println();
    }
}
