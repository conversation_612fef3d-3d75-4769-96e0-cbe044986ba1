package com.train.servlet;

import com.train.model.Gare;
import com.train.service.GareService;
import com.train.service.impl.GareServiceImpl;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 * Servlet spécifique pour la gestion des gares
 */
@WebServlet(name = "GareServlet", urlPatterns = {"/admin/gares/save"})
public class GareServlet extends HttpServlet {
    
    private GareService gareService;
    
    @Override
    public void init() throws ServletException {
        gareService = new GareServiceImpl();
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // Vérifier l'authentification admin
        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        try {
            // Récupérer les paramètres du formulaire
            String idStr = request.getParameter("id");
            String nom = request.getParameter("nom");
            String ville = request.getParameter("ville");
            String codeGare = request.getParameter("codeGare");
            String adresse = request.getParameter("adresse");
            String codePostal = request.getParameter("codePostal");
            String latitudeStr = request.getParameter("latitude");
            String longitudeStr = request.getParameter("longitude");
            String activeStr = request.getParameter("active");
            
            // Validation des champs obligatoires
            if (nom == null || nom.trim().isEmpty() ||
                ville == null || ville.trim().isEmpty() ||
                codeGare == null || codeGare.trim().isEmpty()) {
                
                session.setAttribute("error", "Les champs nom, ville et code gare sont obligatoires");
                response.sendRedirect(request.getContextPath() + "/admin/gares/new");
                return;
            }
            
            // Vérifier l'unicité du code gare
            Gare gareExistante = gareService.obtenirGareParCode(codeGare.trim().toUpperCase());
            if (gareExistante != null && (idStr == null || !gareExistante.getId().equals(Long.parseLong(idStr)))) {
                session.setAttribute("error", "Une gare avec ce code existe déjà");
                if (idStr != null && !idStr.trim().isEmpty()) {
                    response.sendRedirect(request.getContextPath() + "/admin/gares/edit/" + idStr);
                } else {
                    response.sendRedirect(request.getContextPath() + "/admin/gares/new");
                }
                return;
            }
            
            // Créer ou modifier la gare
            Gare gare;
            if (idStr != null && !idStr.trim().isEmpty()) {
                // Modification
                gare = gareService.obtenirGareParId(Long.parseLong(idStr));
                if (gare == null) {
                    session.setAttribute("error", "Gare non trouvée");
                    response.sendRedirect(request.getContextPath() + "/admin/gares");
                    return;
                }
                gare.setDateModification(java.time.LocalDateTime.now());
            } else {
                // Création
                gare = new Gare();
                gare.setDateCreation(java.time.LocalDateTime.now());
            }
            
            // Remplir les propriétés
            gare.setNom(nom.trim());
            gare.setVille(ville.trim());
            gare.setCodeGare(codeGare.trim().toUpperCase());
            gare.setAdresse(adresse != null && !adresse.trim().isEmpty() ? adresse.trim() : null);
            gare.setCodePostal(codePostal != null && !codePostal.trim().isEmpty() ? codePostal.trim() : null);
            gare.setActive("true".equals(activeStr));
            
            // Gérer les coordonnées GPS
            if (latitudeStr != null && !latitudeStr.trim().isEmpty()) {
                try {
                    gare.setLatitude(Double.parseDouble(latitudeStr));
                } catch (NumberFormatException e) {
                    session.setAttribute("error", "Format de latitude invalide");
                    if (idStr != null && !idStr.trim().isEmpty()) {
                        response.sendRedirect(request.getContextPath() + "/admin/gares/edit/" + idStr);
                    } else {
                        response.sendRedirect(request.getContextPath() + "/admin/gares/new");
                    }
                    return;
                }
            } else {
                gare.setLatitude(null);
            }
            
            if (longitudeStr != null && !longitudeStr.trim().isEmpty()) {
                try {
                    gare.setLongitude(Double.parseDouble(longitudeStr));
                } catch (NumberFormatException e) {
                    session.setAttribute("error", "Format de longitude invalide");
                    if (idStr != null && !idStr.trim().isEmpty()) {
                        response.sendRedirect(request.getContextPath() + "/admin/gares/edit/" + idStr);
                    } else {
                        response.sendRedirect(request.getContextPath() + "/admin/gares/new");
                    }
                    return;
                }
            } else {
                gare.setLongitude(null);
            }
            
            // Sauvegarder
            if (gare.getId() == null) {
                gareService.creerGare(gare);
                session.setAttribute("success", "Gare créée avec succès");
            } else {
                gareService.modifierGare(gare);
                session.setAttribute("success", "Gare modifiée avec succès");
            }
            
            response.sendRedirect(request.getContextPath() + "/admin/gares");
            
        } catch (Exception e) {
            session.setAttribute("error", "Erreur lors de la sauvegarde : " + e.getMessage());
            response.sendRedirect(request.getContextPath() + "/admin/gares");
        }
    }
}
