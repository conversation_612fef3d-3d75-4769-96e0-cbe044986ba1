package com.train.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <PERSON><PERSON><PERSON><PERSON> représentant une demande d'annulation
 */
public class DemandeAnnulation {
    
    private Long id;
    private Long reservationId;
    private Reservation reservation;
    private Long utilisateurId;
    private Utilisateur utilisateur;
    private String motif;
    private StatutAnnulation statut;
    private BigDecimal montantPaye;
    private BigDecimal fraisAnnulation;
    private BigDecimal montantRemboursement;
    private LocalDateTime dateDemande;
    private LocalDateTime dateTraitement;
    private String commentaireAdmin;
    private Long adminId;
    private Utilisateur admin;
    
    // Constructeurs
    public DemandeAnnulation() {
        this.dateDemande = LocalDateTime.now();
        this.statut = StatutAnnulation.EN_ATTENTE;
    }
    
    public DemandeAnnulation(Long reservationId, Long utilisateurId, String motif) {
        this();
        this.reservationId = reservationId;
        this.utilisateurId = utilisateurId;
        this.motif = motif;
    }
    
    // Getters et Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getReservationId() {
        return reservationId;
    }
    
    public void setReservationId(Long reservationId) {
        this.reservationId = reservationId;
    }
    
    public Reservation getReservation() {
        return reservation;
    }
    
    public void setReservation(Reservation reservation) {
        this.reservation = reservation;
        if (reservation != null) {
            this.reservationId = reservation.getId();
        }
    }
    
    public Long getUtilisateurId() {
        return utilisateurId;
    }
    
    public void setUtilisateurId(Long utilisateurId) {
        this.utilisateurId = utilisateurId;
    }
    
    public Utilisateur getUtilisateur() {
        return utilisateur;
    }
    
    public void setUtilisateur(Utilisateur utilisateur) {
        this.utilisateur = utilisateur;
        if (utilisateur != null) {
            this.utilisateurId = utilisateur.getId();
        }
    }
    
    public String getMotif() {
        return motif;
    }
    
    public void setMotif(String motif) {
        this.motif = motif;
    }
    
    public StatutAnnulation getStatut() {
        return statut;
    }
    
    public void setStatut(StatutAnnulation statut) {
        this.statut = statut;
    }
    
    public BigDecimal getMontantPaye() {
        return montantPaye;
    }
    
    public void setMontantPaye(BigDecimal montantPaye) {
        this.montantPaye = montantPaye;
    }
    
    public BigDecimal getFraisAnnulation() {
        return fraisAnnulation;
    }
    
    public void setFraisAnnulation(BigDecimal fraisAnnulation) {
        this.fraisAnnulation = fraisAnnulation;
    }
    
    public BigDecimal getMontantRemboursement() {
        return montantRemboursement;
    }
    
    public void setMontantRemboursement(BigDecimal montantRemboursement) {
        this.montantRemboursement = montantRemboursement;
    }
    
    public LocalDateTime getDateDemande() {
        return dateDemande;
    }
    
    public void setDateDemande(LocalDateTime dateDemande) {
        this.dateDemande = dateDemande;
    }
    
    public LocalDateTime getDateTraitement() {
        return dateTraitement;
    }
    
    public void setDateTraitement(LocalDateTime dateTraitement) {
        this.dateTraitement = dateTraitement;
    }
    
    public String getCommentaireAdmin() {
        return commentaireAdmin;
    }
    
    public void setCommentaireAdmin(String commentaireAdmin) {
        this.commentaireAdmin = commentaireAdmin;
    }
    
    public Long getAdminId() {
        return adminId;
    }
    
    public void setAdminId(Long adminId) {
        this.adminId = adminId;
    }
    
    public Utilisateur getAdmin() {
        return admin;
    }
    
    public void setAdmin(Utilisateur admin) {
        this.admin = admin;
        if (admin != null) {
            this.adminId = admin.getId();
        }
    }
    
    // Méthodes utilitaires
    public boolean isEnAttente() {
        return StatutAnnulation.EN_ATTENTE.equals(this.statut);
    }
    
    public boolean isApprouvee() {
        return StatutAnnulation.APPROUVEE.equals(this.statut);
    }
    
    public boolean isRejetee() {
        return StatutAnnulation.REJETEE.equals(this.statut);
    }
    
    public boolean isTraitee() {
        return StatutAnnulation.TRAITEE.equals(this.statut);
    }
    
    public String getLibelleStatut() {
        if (statut == null) return "Inconnu";
        
        switch (statut) {
            case EN_ATTENTE:
                return "En attente";
            case APPROUVEE:
                return "Approuvée";
            case REJETEE:
                return "Rejetée";
            case TRAITEE:
                return "Traitée";
            default:
                return statut.toString();
        }
    }
    
    public void calculerMontantRemboursement() {
        if (montantPaye != null && fraisAnnulation != null) {
            this.montantRemboursement = montantPaye.subtract(fraisAnnulation);
            if (this.montantRemboursement.compareTo(BigDecimal.ZERO) < 0) {
                this.montantRemboursement = BigDecimal.ZERO;
            }
        }
    }
    
    public String getNumeroReference() {
        return "ANN" + String.format("%06d", id != null ? id : 0);
    }
    
    @Override
    public String toString() {
        return "DemandeAnnulation{" +
                "id=" + id +
                ", reservationId=" + reservationId +
                ", motif='" + motif + '\'' +
                ", statut=" + statut +
                ", montantRemboursement=" + montantRemboursement +
                ", dateDemande=" + dateDemande +
                '}';
    }
}
