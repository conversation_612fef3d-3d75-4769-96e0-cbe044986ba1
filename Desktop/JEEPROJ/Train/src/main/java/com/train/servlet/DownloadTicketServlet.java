package com.train.servlet;

import com.train.model.Reservation;
import com.train.model.Utilisateur;
import com.train.service.ReservationService;
import com.train.service.PdfService;
import com.train.service.impl.ReservationServiceImpl;
import com.train.service.impl.PdfServiceImpl;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Optional;

/**
 * Servlet pour télécharger les billets en PDF
 */
@WebServlet("/download-ticket")
public class DownloadTicketServlet extends HttpServlet {
    
    private ReservationService reservationService;
    private PdfService pdfService;
    
    @Override
    public void init() throws ServletException {
        reservationService = new ReservationServiceImpl();
        pdfService = new PdfServiceImpl();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("utilisateur") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        Utilisateur utilisateur = (Utilisateur) session.getAttribute("utilisateur");
        String reservationIdStr = request.getParameter("id");
        
        if (reservationIdStr == null || reservationIdStr.trim().isEmpty()) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID de réservation manquant");
            return;
        }
        
        try {
            Long reservationId = Long.parseLong(reservationIdStr);
            Optional<Reservation> reservationOpt = reservationService.trouverParId(reservationId);
            
            if (!reservationOpt.isPresent()) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "Réservation non trouvée");
                return;
            }
            
            Reservation reservation = reservationOpt.get();
            
            // Vérifier que la réservation appartient à l'utilisateur connecté
            // (sauf si c'est un admin)
            if (!utilisateur.getTypeUtilisateur().toString().equals("ADMIN") && 
                !reservation.getUtilisateur().getId().equals(utilisateur.getId())) {
                response.sendError(HttpServletResponse.SC_FORBIDDEN, "Accès non autorisé");
                return;
            }
            
            // Vérifier que la réservation est confirmée
            if (!reservation.getStatut().toString().equals("CONFIRMEE")) {
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, 
                    "Le billet ne peut être téléchargé que pour les réservations confirmées");
                return;
            }
            
            // Générer le PDF
            byte[] pdfContent = pdfService.genererBilletPdf(reservation);
            
            // Configurer la réponse pour le téléchargement
            response.setContentType("application/pdf");
            response.setContentLength(pdfContent.length);
            response.setHeader("Content-Disposition", 
                "attachment; filename=\"billet-" + reservation.getId() + ".pdf\"");
            
            // Écrire le contenu PDF
            response.getOutputStream().write(pdfContent);
            response.getOutputStream().flush();
            
        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID de réservation invalide");
        } catch (Exception e) {
            e.printStackTrace();
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, 
                "Erreur lors de la génération du billet: " + e.getMessage());
        }
    }
}
