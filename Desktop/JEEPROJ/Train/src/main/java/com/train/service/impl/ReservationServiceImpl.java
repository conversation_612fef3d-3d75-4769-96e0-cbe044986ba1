package com.train.service.impl;

import com.train.service.ReservationService;
import com.train.service.VoyageService;
import com.train.service.UtilisateurService;
import com.train.dao.ReservationDAO;
import com.train.dao.impl.ReservationDAOImpl;
import com.train.model.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Implémentation du service de gestion des réservations
 */
public class ReservationServiceImpl implements ReservationService {
    
    private final ReservationDAO reservationDAO;
    private final VoyageService voyageService;
    private final UtilisateurService utilisateurService;
    
    public ReservationServiceImpl() {
        this.reservationDAO = new ReservationDAOImpl();
        this.voyageService = new VoyageServiceImpl();
        this.utilisateurService = new UtilisateurServiceImpl();
    }
    
    public ReservationServiceImpl(ReservationDAO reservationDAO, VoyageService voyageService, 
                                UtilisateurService utilisateurService) {
        this.reservationDAO = reservationDAO;
        this.voyageService = voyageService;
        this.utilisateurService = utilisateurService;
    }
    
    @Override
    public Reservation creerReservation(Long utilisateurId, Long voyageId, int nombrePlaces) {
        // Validation des paramètres
        if (utilisateurId == null || voyageId == null || nombrePlaces <= 0) {
            throw new IllegalArgumentException("Paramètres de réservation invalides");
        }
        
        // Vérifier que l'utilisateur existe
        Optional<Utilisateur> utilisateurOpt = utilisateurService.trouverParId(utilisateurId);
        if (!utilisateurOpt.isPresent()) {
            throw new IllegalArgumentException("Utilisateur non trouvé");
        }
        
        // Vérifier que le voyage existe
        Optional<Voyage> voyageOpt = voyageService.trouverParId(voyageId);
        if (!voyageOpt.isPresent()) {
            throw new IllegalArgumentException("Voyage non trouvé");
        }
        
        Voyage voyage = voyageOpt.get();
        
        // Vérifier que le voyage n'est pas dans le passé
        if (voyage.getDateVoyage().isBefore(LocalDate.now())) {
            throw new IllegalStateException("Impossible de réserver pour un voyage passé");
        }
        
        // Vérifier la disponibilité des places
        if (!voyageService.aPlacesDisponibles(voyageId, nombrePlaces)) {
            throw new IllegalStateException("Pas assez de places disponibles");
        }
        
        // Vérifier si l'utilisateur n'a pas déjà une réservation pour ce voyage
        if (aDejaReservation(utilisateurId, voyageId)) {
            throw new IllegalStateException("Vous avez déjà une réservation pour ce voyage");
        }
        
        // Créer la réservation
        Reservation reservation = new Reservation();
        reservation.setUtilisateur(utilisateurOpt.get());
        reservation.setVoyage(voyage);
        reservation.setNombrePlaces(nombrePlaces);
        reservation.setPrixTotal(voyage.getTrajet().getPrix().multiply(BigDecimal.valueOf(nombrePlaces)));
        reservation.setStatut(StatutReservation.EN_ATTENTE);
        reservation.setDateReservation(LocalDateTime.now());
        reservation.setNumeroReservation(genererNumeroReservation());
        
        // Valider la réservation
        validerReservation(reservation);
        
        // Réserver les places dans le voyage
        if (!voyageService.reserverPlaces(voyageId, nombrePlaces)) {
            throw new IllegalStateException("Échec de la réservation des places");
        }
        
        try {
            // Sauvegarder la réservation
            return reservationDAO.save(reservation);
        } catch (Exception e) {
            // En cas d'erreur, libérer les places
            voyageService.libererPlaces(voyageId, nombrePlaces);
            throw new RuntimeException("Erreur lors de la création de la réservation", e);
        }
    }
    
    @Override
    public boolean confirmerReservation(Long reservationId) {
        if (reservationId == null) {
            return false;
        }
        
        Optional<Reservation> reservationOpt = reservationDAO.findById(reservationId);
        if (!reservationOpt.isPresent()) {
            return false;
        }
        
        Reservation reservation = reservationOpt.get();
        
        // Vérifier que la réservation peut être confirmée
        if (reservation.getStatut() != StatutReservation.EN_ATTENTE) {
            return false;
        }
        
        reservation.setStatut(StatutReservation.CONFIRMEE);
        
        try {
            reservationDAO.update(reservation);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public boolean annulerReservation(Long reservationId, String motifAnnulation) {
        if (reservationId == null) {
            return false;
        }
        
        Optional<Reservation> reservationOpt = reservationDAO.findById(reservationId);
        if (!reservationOpt.isPresent()) {
            return false;
        }
        
        Reservation reservation = reservationOpt.get();
        
        // Vérifier que la réservation peut être annulée
        if (!peutEtreAnnulee(reservationId)) {
            return false;
        }
        
        // Libérer les places dans le voyage
        voyageService.libererPlaces(reservation.getVoyage().getId(), reservation.getNombrePlaces());
        
        // Mettre à jour la réservation
        reservation.setStatut(StatutReservation.ANNULEE);
        reservation.setDateAnnulation(LocalDateTime.now());
        reservation.setMotifAnnulation(motifAnnulation);
        
        try {
            reservationDAO.update(reservation);
            return true;
        } catch (Exception e) {
            // En cas d'erreur, re-réserver les places
            voyageService.reserverPlaces(reservation.getVoyage().getId(), reservation.getNombrePlaces());
            return false;
        }
    }
    
    @Override
    public Optional<Reservation> trouverParId(Long id) {
        if (id == null) {
            return Optional.empty();
        }
        return reservationDAO.findById(id);
    }
    
    @Override
    public Optional<Reservation> trouverParNumero(String numeroReservation) {
        if (numeroReservation == null || numeroReservation.trim().isEmpty()) {
            return Optional.empty();
        }
        return reservationDAO.findByNumero(numeroReservation.trim());
    }
    
    @Override
    public List<Reservation> obtenirReservationsUtilisateur(Long utilisateurId) {
        if (utilisateurId == null) {
            throw new IllegalArgumentException("L'ID utilisateur ne peut pas être null");
        }
        return reservationDAO.findByUtilisateurId(utilisateurId);
    }
    
    @Override
    public List<Reservation> obtenirReservationsActives(Long utilisateurId) {
        if (utilisateurId == null) {
            throw new IllegalArgumentException("L'ID utilisateur ne peut pas être null");
        }
        return reservationDAO.findActiveReservationsByUser(utilisateurId);
    }
    
    @Override
    public List<Reservation> obtenirReservationsVoyage(Long voyageId) {
        if (voyageId == null) {
            throw new IllegalArgumentException("L'ID voyage ne peut pas être null");
        }
        
        Optional<Voyage> voyageOpt = voyageService.trouverParId(voyageId);
        if (!voyageOpt.isPresent()) {
            throw new IllegalArgumentException("Voyage non trouvé");
        }
        
        return reservationDAO.findByVoyage(voyageOpt.get());
    }
    
    @Override
    public List<Reservation> obtenirReservationsParStatut(StatutReservation statut) {
        if (statut == null) {
            throw new IllegalArgumentException("Le statut ne peut pas être null");
        }
        return reservationDAO.findByStatut(statut);
    }
    
    @Override
    public List<Reservation> obtenirReservationsParDate(LocalDate dateVoyage) {
        if (dateVoyage == null) {
            throw new IllegalArgumentException("La date ne peut pas être null");
        }
        return reservationDAO.findByDateVoyage(dateVoyage);
    }
    
    @Override
    public List<Reservation> obtenirReservationsParPeriode(LocalDate dateDebut, LocalDate dateFin) {
        if (dateDebut == null || dateFin == null) {
            throw new IllegalArgumentException("Les dates ne peuvent pas être null");
        }
        if (dateDebut.isAfter(dateFin)) {
            throw new IllegalArgumentException("La date de début doit être antérieure à la date de fin");
        }
        return reservationDAO.findByDateRange(dateDebut, dateFin);
    }
    
    @Override
    public boolean peutEtreAnnulee(Long reservationId) {
        if (reservationId == null) {
            return false;
        }
        
        Optional<Reservation> reservationOpt = reservationDAO.findById(reservationId);
        if (!reservationOpt.isPresent()) {
            return false;
        }
        
        Reservation reservation = reservationOpt.get();
        
        // Vérifier le statut
        if (reservation.getStatut() != StatutReservation.CONFIRMEE && 
            reservation.getStatut() != StatutReservation.EN_ATTENTE) {
            return false;
        }
        
        // Vérifier que le voyage n'est pas dans le passé
        return !reservation.getVoyage().getDateVoyage().isBefore(LocalDate.now());
    }
    
    @Override
    public double calculerMontantTotalUtilisateur(Long utilisateurId) {
        List<Reservation> reservations = obtenirReservationsUtilisateur(utilisateurId);
        return reservations.stream()
                .filter(r -> r.getStatut() == StatutReservation.CONFIRMEE)
                .mapToDouble(r -> r.getPrixTotal().doubleValue())
                .sum();
    }
    
    @Override
    public StatistiquesReservation calculerStatistiques(LocalDate dateDebut, LocalDate dateFin) {
        List<Reservation> reservations = obtenirReservationsParPeriode(dateDebut, dateFin);
        
        long nombreTotal = reservations.size();
        long nombreConfirmees = reservations.stream()
                .mapToLong(r -> r.getStatut() == StatutReservation.CONFIRMEE ? 1 : 0)
                .sum();
        long nombreAnnulees = reservations.stream()
                .mapToLong(r -> r.getStatut() == StatutReservation.ANNULEE ? 1 : 0)
                .sum();
        double chiffreAffaires = reservations.stream()
                .filter(r -> r.getStatut() == StatutReservation.CONFIRMEE)
                .mapToDouble(r -> r.getPrixTotal().doubleValue())
                .sum();
        
        return new StatistiquesReservation(nombreTotal, nombreConfirmees, nombreAnnulees, chiffreAffaires);
    }
    
    @Override
    public boolean aDejaReservation(Long utilisateurId, Long voyageId) {
        if (utilisateurId == null || voyageId == null) {
            return false;
        }
        return reservationDAO.hasReservationForVoyage(utilisateurId, voyageId);
    }
    
    @Override
    public String genererNumeroReservation() {
        return "RES" + System.currentTimeMillis() + String.format("%03d", (int)(Math.random() * 1000));
    }
    
    @Override
    public void validerReservation(Reservation reservation) {
        if (reservation == null) {
            throw new IllegalArgumentException("La réservation ne peut pas être null");
        }
        
        if (reservation.getUtilisateur() == null) {
            throw new IllegalArgumentException("L'utilisateur est requis");
        }
        
        if (reservation.getVoyage() == null) {
            throw new IllegalArgumentException("Le voyage est requis");
        }
        
        if (reservation.getNombrePlaces() <= 0) {
            throw new IllegalArgumentException("Le nombre de places doit être positif");
        }
        
        if (reservation.getNombrePlaces() > 10) {
            throw new IllegalArgumentException("Maximum 10 places par réservation");
        }
        
        if (reservation.getPrixTotal() == null || reservation.getPrixTotal().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Le prix total doit être positif");
        }
        
        if (reservation.getNumeroReservation() == null || reservation.getNumeroReservation().trim().isEmpty()) {
            throw new IllegalArgumentException("Le numéro de réservation est requis");
        }
    }

    @Override
    public List<Reservation> obtenirToutesLesReservations() {
        return reservationDAO.findAll();
    }
}
