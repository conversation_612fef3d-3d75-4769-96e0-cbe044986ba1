package com.train.dao.impl;

import com.train.dao.GareDAO;
import com.train.model.Gare;
import com.train.util.DatabaseConnection;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Implémentation DAO pour les gares
 */
public class GareDAOImpl implements GareDAO {
    
    private static final String INSERT_GARE =
        "INSERT INTO gares (nom, ville, code_gare, adresse, code_postal, latitude, longitude, active) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

    private static final String UPDATE_GARE =
        "UPDATE gares SET nom=?, ville=?, code_gare=?, adresse=?, code_postal=?, latitude=?, longitude=?, active=? WHERE id=?";
    
    private static final String DELETE_GARE = "DELETE FROM gares WHERE id=?";
    
    private static final String FIND_BY_ID = "SELECT * FROM gares WHERE id=?";
    
    private static final String FIND_ALL = "SELECT * FROM gares ORDER BY ville, nom";
    
    private static final String FIND_BY_CODE = "SELECT * FROM gares WHERE code_gare=?";
    
    private static final String FIND_BY_VILLE = "SELECT * FROM gares WHERE ville=? ORDER BY nom";
    
    private static final String FIND_ACTIVE = "SELECT * FROM gares WHERE active=true ORDER BY ville, nom";
    
    private static final String SEARCH_BY_NAME = 
        "SELECT * FROM gares WHERE nom LIKE ? OR ville LIKE ? ORDER BY ville, nom";
    
    private static final String EXISTS_BY_CODE = "SELECT COUNT(*) FROM gares WHERE code_gare=?";
    
    private static final String FIND_ALL_CITIES = "SELECT DISTINCT ville FROM gares WHERE active=true ORDER BY ville";
    
    private static final String COUNT_GARES = "SELECT COUNT(*) FROM gares";
    
    @Override
    public Gare save(Gare gare) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(INSERT_GARE, Statement.RETURN_GENERATED_KEYS)) {
            
            stmt.setString(1, gare.getNom());
            stmt.setString(2, gare.getVille());
            stmt.setString(3, gare.getCodeGare());
            stmt.setString(4, gare.getAdresse());
            stmt.setString(5, gare.getCodePostal());
            if (gare.getLatitude() != null) {
                stmt.setDouble(6, gare.getLatitude());
            } else {
                stmt.setNull(6, Types.DOUBLE);
            }
            if (gare.getLongitude() != null) {
                stmt.setDouble(7, gare.getLongitude());
            } else {
                stmt.setNull(7, Types.DOUBLE);
            }
            stmt.setBoolean(8, gare.isActive());
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Échec de la création de la gare");
            }
            
            try (ResultSet generatedKeys = stmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    gare.setId(generatedKeys.getLong(1));
                }
            }
            
            return gare;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la sauvegarde de la gare", e);
        }
    }
    
    @Override
    public Gare update(Gare gare) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(UPDATE_GARE)) {
            
            stmt.setString(1, gare.getNom());
            stmt.setString(2, gare.getVille());
            stmt.setString(3, gare.getCodeGare());
            stmt.setString(4, gare.getAdresse());
            stmt.setString(5, gare.getCodePostal());
            if (gare.getLatitude() != null) {
                stmt.setDouble(6, gare.getLatitude());
            } else {
                stmt.setNull(6, Types.DOUBLE);
            }
            if (gare.getLongitude() != null) {
                stmt.setDouble(7, gare.getLongitude());
            } else {
                stmt.setNull(7, Types.DOUBLE);
            }
            stmt.setBoolean(8, gare.isActive());
            stmt.setLong(9, gare.getId());
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Gare non trouvée pour la mise à jour");
            }
            
            return gare;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la mise à jour de la gare", e);
        }
    }
    
    @Override
    public boolean deleteById(Long id) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(DELETE_GARE)) {
            
            stmt.setLong(1, id);
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la suppression de la gare", e);
        }
    }
    
    @Override
    public Optional<Gare> findById(Long id) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_BY_ID)) {
            
            stmt.setLong(1, id);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToGare(rs));
                }
            }
            return Optional.empty();
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche de la gare", e);
        }
    }
    
    @Override
    public List<Gare> findAll() {
        List<Gare> gares = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_ALL);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                gares.add(mapResultSetToGare(rs));
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la récupération des gares", e);
        }
        return gares;
    }
    
    @Override
    public Optional<Gare> findByCode(String codeGare) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_BY_CODE)) {
            
            stmt.setString(1, codeGare);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToGare(rs));
                }
            }
            return Optional.empty();
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche par code", e);
        }
    }
    
    @Override
    public List<Gare> findByVille(String ville) {
        List<Gare> gares = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_BY_VILLE)) {
            
            stmt.setString(1, ville);
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    gares.add(mapResultSetToGare(rs));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche par ville", e);
        }
        return gares;
    }
    
    @Override
    public List<Gare> findActiveGares() {
        List<Gare> gares = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_ACTIVE);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                gares.add(mapResultSetToGare(rs));
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche des gares actives", e);
        }
        return gares;
    }
    
    @Override
    public List<Gare> searchByName(String nom) {
        List<Gare> gares = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(SEARCH_BY_NAME)) {
            
            String searchPattern = "%" + nom + "%";
            stmt.setString(1, searchPattern);
            stmt.setString(2, searchPattern);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    gares.add(mapResultSetToGare(rs));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche par nom", e);
        }
        return gares;
    }
    
    @Override
    public boolean existsByCode(String codeGare) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(EXISTS_BY_CODE)) {
            
            stmt.setString(1, codeGare);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
            return false;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la vérification du code", e);
        }
    }
    
    @Override
    public List<String> findAllCities() {
        List<String> villes = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_ALL_CITIES);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                villes.add(rs.getString("ville"));
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la récupération des villes", e);
        }
        return villes;
    }
    
    @Override
    public boolean existsById(Long id) {
        return findById(id).isPresent();
    }
    
    @Override
    public long count() {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(COUNT_GARES);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                return rs.getLong(1);
            }
            return 0;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors du comptage des gares", e);
        }
    }
    
    private Gare mapResultSetToGare(ResultSet rs) throws SQLException {
        Gare gare = new Gare();
        gare.setId(rs.getLong("id"));
        gare.setNom(rs.getString("nom"));
        gare.setVille(rs.getString("ville"));
        gare.setCodeGare(rs.getString("code_gare"));
        gare.setAdresse(rs.getString("adresse"));
        gare.setCodePostal(rs.getString("code_postal"));

        // Gérer les coordonnées GPS (peuvent être nulles)
        Double latitude = rs.getDouble("latitude");
        if (!rs.wasNull()) {
            gare.setLatitude(latitude);
        }

        Double longitude = rs.getDouble("longitude");
        if (!rs.wasNull()) {
            gare.setLongitude(longitude);
        }

        gare.setActive(rs.getBoolean("active"));

        // Gérer les dates si elles existent dans la base
        try {
            Timestamp dateCreation = rs.getTimestamp("date_creation");
            if (dateCreation != null) {
                gare.setDateCreation(dateCreation.toLocalDateTime());
            }

            Timestamp dateModification = rs.getTimestamp("date_modification");
            if (dateModification != null) {
                gare.setDateModification(dateModification.toLocalDateTime());
            }
        } catch (SQLException e) {
            // Les colonnes de date n'existent peut-être pas dans toutes les versions de la base
        }

        return gare;
    }
}
