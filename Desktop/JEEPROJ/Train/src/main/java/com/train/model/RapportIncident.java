package com.train.model;

import java.time.LocalDateTime;

/**
 * Modèle pour les rapports d'incidents
 */
public class RapportIncident {
    
    private Long id;
    private Long employeId;
    private Long voyageId;
    private TypeIncident typeIncident;
    private String titre;
    private String description;
    private GraviteIncident gravite;
    private LocalDateTime dateIncident;
    private LocalDateTime dateRapport;
    private StatutIncident statut;
    private String actionsPrises;
    
    // Relations
    private Utilisateur employe;
    private Voyage voyage;
    
    public enum TypeIncident {
        RETARD("Retard"),
        PANNE("Panne technique"),
        ACCIDENT("Accident"),
        SECURITE("Problème de sécurité"),
        AUTRE("Autre");
        
        private final String libelle;
        
        TypeIncident(String libelle) {
            this.libelle = libelle;
        }
        
        public String getLibelle() {
            return libelle;
        }
    }
    
    public enum GraviteIncident {
        FAIBLE("Faible"),
        MOYENNE("Moyenne"),
        ELEVEE("Élevée"),
        CRITIQUE("Critique");
        
        private final String libelle;
        
        GraviteIncident(String libelle) {
            this.libelle = libelle;
        }
        
        public String getLibelle() {
            return libelle;
        }
        
        public String getCssClass() {
            switch (this) {
                case FAIBLE: return "success";
                case MOYENNE: return "warning";
                case ELEVEE: return "danger";
                case CRITIQUE: return "dark";
                default: return "secondary";
            }
        }
    }
    
    public enum StatutIncident {
        OUVERT("Ouvert"),
        EN_COURS("En cours"),
        RESOLU("Résolu"),
        FERME("Fermé");
        
        private final String libelle;
        
        StatutIncident(String libelle) {
            this.libelle = libelle;
        }
        
        public String getLibelle() {
            return libelle;
        }
        
        public String getCssClass() {
            switch (this) {
                case OUVERT: return "danger";
                case EN_COURS: return "warning";
                case RESOLU: return "info";
                case FERME: return "success";
                default: return "secondary";
            }
        }
    }
    
    // Constructeurs
    public RapportIncident() {}
    
    public RapportIncident(Long employeId, TypeIncident typeIncident, String titre, 
                          String description, GraviteIncident gravite, LocalDateTime dateIncident) {
        this.employeId = employeId;
        this.typeIncident = typeIncident;
        this.titre = titre;
        this.description = description;
        this.gravite = gravite;
        this.dateIncident = dateIncident;
        this.dateRapport = LocalDateTime.now();
        this.statut = StatutIncident.OUVERT;
    }
    
    // Getters et Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getEmployeId() {
        return employeId;
    }
    
    public void setEmployeId(Long employeId) {
        this.employeId = employeId;
    }
    
    public Long getVoyageId() {
        return voyageId;
    }
    
    public void setVoyageId(Long voyageId) {
        this.voyageId = voyageId;
    }
    
    public TypeIncident getTypeIncident() {
        return typeIncident;
    }
    
    public void setTypeIncident(TypeIncident typeIncident) {
        this.typeIncident = typeIncident;
    }
    
    public String getTitre() {
        return titre;
    }
    
    public void setTitre(String titre) {
        this.titre = titre;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public GraviteIncident getGravite() {
        return gravite;
    }
    
    public void setGravite(GraviteIncident gravite) {
        this.gravite = gravite;
    }
    
    public LocalDateTime getDateIncident() {
        return dateIncident;
    }
    
    public void setDateIncident(LocalDateTime dateIncident) {
        this.dateIncident = dateIncident;
    }
    
    public LocalDateTime getDateRapport() {
        return dateRapport;
    }
    
    public void setDateRapport(LocalDateTime dateRapport) {
        this.dateRapport = dateRapport;
    }
    
    public StatutIncident getStatut() {
        return statut;
    }
    
    public void setStatut(StatutIncident statut) {
        this.statut = statut;
    }
    
    public String getActionsPrises() {
        return actionsPrises;
    }
    
    public void setActionsPrises(String actionsPrises) {
        this.actionsPrises = actionsPrises;
    }
    
    public Utilisateur getEmploye() {
        return employe;
    }
    
    public void setEmploye(Utilisateur employe) {
        this.employe = employe;
    }
    
    public Voyage getVoyage() {
        return voyage;
    }
    
    public void setVoyage(Voyage voyage) {
        this.voyage = voyage;
    }
    
    // Méthodes utilitaires
    
    /**
     * Marque l'incident comme en cours
     */
    public void marquerEnCours() {
        this.statut = StatutIncident.EN_COURS;
    }
    
    /**
     * Marque l'incident comme résolu
     */
    public void marquerResolu(String actionsPrises) {
        this.statut = StatutIncident.RESOLU;
        this.actionsPrises = actionsPrises;
    }
    
    /**
     * Ferme l'incident
     */
    public void fermer() {
        this.statut = StatutIncident.FERME;
    }
    
    /**
     * Vérifie si l'incident est ouvert
     */
    public boolean isOuvert() {
        return statut == StatutIncident.OUVERT || statut == StatutIncident.EN_COURS;
    }
    
    /**
     * Obtient le nom complet de l'employé rapporteur
     */
    public String getNomCompletEmploye() {
        if (employe != null) {
            return employe.getPrenom() + " " + employe.getNom();
        }
        return "Employé inconnu";
    }
    
    /**
     * Obtient un résumé court de l'incident
     */
    public String getResume() {
        if (titre != null && titre.length() > 50) {
            return titre.substring(0, 47) + "...";
        }
        return titre;
    }
    
    @Override
    public String toString() {
        return "RapportIncident{" +
                "id=" + id +
                ", typeIncident=" + typeIncident +
                ", titre='" + titre + '\'' +
                ", gravite=" + gravite +
                ", statut=" + statut +
                '}';
    }
}
