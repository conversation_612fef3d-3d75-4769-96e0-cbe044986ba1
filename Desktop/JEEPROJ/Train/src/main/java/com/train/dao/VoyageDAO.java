package com.train.dao;

import com.train.model.Voyage;
import com.train.model.Trajet;
import com.train.model.StatutVoyage;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Interface DAO pour les opérations sur les voyages
 */
public interface VoyageDAO extends BaseDAO<Voyage, Long> {
    
    /**
     * Trouve un voyage par trajet et date
     * @param trajet le trajet
     * @param dateVoyage la date du voyage
     * @return Optional contenant le voyage si trouvé
     */
    Optional<Voyage> findByTrajetAndDate(Trajet trajet, LocalDate dateVoyage);
    
    /**
     * Trouve un voyage par trajet ID et date
     * @param trajetId l'ID du trajet
     * @param dateVoyage la date du voyage
     * @return Optional contenant le voyage si trouvé
     */
    Optional<Voyage> findByTrajetIdAndDate(Long trajetId, LocalDate dateVoyage);
    
    /**
     * Trouve les voyages par trajet
     * @param trajet le trajet
     * @return liste des voyages pour ce trajet
     */
    List<Voyage> findByTrajet(Trajet trajet);
    
    /**
     * Trouve les voyages par date
     * @param dateVoyage la date du voyage
     * @return liste des voyages à cette date
     */
    List<Voyage> findByDate(LocalDate dateVoyage);
    
    /**
     * Trouve les voyages entre deux dates
     * @param dateDebut date de début
     * @param dateFin date de fin
     * @return liste des voyages dans cette période
     */
    List<Voyage> findByDateRange(LocalDate dateDebut, LocalDate dateFin);
    
    /**
     * Trouve les voyages par statut
     * @param statut le statut du voyage
     * @return liste des voyages avec ce statut
     */
    List<Voyage> findByStatut(StatutVoyage statut);
    
    /**
     * Trouve les voyages disponibles (avec places)
     * @param dateVoyage la date du voyage
     * @return liste des voyages disponibles
     */
    List<Voyage> findAvailableVoyages(LocalDate dateVoyage);
    
    /**
     * Recherche les voyages disponibles entre deux villes
     * @param villeDepart ville de départ
     * @param villeArrivee ville d'arrivée
     * @param dateVoyage date du voyage
     * @return liste des voyages disponibles
     */
    List<Voyage> searchAvailableVoyages(String villeDepart, String villeArrivee, LocalDate dateVoyage);
    
    /**
     * Met à jour les places disponibles d'un voyage
     * @param voyageId l'ID du voyage
     * @param placesReservees nombre de places à réserver
     * @return true si la mise à jour a réussi
     */
    boolean updatePlaces(Long voyageId, int placesReservees);
    
    /**
     * Libère des places d'un voyage
     * @param voyageId l'ID du voyage
     * @param placesALiberer nombre de places à libérer
     * @return true si la mise à jour a réussi
     */
    boolean liberePlaces(Long voyageId, int placesALiberer);
}
