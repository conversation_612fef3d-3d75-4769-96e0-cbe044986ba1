package com.train.model;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * Modèle pour les promotions et codes promo
 */
public class Promotion {
    
    private Long id;
    private String nom;
    private String description;
    private TypePromotion typePromotion;
    private BigDecimal valeurReduction;
    private String codePromo;
    private LocalDate dateDebut;
    private LocalDate dateFin;
    private String conditionsUtilisation;
    private Integer nombreUtilisationsMax;
    private Integer nombreUtilisationsActuelles;
    private boolean actif;
    private Long trajetId;
    private TypeUtilisateur typeUtilisateur;
    
    // Trajet associé (pour les promotions spécifiques)
    private Trajet trajet;
    
    public enum TypePromotion {
        POURCENTAGE,
        MONTANT_FIXE,
        FAMILLE,
        FIDELITE
    }
    
    // Constructeurs
    public Promotion() {}
    
    public Promotion(String nom, TypePromotion typePromotion, BigDecimal valeurReduction, 
                    LocalDate dateDebut, LocalDate dateFin) {
        this.nom = nom;
        this.typePromotion = typePromotion;
        this.valeurReduction = valeurReduction;
        this.dateDebut = dateDebut;
        this.dateFin = dateFin;
        this.actif = true;
        this.nombreUtilisationsActuelles = 0;
    }
    
    // Getters et Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getNom() {
        return nom;
    }
    
    public void setNom(String nom) {
        this.nom = nom;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public TypePromotion getTypePromotion() {
        return typePromotion;
    }
    
    public void setTypePromotion(TypePromotion typePromotion) {
        this.typePromotion = typePromotion;
    }
    
    public BigDecimal getValeurReduction() {
        return valeurReduction;
    }
    
    public void setValeurReduction(BigDecimal valeurReduction) {
        this.valeurReduction = valeurReduction;
    }
    
    public String getCodePromo() {
        return codePromo;
    }
    
    public void setCodePromo(String codePromo) {
        this.codePromo = codePromo;
    }
    
    public LocalDate getDateDebut() {
        return dateDebut;
    }
    
    public void setDateDebut(LocalDate dateDebut) {
        this.dateDebut = dateDebut;
    }
    
    public LocalDate getDateFin() {
        return dateFin;
    }
    
    public void setDateFin(LocalDate dateFin) {
        this.dateFin = dateFin;
    }
    
    public String getConditionsUtilisation() {
        return conditionsUtilisation;
    }
    
    public void setConditionsUtilisation(String conditionsUtilisation) {
        this.conditionsUtilisation = conditionsUtilisation;
    }
    
    public Integer getNombreUtilisationsMax() {
        return nombreUtilisationsMax;
    }
    
    public void setNombreUtilisationsMax(Integer nombreUtilisationsMax) {
        this.nombreUtilisationsMax = nombreUtilisationsMax;
    }
    
    public Integer getNombreUtilisationsActuelles() {
        return nombreUtilisationsActuelles;
    }
    
    public void setNombreUtilisationsActuelles(Integer nombreUtilisationsActuelles) {
        this.nombreUtilisationsActuelles = nombreUtilisationsActuelles;
    }
    
    public boolean isActif() {
        return actif;
    }
    
    public void setActif(boolean actif) {
        this.actif = actif;
    }
    
    public Long getTrajetId() {
        return trajetId;
    }
    
    public void setTrajetId(Long trajetId) {
        this.trajetId = trajetId;
    }
    
    public TypeUtilisateur getTypeUtilisateur() {
        return typeUtilisateur;
    }
    
    public void setTypeUtilisateur(TypeUtilisateur typeUtilisateur) {
        this.typeUtilisateur = typeUtilisateur;
    }
    
    public Trajet getTrajet() {
        return trajet;
    }
    
    public void setTrajet(Trajet trajet) {
        this.trajet = trajet;
    }
    
    // Méthodes utilitaires
    
    /**
     * Vérifie si la promotion est valide à la date donnée
     */
    public boolean isValide(LocalDate date) {
        return actif && 
               (dateDebut == null || !date.isBefore(dateDebut)) &&
               (dateFin == null || !date.isAfter(dateFin)) &&
               (nombreUtilisationsMax == null || nombreUtilisationsMax == 0 || 
                nombreUtilisationsActuelles < nombreUtilisationsMax);
    }
    
    /**
     * Calcule le montant de la réduction pour un prix donné
     */
    public BigDecimal calculerReduction(BigDecimal prixOriginal) {
        if (!isValide(LocalDate.now())) {
            return BigDecimal.ZERO;
        }
        
        switch (typePromotion) {
            case POURCENTAGE:
                return prixOriginal.multiply(valeurReduction).divide(BigDecimal.valueOf(100));
            case MONTANT_FIXE:
                return valeurReduction.min(prixOriginal);
            case FAMILLE:
                // Logique spécifique pour les réductions famille
                return prixOriginal.multiply(valeurReduction).divide(BigDecimal.valueOf(100));
            case FIDELITE:
                // Logique spécifique pour les réductions fidélité
                return prixOriginal.multiply(valeurReduction).divide(BigDecimal.valueOf(100));
            default:
                return BigDecimal.ZERO;
        }
    }
    
    /**
     * Incrémente le nombre d'utilisations
     */
    public void incrementerUtilisations() {
        if (nombreUtilisationsActuelles == null) {
            nombreUtilisationsActuelles = 0;
        }
        nombreUtilisationsActuelles++;
    }
    
    @Override
    public String toString() {
        return "Promotion{" +
                "id=" + id +
                ", nom='" + nom + '\'' +
                ", typePromotion=" + typePromotion +
                ", valeurReduction=" + valeurReduction +
                ", codePromo='" + codePromo + '\'' +
                ", actif=" + actif +
                '}';
    }
}
