package com.train.service.impl;

import com.train.dao.TrajetDAO;
import com.train.dao.GareDAO;
import com.train.dao.impl.TrajetDAOImpl;
import com.train.dao.impl.GareDAOImpl;
import com.train.model.Trajet;
import com.train.model.Gare;
import com.train.service.TrajetService;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;

/**
 * Implémentation du service pour la gestion des trajets
 */
public class TrajetServiceImpl implements TrajetService {
    
    private final TrajetDAO trajetDAO;
    private final GareDAO gareDAO;
    
    public TrajetServiceImpl() {
        this.trajetDAO = new TrajetDAOImpl();
        this.gareDAO = new GareDAOImpl();
    }
    
    @Override
    public Trajet creerTrajet(Trajet trajet) {
        validerTrajet(trajet);
        
        // Vérifier que les gares existent
        if (trajet.getGareDepart() == null || trajet.getGareDepart().getId() == null) {
            throw new IllegalArgumentException("La gare de départ est requise");
        }
        if (trajet.getGareArrivee() == null || trajet.getGareArrivee().getId() == null) {
            throw new IllegalArgumentException("La gare d'arrivée est requise");
        }
        
        Optional<Gare> gareDepart = gareDAO.findById(trajet.getGareDepart().getId());
        Optional<Gare> gareArrivee = gareDAO.findById(trajet.getGareArrivee().getId());
        
        if (!gareDepart.isPresent()) {
            throw new IllegalArgumentException("La gare de départ n'existe pas");
        }
        if (!gareArrivee.isPresent()) {
            throw new IllegalArgumentException("La gare d'arrivée n'existe pas");
        }
        
        trajet.setGareDepart(gareDepart.get());
        trajet.setGareArrivee(gareArrivee.get());
        
        // Définir des valeurs par défaut si nécessaire
        if (trajet.getTypeTrain() == null || trajet.getTypeTrain().trim().isEmpty()) {
            trajet.setTypeTrain("TER");
        }
        
        return trajetDAO.save(trajet);
    }
    
    @Override
    public Trajet mettreAJourTrajet(Trajet trajet) {
        if (trajet.getId() == null) {
            throw new IllegalArgumentException("L'ID du trajet est requis pour la mise à jour");
        }
        
        validerTrajet(trajet);
        
        // Vérifier que le trajet existe
        Optional<Trajet> trajetExistant = trajetDAO.findById(trajet.getId());
        if (!trajetExistant.isPresent()) {
            throw new IllegalArgumentException("Le trajet à modifier n'existe pas");
        }
        
        return trajetDAO.update(trajet);
    }
    
    @Override
    public boolean supprimerTrajet(Long id) {
        if (id == null) {
            return false;
        }
        
        Optional<Trajet> trajet = trajetDAO.findById(id);
        if (!trajet.isPresent()) {
            return false;
        }
        
        return trajetDAO.deleteById(id);
    }
    
    @Override
    public Optional<Trajet> trouverParId(Long id) {
        if (id == null) {
            return Optional.empty();
        }
        return trajetDAO.findById(id);
    }
    
    @Override
    public List<Trajet> obtenirTousLesTrajets() {
        return trajetDAO.findAll();
    }
    
    @Override
    public List<Trajet> obtenirTrajetsActifs() {
        return trajetDAO.findByActif(true);
    }
    
    @Override
    public List<Trajet> rechercherTrajets(Long gareDepartId, Long gareArriveeId) {
        if (gareDepartId == null || gareArriveeId == null) {
            return List.of();
        }
        return trajetDAO.findByGareIds(gareDepartId, gareArriveeId);
    }
    
    @Override
    public boolean changerStatutTrajet(Long id) {
        if (id == null) {
            return false;
        }
        
        Optional<Trajet> trajetOpt = trajetDAO.findById(id);
        if (!trajetOpt.isPresent()) {
            return false;
        }
        
        Trajet trajet = trajetOpt.get();
        trajet.setActif(!trajet.isActif());
        
        try {
            trajetDAO.update(trajet);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public void validerTrajet(Trajet trajet) {
        if (trajet == null) {
            throw new IllegalArgumentException("Le trajet ne peut pas être null");
        }
        
        // Validation des gares
        if (trajet.getGareDepart() == null) {
            throw new IllegalArgumentException("La gare de départ est requise");
        }
        if (trajet.getGareArrivee() == null) {
            throw new IllegalArgumentException("La gare d'arrivée est requise");
        }
        if (trajet.getGareDepart().getId() != null && 
            trajet.getGareArrivee().getId() != null &&
            trajet.getGareDepart().getId().equals(trajet.getGareArrivee().getId())) {
            throw new IllegalArgumentException("La gare de départ et d'arrivée ne peuvent pas être identiques");
        }
        
        // Validation des horaires
        if (trajet.getHeureDepart() == null) {
            throw new IllegalArgumentException("L'heure de départ est requise");
        }
        if (trajet.getHeureArrivee() == null) {
            throw new IllegalArgumentException("L'heure d'arrivée est requise");
        }
        
        // Validation du prix
        if (trajet.getPrix() == null) {
            throw new IllegalArgumentException("Le prix est requis");
        }
        if (trajet.getPrix().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Le prix doit être positif");
        }
        if (trajet.getPrix().compareTo(new BigDecimal("1000")) > 0) {
            throw new IllegalArgumentException("Le prix ne peut pas dépasser 1000€");
        }
        
        // Validation du nombre de places
        if (trajet.getNombrePlaces() <= 0) {
            throw new IllegalArgumentException("Le nombre de places doit être positif");
        }
        if (trajet.getNombrePlaces() > 1000) {
            throw new IllegalArgumentException("Le nombre de places ne peut pas dépasser 1000");
        }
        
        // Validation du type de train
        if (trajet.getTypeTrain() != null && !trajet.getTypeTrain().trim().isEmpty()) {
            String type = trajet.getTypeTrain().toUpperCase();
            if (!type.equals("TGV") && !type.equals("INTERCITES") && 
                !type.equals("TER") && !type.equals("OUIGO")) {
                throw new IllegalArgumentException("Type de train invalide. Types autorisés : TGV, INTERCITES, TER, OUIGO");
            }
        }
    }
    
    @Override
    public long compterTrajets() {
        return trajetDAO.count();
    }
    
    @Override
    public long compterTrajetsActifs() {
        return trajetDAO.findByActif(true).size();
    }
    
    @Override
    public long compterTrajetsInactifs() {
        return trajetDAO.findByActif(false).size();
    }
}
