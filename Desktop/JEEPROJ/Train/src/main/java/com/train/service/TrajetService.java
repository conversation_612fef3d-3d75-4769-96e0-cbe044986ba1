package com.train.service;

import com.train.model.Trajet;
import com.train.model.Gare;
import java.util.List;
import java.util.Optional;

/**
 * Interface du service pour la gestion des trajets
 */
public interface TrajetService {
    
    /**
     * Créer un nouveau trajet
     * @param trajet le trajet à créer
     * @return le trajet créé avec son ID
     */
    Trajet creerTrajet(Trajet trajet);
    
    /**
     * Mettre à jour un trajet existant
     * @param trajet le trajet à mettre à jour
     * @return le trajet mis à jour
     */
    Trajet mettreAJourTrajet(Trajet trajet);
    
    /**
     * Supprimer un trajet
     * @param id l'ID du trajet à supprimer
     * @return true si la suppression a réussi
     */
    boolean supprimerTrajet(Long id);
    
    /**
     * Trouver un trajet par son ID
     * @param id l'ID du trajet
     * @return le trajet trouvé ou Optional.empty()
     */
    Optional<Trajet> trouverParId(Long id);
    
    /**
     * Obtenir tous les trajets
     * @return la liste de tous les trajets
     */
    List<Trajet> obtenirTousLesTrajets();
    
    /**
     * Obtenir les trajets actifs seulement
     * @return la liste des trajets actifs
     */
    List<Trajet> obtenirTrajetsActifs();
    
    /**
     * Rechercher des trajets entre deux gares
     * @param gareDepartId l'ID de la gare de départ
     * @param gareArriveeId l'ID de la gare d'arrivée
     * @return la liste des trajets trouvés
     */
    List<Trajet> rechercherTrajets(Long gareDepartId, Long gareArriveeId);
    
    /**
     * Changer le statut d'un trajet (actif/inactif)
     * @param id l'ID du trajet
     * @return true si le changement a réussi
     */
    boolean changerStatutTrajet(Long id);
    
    /**
     * Valider un trajet avant sauvegarde
     * @param trajet le trajet à valider
     * @throws IllegalArgumentException si le trajet n'est pas valide
     */
    void validerTrajet(Trajet trajet);
    
    /**
     * Obtenir le nombre total de trajets
     * @return le nombre total de trajets
     */
    long compterTrajets();
    
    /**
     * Obtenir le nombre de trajets actifs
     * @return le nombre de trajets actifs
     */
    long compterTrajetsActifs();
    
    /**
     * Obtenir le nombre de trajets inactifs
     * @return le nombre de trajets inactifs
     */
    long compterTrajetsInactifs();
}
