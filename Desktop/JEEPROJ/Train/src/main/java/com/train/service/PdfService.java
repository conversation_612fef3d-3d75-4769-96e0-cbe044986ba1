package com.train.service;

import com.train.model.Reservation;
import java.io.ByteArrayOutputStream;

/**
 * Interface pour la génération de documents PDF
 */
public interface PdfService {
    
    /**
     * Génère un billet PDF pour une réservation
     * @param reservation la réservation pour laquelle générer le billet
     * @return le contenu PDF sous forme de tableau d'octets
     * @throws Exception si une erreur survient lors de la génération
     */
    byte[] genererBilletPdf(Reservation reservation) throws Exception;
    
    /**
     * Génère un rapport PDF des réservations
     * @param reservations liste des réservations à inclure
     * @param titre titre du rapport
     * @return le contenu PDF sous forme de tableau d'octets
     * @throws Exception si une erreur survient lors de la génération
     */
    byte[] genererRapportReservations(java.util.List<Reservation> reservations, String titre) throws Exception;
}
