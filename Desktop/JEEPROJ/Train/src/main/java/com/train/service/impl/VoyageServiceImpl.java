package com.train.service.impl;

import com.train.service.VoyageService;
import com.train.dao.VoyageDAO;
import com.train.dao.TrajetDAO;
import com.train.dao.impl.VoyageDAOImpl;
import com.train.dao.impl.TrajetDAOImpl;
import com.train.model.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Implémentation du service de gestion des voyages
 */
public class VoyageServiceImpl implements VoyageService {
    
    private final VoyageDAO voyageDAO;
    private final TrajetDAO trajetDAO;
    
    public VoyageServiceImpl() {
        this.voyageDAO = new VoyageDAOImpl();
        this.trajetDAO = new TrajetDAOImpl();
    }
    
    public VoyageServiceImpl(VoyageDAO voyageDAO, TrajetDAO trajetDAO) {
        this.voyageDAO = voyageDAO;
        this.trajetDAO = trajetDAO;
    }
    
    @Override
    public Voyage creerVoyage(Voyage voyage) {
        validerVoyage(voyage);
        
        // Vérifier qu'un voyage n'existe pas déjà pour ce trajet à cette date
        Optional<Voyage> existant = voyageDAO.findByTrajetIdAndDate(
            voyage.getTrajet().getId(), voyage.getDateVoyage());
        
        if (existant.isPresent()) {
            throw new IllegalStateException("Un voyage existe déjà pour ce trajet à cette date");
        }
        
        // Définir les valeurs par défaut
        voyage.setDateCreation(LocalDateTime.now());
        voyage.setStatut(StatutVoyage.PROGRAMME);
        voyage.setPlacesReservees(0);
        
        // Si les places disponibles ne sont pas définies, utiliser celles du trajet
        if (voyage.getPlacesDisponibles() == 0) {
            voyage.setPlacesDisponibles(voyage.getTrajet().getNombrePlaces());
        }
        
        return voyageDAO.save(voyage);
    }
    
    @Override
    public Voyage mettreAJourVoyage(Voyage voyage) {
        if (voyage == null || voyage.getId() == null) {
            throw new IllegalArgumentException("Voyage invalide pour la mise à jour");
        }
        
        // Vérifier que le voyage existe
        Optional<Voyage> existant = voyageDAO.findById(voyage.getId());
        if (!existant.isPresent()) {
            throw new IllegalArgumentException("Voyage non trouvé");
        }
        
        validerVoyage(voyage);
        return voyageDAO.update(voyage);
    }
    
    @Override
    public Optional<Voyage> trouverParId(Long id) {
        if (id == null) {
            return Optional.empty();
        }
        return voyageDAO.findById(id);
    }
    
    @Override
    public Optional<Voyage> trouverParTrajetEtDate(Long trajetId, LocalDate dateVoyage) {
        if (trajetId == null || dateVoyage == null) {
            return Optional.empty();
        }
        return voyageDAO.findByTrajetIdAndDate(trajetId, dateVoyage);
    }
    
    @Override
    public List<Voyage> rechercherVoyagesDisponibles(String villeDepart, String villeArrivee, LocalDate dateVoyage) {
        if (villeDepart == null || villeArrivee == null || dateVoyage == null) {
            throw new IllegalArgumentException("Tous les paramètres de recherche sont requis");
        }
        
        if (dateVoyage.isBefore(LocalDate.now())) {
            throw new IllegalArgumentException("Impossible de rechercher des voyages dans le passé");
        }
        
        return voyageDAO.searchAvailableVoyages(villeDepart.trim(), villeArrivee.trim(), dateVoyage);
    }
    
    @Override
    public List<Voyage> obtenirVoyagesParDate(LocalDate dateVoyage) {
        if (dateVoyage == null) {
            throw new IllegalArgumentException("La date ne peut pas être null");
        }
        return voyageDAO.findByDate(dateVoyage);
    }
    
    @Override
    public List<Voyage> obtenirVoyagesParPeriode(LocalDate dateDebut, LocalDate dateFin) {
        if (dateDebut == null || dateFin == null) {
            throw new IllegalArgumentException("Les dates ne peuvent pas être null");
        }
        if (dateDebut.isAfter(dateFin)) {
            throw new IllegalArgumentException("La date de début doit être antérieure à la date de fin");
        }
        return voyageDAO.findByDateRange(dateDebut, dateFin);
    }
    
    @Override
    public List<Voyage> obtenirVoyagesParStatut(StatutVoyage statut) {
        if (statut == null) {
            throw new IllegalArgumentException("Le statut ne peut pas être null");
        }
        return voyageDAO.findByStatut(statut);
    }
    
    @Override
    public boolean reserverPlaces(Long voyageId, int nombrePlaces) {
        if (voyageId == null || nombrePlaces <= 0) {
            return false;
        }
        
        // Vérifier la disponibilité
        if (!aPlacesDisponibles(voyageId, nombrePlaces)) {
            throw new IllegalStateException("Pas assez de places disponibles");
        }
        
        return voyageDAO.updatePlaces(voyageId, nombrePlaces);
    }
    
    @Override
    public boolean libererPlaces(Long voyageId, int nombrePlaces) {
        if (voyageId == null || nombrePlaces <= 0) {
            return false;
        }
        
        return voyageDAO.liberePlaces(voyageId, nombrePlaces);
    }
    
    @Override
    public boolean changerStatutVoyage(Long voyageId, StatutVoyage nouveauStatut) {
        if (voyageId == null || nouveauStatut == null) {
            return false;
        }
        
        Optional<Voyage> voyageOpt = voyageDAO.findById(voyageId);
        if (!voyageOpt.isPresent()) {
            return false;
        }
        
        Voyage voyage = voyageOpt.get();
        voyage.setStatut(nouveauStatut);
        
        try {
            voyageDAO.update(voyage);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public int genererVoyagesPourTrajet(Long trajetId, LocalDate dateDebut, LocalDate dateFin) {
        if (trajetId == null || dateDebut == null || dateFin == null) {
            throw new IllegalArgumentException("Tous les paramètres sont requis");
        }
        
        if (dateDebut.isAfter(dateFin)) {
            throw new IllegalArgumentException("La date de début doit être antérieure à la date de fin");
        }
        
        // Vérifier que le trajet existe
        Optional<Trajet> trajetOpt = trajetDAO.findById(trajetId);
        if (!trajetOpt.isPresent()) {
            throw new IllegalArgumentException("Trajet non trouvé");
        }
        
        Trajet trajet = trajetOpt.get();
        int voyagesCrees = 0;
        
        LocalDate dateCourante = dateDebut;
        while (!dateCourante.isAfter(dateFin)) {
            // Vérifier si un voyage n'existe pas déjà pour cette date
            Optional<Voyage> existant = voyageDAO.findByTrajetIdAndDate(trajetId, dateCourante);
            
            if (!existant.isPresent()) {
                Voyage voyage = new Voyage(trajet, dateCourante);
                try {
                    voyageDAO.save(voyage);
                    voyagesCrees++;
                } catch (Exception e) {
                    // Log l'erreur mais continuer
                    System.err.println("Erreur lors de la création du voyage pour " + dateCourante + ": " + e.getMessage());
                }
            }
            
            dateCourante = dateCourante.plusDays(1);
        }
        
        return voyagesCrees;
    }
    
    @Override
    public boolean aPlacesDisponibles(Long voyageId, int nombrePlaces) {
        if (voyageId == null || nombrePlaces <= 0) {
            return false;
        }
        
        Optional<Voyage> voyageOpt = voyageDAO.findById(voyageId);
        if (!voyageOpt.isPresent()) {
            return false;
        }
        
        Voyage voyage = voyageOpt.get();
        return voyage.hasPlacesDisponibles(nombrePlaces) && 
               voyage.getStatut() == StatutVoyage.PROGRAMME;
    }
    
    @Override
    public double obtenirTauxOccupation(Long voyageId) {
        if (voyageId == null) {
            return 0.0;
        }
        
        Optional<Voyage> voyageOpt = voyageDAO.findById(voyageId);
        if (!voyageOpt.isPresent()) {
            return 0.0;
        }
        
        return voyageOpt.get().getTauxOccupation();
    }
    
    @Override
    public boolean supprimerVoyage(Long voyageId) {
        if (voyageId == null) {
            return false;
        }
        
        Optional<Voyage> voyageOpt = voyageDAO.findById(voyageId);
        if (!voyageOpt.isPresent()) {
            return false;
        }
        
        Voyage voyage = voyageOpt.get();
        
        // Vérifier qu'il n'y a pas de réservations
        if (voyage.getPlacesReservees() > 0) {
            throw new IllegalStateException("Impossible de supprimer un voyage avec des réservations");
        }
        
        // Vérifier que le voyage n'est pas dans le passé ou en cours
        if (voyage.getDateVoyage().isBefore(LocalDate.now()) || 
            voyage.getStatut() == StatutVoyage.EN_COURS) {
            throw new IllegalStateException("Impossible de supprimer un voyage passé ou en cours");
        }
        
        return voyageDAO.deleteById(voyageId);
    }
    
    @Override
    public void validerVoyage(Voyage voyage) {
        if (voyage == null) {
            throw new IllegalArgumentException("Le voyage ne peut pas être null");
        }
        
        if (voyage.getTrajet() == null) {
            throw new IllegalArgumentException("Le trajet est requis");
        }
        
        if (voyage.getDateVoyage() == null) {
            throw new IllegalArgumentException("La date du voyage est requise");
        }
        
        if (voyage.getDateVoyage().isBefore(LocalDate.now())) {
            throw new IllegalArgumentException("La date du voyage ne peut pas être dans le passé");
        }
        
        if (voyage.getPlacesDisponibles() < 0) {
            throw new IllegalArgumentException("Le nombre de places disponibles ne peut pas être négatif");
        }
        
        if (voyage.getPlacesReservees() < 0) {
            throw new IllegalArgumentException("Le nombre de places réservées ne peut pas être négatif");
        }
        
        if (voyage.getPlacesDisponibles() + voyage.getPlacesReservees() > voyage.getTrajet().getNombrePlaces()) {
            throw new IllegalArgumentException("Le total des places ne peut pas dépasser la capacité du trajet");
        }
    }

    @Override
    public List<Voyage> obtenirTousLesVoyages() {
        return voyageDAO.findAll();
    }
}
