package com.train.servlet;

import com.train.service.VoyageService;
import com.train.service.impl.VoyageServiceImpl;
import com.train.model.Voyage;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;

/**
 * Servlet pour la recherche de voyages
 */
@WebServlet(name = "SearchServlet", urlPatterns = {"/search"})
public class SearchServlet extends HttpServlet {
    
    private VoyageService voyageService;
    
    @Override
    public void init() throws ServletException {
        voyageService = new VoyageServiceImpl();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String villeDepart = request.getParameter("villeDepart");
        String villeArrivee = request.getParameter("villeArrivee");
        String dateVoyageStr = request.getParameter("dateVoyage");
        
        // Si aucun paramètre de recherche, afficher la page de recherche
        if (villeDepart == null || villeArrivee == null || dateVoyageStr == null) {
            request.getRequestDispatcher("/WEB-INF/views/search/search.jsp").forward(request, response);
            return;
        }
        
        try {
            // Validation et nettoyage des paramètres
            villeDepart = villeDepart.trim();
            villeArrivee = villeArrivee.trim();
            
            if (villeDepart.isEmpty() || villeArrivee.isEmpty()) {
                request.setAttribute("errorMessage", "Veuillez saisir les villes de départ et d'arrivée");
                request.getRequestDispatcher("/WEB-INF/views/search/search.jsp").forward(request, response);
                return;
            }
            
            if (villeDepart.equalsIgnoreCase(villeArrivee)) {
                request.setAttribute("errorMessage", "La ville de départ doit être différente de la ville d'arrivée");
                request.setAttribute("villeDepart", villeDepart);
                request.setAttribute("villeArrivee", villeArrivee);
                request.setAttribute("dateVoyage", dateVoyageStr);
                request.getRequestDispatcher("/WEB-INF/views/search/search.jsp").forward(request, response);
                return;
            }
            
            // Validation de la date
            LocalDate dateVoyage;
            try {
                dateVoyage = LocalDate.parse(dateVoyageStr, DateTimeFormatter.ISO_LOCAL_DATE);
            } catch (DateTimeParseException e) {
                request.setAttribute("errorMessage", "Format de date invalide");
                request.setAttribute("villeDepart", villeDepart);
                request.setAttribute("villeArrivee", villeArrivee);
                request.getRequestDispatcher("/WEB-INF/views/search/search.jsp").forward(request, response);
                return;
            }
            
            // Vérifier que la date n'est pas dans le passé
            if (dateVoyage.isBefore(LocalDate.now())) {
                request.setAttribute("errorMessage", "Impossible de rechercher des voyages dans le passé");
                request.setAttribute("villeDepart", villeDepart);
                request.setAttribute("villeArrivee", villeArrivee);
                request.setAttribute("dateVoyage", dateVoyageStr);
                request.getRequestDispatcher("/WEB-INF/views/search/search.jsp").forward(request, response);
                return;
            }
            
            // Rechercher les voyages
            List<Voyage> voyagesDisponibles = voyageService.rechercherVoyagesDisponibles(
                villeDepart, villeArrivee, dateVoyage);
            
            // Préparer les attributs pour la vue
            request.setAttribute("voyages", voyagesDisponibles);
            request.setAttribute("villeDepart", villeDepart);
            request.setAttribute("villeArrivee", villeArrivee);
            request.setAttribute("dateVoyage", dateVoyage);
            request.setAttribute("dateVoyageStr", dateVoyageStr);
            
            // Statistiques de recherche
            request.setAttribute("nombreResultats", voyagesDisponibles.size());
            
            if (voyagesDisponibles.isEmpty()) {
                request.setAttribute("infoMessage", 
                    "Aucun voyage trouvé pour " + villeDepart + " → " + villeArrivee + 
                    " le " + dateVoyage.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
            } else {
                request.setAttribute("successMessage", 
                    voyagesDisponibles.size() + " voyage(s) trouvé(s) pour " + 
                    villeDepart + " → " + villeArrivee + 
                    " le " + dateVoyage.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
            }
            
            // Afficher les résultats
            request.getRequestDispatcher("/WEB-INF/views/search/results.jsp").forward(request, response);
            
        } catch (Exception e) {
            request.setAttribute("errorMessage", "Erreur lors de la recherche : " + e.getMessage());
            request.setAttribute("villeDepart", villeDepart);
            request.setAttribute("villeArrivee", villeArrivee);
            request.setAttribute("dateVoyage", dateVoyageStr);
            request.getRequestDispatcher("/WEB-INF/views/search/search.jsp").forward(request, response);
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // Rediriger vers GET avec les paramètres
        String villeDepart = request.getParameter("villeDepart");
        String villeArrivee = request.getParameter("villeArrivee");
        String dateVoyage = request.getParameter("dateVoyage");
        
        StringBuilder url = new StringBuilder(request.getContextPath() + "/search");
        
        if (villeDepart != null || villeArrivee != null || dateVoyage != null) {
            url.append("?");
            
            if (villeDepart != null && !villeDepart.trim().isEmpty()) {
                url.append("villeDepart=").append(java.net.URLEncoder.encode(villeDepart.trim(), "UTF-8"));
            }
            
            if (villeArrivee != null && !villeArrivee.trim().isEmpty()) {
                if (url.toString().contains("=")) url.append("&");
                url.append("villeArrivee=").append(java.net.URLEncoder.encode(villeArrivee.trim(), "UTF-8"));
            }
            
            if (dateVoyage != null && !dateVoyage.trim().isEmpty()) {
                if (url.toString().contains("=")) url.append("&");
                url.append("dateVoyage=").append(dateVoyage.trim());
            }
        }
        
        response.sendRedirect(url.toString());
    }
}
