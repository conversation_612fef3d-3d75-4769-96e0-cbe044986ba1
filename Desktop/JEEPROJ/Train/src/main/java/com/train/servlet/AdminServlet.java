package com.train.servlet;

import com.train.model.*;
import com.train.service.*;
import com.train.service.impl.*;
import com.train.model.Paiement;
import com.train.model.DemandeAnnulation;
import com.train.model.Affectation;
import com.train.service.GareService;
import com.train.service.impl.GareServiceImpl;
import com.train.service.TrajetService;
import com.train.service.impl.TrajetServiceImpl;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * Servlet pour l'espace administrateur
 */
@WebServlet(name = "AdminServlet", urlPatterns = {"/admin", "/admin/*"})
public class AdminServlet extends HttpServlet {
    
    private UtilisateurService utilisateurService;
    private VoyageService voyageService;
    private ReservationService reservationService;
    private GareService gareService;
    private TrajetService trajetService;
    private PaiementService paiementService;
    private AnnulationService annulationService;
    private AffectationService affectationService;
    
    @Override
    public void init() throws ServletException {
        // Utiliser les vrais services avec base de données
        utilisateurService = new UtilisateurServiceImpl(); // CHANGÉ : utiliser le vrai service
        voyageService = new VoyageServiceImpl();
        reservationService = new DemoReservationServiceImpl(); // TODO: remplacer par le vrai service
        gareService = new GareServiceImpl();
        trajetService = new TrajetServiceImpl();
        paiementService = new PaiementServiceImpl();
        annulationService = new AnnulationServiceImpl();
        affectationService = new AffectationServiceDBImpl(utilisateurService, voyageService);
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // Vérifier que l'utilisateur est connecté et est admin
        if (!isAdminConnected(request, response)) {
            return;
        }
        
        String pathInfo = request.getPathInfo();
        
        if (pathInfo == null || pathInfo.equals("/")) {
            afficherDashboard(request, response);
        } else if (pathInfo.startsWith("/gares")) {
            gererGares(request, response, pathInfo);
        } else if (pathInfo.startsWith("/trajets")) {
            gererTrajets(request, response, pathInfo);
        } else if (pathInfo.startsWith("/voyages")) {
            gererVoyages(request, response, pathInfo);
        } else if (pathInfo.startsWith("/users")) {
            gererUtilisateurs(request, response, pathInfo);
        } else if (pathInfo.startsWith("/reservations")) {
            gererReservations(request, response, pathInfo);
        } else if (pathInfo.startsWith("/payments")) {
            gererPaiements(request, response, pathInfo);
        } else if (pathInfo.startsWith("/cancellations")) {
            gererAnnulations(request, response, pathInfo);
        } else if (pathInfo.startsWith("/employees")) {
            gererEmployes(request, response, pathInfo);
        } else {
            response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // Vérifier que l'utilisateur est connecté et est admin
        if (!isAdminConnected(request, response)) {
            return;
        }
        
        String pathInfo = request.getPathInfo();
        
        if (pathInfo != null) {
            if (pathInfo.startsWith("/gares")) {
                traiterActionGares(request, response, pathInfo);
            } else if (pathInfo.startsWith("/trajets")) {
                traiterActionTrajets(request, response, pathInfo);
            } else if (pathInfo.startsWith("/voyages")) {
                traiterActionVoyages(request, response, pathInfo);
            } else if (pathInfo.startsWith("/users")) {
                traiterActionUtilisateurs(request, response, pathInfo);
            } else if (pathInfo.startsWith("/reservations")) {
                traiterActionReservations(request, response, pathInfo);
            } else {
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
            }
        }
    }
    
    /**
     * Vérifier que l'utilisateur connecté est un administrateur
     */
    private boolean isAdminConnected(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        HttpSession session = request.getSession(false);
        if (session == null) {
            response.sendRedirect(request.getContextPath() + "/login?redirect=" + 
                                request.getRequestURI());
            return false;
        }
        
        Utilisateur user = (Utilisateur) session.getAttribute("user");
        if (user == null || !user.isAdmin()) {
            response.sendError(HttpServletResponse.SC_FORBIDDEN, 
                             "Accès réservé aux administrateurs");
            return false;
        }
        
        return true;
    }
    
    /**
     * Afficher le tableau de bord administrateur
     */
    private void afficherDashboard(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        System.out.println("=== AdminServlet - Chargement du Dashboard ===");

        // Statistiques pour le dashboard - DONNÉES RÉELLES DE LA BASE
        List<Utilisateur> utilisateurs = utilisateurService.obtenirTousLesUtilisateurs();
        List<Voyage> voyages = voyageService.obtenirTousLesVoyages();
        List<Reservation> reservations = reservationService.obtenirToutesLesReservations();

        System.out.println("Utilisateurs trouvés: " + utilisateurs.size());
        System.out.println("Voyages trouvés: " + voyages.size());
        System.out.println("Réservations trouvées: " + reservations.size());

        // Calculer les statistiques par type d'utilisateur
        long nbUtilisateurs = utilisateurs.size();
        long nbClients = utilisateurs.stream()
                .filter(u -> u.getTypeUtilisateur() == TypeUtilisateur.CLIENT)
                .count();
        long nbEmployes = utilisateurs.stream()
                .filter(u -> u.getTypeUtilisateur() == TypeUtilisateur.EMPLOYE)
                .count();
        long nbAdmins = utilisateurs.stream()
                .filter(u -> u.getTypeUtilisateur() == TypeUtilisateur.ADMINISTRATEUR)
                .count();

        // Statistiques des voyages et réservations
        long nbVoyages = voyages.size();
        long nbReservations = reservations.size();
        long nbReservationsConfirmees = reservations.stream()
                .filter(r -> r.getStatut() == StatutReservation.CONFIRMEE)
                .count();

        // Ajouter les statistiques à la requête
        request.setAttribute("nbUtilisateurs", nbUtilisateurs);
        request.setAttribute("nbClients", nbClients);
        request.setAttribute("nbEmployes", nbEmployes);
        request.setAttribute("nbAdmins", nbAdmins);
        request.setAttribute("nbVoyages", nbVoyages);
        request.setAttribute("nbReservations", nbReservations);
        request.setAttribute("nbReservationsConfirmees", nbReservationsConfirmees);

        // Ajouter la date/heure actuelle pour l'affichage
        request.setAttribute("now", new java.util.Date());

        // Nouveaux clients (inscrits récemment) - DONNÉES RÉELLES
        List<Utilisateur> nouveauxClients = utilisateurs.stream()
                .filter(u -> u.getTypeUtilisateur() == TypeUtilisateur.CLIENT)
                .filter(u -> u.getDateCreation() != null)
                .sorted((u1, u2) -> u2.getDateCreation().compareTo(u1.getDateCreation()))
                .limit(5)
                .toList();
        request.setAttribute("nouveauxClients", nouveauxClients);

        // Liste des employés - DONNÉES RÉELLES
        List<Utilisateur> employes = utilisateurs.stream()
                .filter(u -> u.getTypeUtilisateur() == TypeUtilisateur.EMPLOYE)
                .sorted((u1, u2) -> {
                    if (u1.getDateCreation() != null && u2.getDateCreation() != null) {
                        return u2.getDateCreation().compareTo(u1.getDateCreation());
                    }
                    return u1.getNom().compareTo(u2.getNom());
                })
                .limit(10)
                .toList();
        request.setAttribute("employes", employes);

        // Voyages récents
        List<Voyage> voyagesRecents = voyages.stream()
                .filter(v -> !v.isPasse())
                .sorted((v1, v2) -> v1.getDateVoyage().compareTo(v2.getDateVoyage()))
                .limit(5)
                .toList();
        request.setAttribute("voyagesRecents", voyagesRecents);

        // Réservations récentes
        List<Reservation> reservationsRecentes = reservations.stream()
                .sorted((r1, r2) -> r2.getDateReservation().compareTo(r1.getDateReservation()))
                .limit(5)
                .toList();
        request.setAttribute("reservationsRecentes", reservationsRecentes);

        System.out.println("Nouveaux clients: " + nouveauxClients.size());
        System.out.println("Employés: " + employes.size());

        request.getRequestDispatcher("/WEB-INF/views/admin/dashboard.jsp")
               .forward(request, response);
    }
    
    /**
     * Gérer les actions liées aux gares
     */
    private void gererGares(HttpServletRequest request, HttpServletResponse response, String pathInfo) 
            throws ServletException, IOException {
        
        if (pathInfo.equals("/gares") || pathInfo.equals("/gares/")) {
            // Afficher la liste des gares
            afficherListeGares(request, response);
        } else if (pathInfo.equals("/gares/new")) {
            // Afficher le formulaire de création
            afficherFormulaireGare(request, response, null);
        } else if (pathInfo.startsWith("/gares/edit/")) {
            // Afficher le formulaire d'édition
            String gareId = pathInfo.substring("/gares/edit/".length());
            afficherFormulaireGare(request, response, Long.parseLong(gareId));
        }
    }
    
    /**
     * Gérer les actions liées aux trajets
     */
    private void gererTrajets(HttpServletRequest request, HttpServletResponse response, String pathInfo)
            throws ServletException, IOException {

        if (pathInfo.equals("/trajets") || pathInfo.equals("/trajets/")) {
            // Afficher la liste des trajets
            afficherListeTrajets(request, response);
        } else if (pathInfo.equals("/trajets/new")) {
            // Afficher le formulaire de création
            afficherFormulaireTrajets(request, response, null);
        } else if (pathInfo.startsWith("/trajets/edit/")) {
            // Afficher le formulaire d'édition
            String trajetId = pathInfo.substring("/trajets/edit/".length());
            afficherFormulaireTrajets(request, response, Long.parseLong(trajetId));
        }
    }
    
    /**
     * Gérer les actions liées aux voyages
     */
    private void gererVoyages(HttpServletRequest request, HttpServletResponse response, String pathInfo)
            throws ServletException, IOException {

        if (pathInfo.equals("/voyages") || pathInfo.equals("/voyages/")) {
            // Afficher la liste des voyages
            afficherListeVoyages(request, response);
        } else if (pathInfo.equals("/voyages/new")) {
            // Afficher le formulaire de création
            afficherFormulaireVoyage(request, response, null);
        } else if (pathInfo.startsWith("/voyages/edit/")) {
            // Afficher le formulaire d'édition
            String voyageId = pathInfo.substring("/voyages/edit/".length());
            afficherFormulaireVoyage(request, response, Long.parseLong(voyageId));
        }
    }
    
    /**
     * Gérer les actions liées aux utilisateurs
     */
    private void gererUtilisateurs(HttpServletRequest request, HttpServletResponse response, String pathInfo)
            throws ServletException, IOException {

        System.out.println("=== AdminServlet - Gestion des Utilisateurs ===");
        System.out.println("PathInfo: " + pathInfo);

        if (pathInfo.equals("/users") || pathInfo.equals("/users/")) {
            // Afficher la liste des utilisateurs
            System.out.println("Récupération de tous les utilisateurs...");
            List<Utilisateur> utilisateurs = utilisateurService.obtenirTousLesUtilisateurs();
            System.out.println("Nombre d'utilisateurs trouvés: " + (utilisateurs != null ? utilisateurs.size() : "null"));

            if (utilisateurs != null && !utilisateurs.isEmpty()) {
                for (Utilisateur u : utilisateurs) {
                    System.out.println("- " + u.getNom() + " " + u.getPrenom() + " (" + u.getEmail() + ") - " + u.getTypeUtilisateur());
                }
            }

            request.setAttribute("utilisateurs", utilisateurs);
            System.out.println("Redirection vers la JSP...");
            request.getRequestDispatcher("/WEB-INF/views/admin/users/list.jsp")
                   .forward(request, response);
        }
    }
    
    /**
     * Gérer les actions liées aux réservations
     */
    private void gererReservations(HttpServletRequest request, HttpServletResponse response, String pathInfo) 
            throws ServletException, IOException {
        
        if (pathInfo.equals("/reservations") || pathInfo.equals("/reservations/")) {
            // Afficher la liste des réservations
            List<Reservation> reservations = reservationService.obtenirToutesLesReservations();
            request.setAttribute("reservations", reservations);
            request.getRequestDispatcher("/WEB-INF/views/admin/reservations/list.jsp")
                   .forward(request, response);
        }
    }
    
    /**
     * Gérer les actions liées aux paiements
     */
    private void gererPaiements(HttpServletRequest request, HttpServletResponse response, String pathInfo)
            throws ServletException, IOException {

        if (pathInfo.equals("/payments") || pathInfo.equals("/payments/")) {
            // Afficher la liste des paiements avec données réelles
            List<Paiement> paiements = paiementService.obtenirTousLesPaiements();
            PaiementService.StatistiquesPaiement stats = paiementService.obtenirStatistiques();

            request.setAttribute("paiements", paiements);
            request.setAttribute("statistiques", stats);
            request.getRequestDispatcher("/WEB-INF/views/admin/payments/list.jsp")
                   .forward(request, response);
        }
    }

    /**
     * Gérer les actions liées aux annulations et remboursements
     */
    private void gererAnnulations(HttpServletRequest request, HttpServletResponse response, String pathInfo)
            throws ServletException, IOException {

        if (pathInfo.equals("/cancellations") || pathInfo.equals("/cancellations/")) {
            // Afficher la page de gestion des annulations avec données réelles
            List<DemandeAnnulation> demandes = annulationService.obtenirToutesLesDemandes();
            AnnulationService.StatistiquesAnnulation stats = annulationService.obtenirStatistiques();

            request.setAttribute("demandes", demandes);
            request.setAttribute("statistiques", stats);
            request.getRequestDispatcher("/WEB-INF/views/admin/cancellations/list.jsp")
                   .forward(request, response);
        }
    }

    /**
     * Gérer les actions liées aux employés et affectations
     */
    private void gererEmployes(HttpServletRequest request, HttpServletResponse response, String pathInfo)
            throws ServletException, IOException {

        if (pathInfo.equals("/employees") || pathInfo.equals("/employees/")) {
            // Afficher la page de gestion des affectations avec données réelles
            List<Affectation> affectations = affectationService.obtenirToutesLesAffectations();
            AffectationService.StatistiquesAffectation stats = affectationService.obtenirStatistiques();

            // Obtenir les employés et voyages pour les formulaires
            List<Utilisateur> employes = utilisateurService.obtenirTousLesUtilisateurs().stream()
                    .filter(u -> u.getTypeUtilisateur() == TypeUtilisateur.EMPLOYE)
                    .collect(java.util.stream.Collectors.toList());

            List<Voyage> voyages = voyageService.obtenirTousLesVoyages();

            request.setAttribute("affectations", affectations);
            request.setAttribute("statistiques", stats);
            request.setAttribute("employes", employes);
            request.setAttribute("voyages", voyages);
            request.getRequestDispatcher("/WEB-INF/views/admin/employees/affectations.jsp")
                   .forward(request, response);
        }
    }

    // Méthodes pour traiter les actions POST
    private void traiterActionGares(HttpServletRequest request, HttpServletResponse response, String pathInfo)
            throws ServletException, IOException {

        if (pathInfo.equals("/gares/save")) {
            sauvegarderGare(request, response);
        } else if (pathInfo.startsWith("/gares/delete/")) {
            String gareId = pathInfo.substring("/gares/delete/".length());
            supprimerGare(request, response, Long.parseLong(gareId));
        } else if (pathInfo.startsWith("/gares/toggle/")) {
            String gareId = pathInfo.substring("/gares/toggle/".length());
            toggleGareActive(request, response, Long.parseLong(gareId));
        } else {
            response.sendRedirect(request.getContextPath() + "/admin/gares");
        }
    }

    private void traiterActionTrajets(HttpServletRequest request, HttpServletResponse response, String pathInfo)
            throws ServletException, IOException {

        if (pathInfo.equals("/trajets/save")) {
            sauvegarderTrajet(request, response);
        } else if (pathInfo.startsWith("/trajets/delete/")) {
            String trajetId = pathInfo.substring("/trajets/delete/".length());
            supprimerTrajet(request, response, Long.parseLong(trajetId));
        } else if (pathInfo.startsWith("/trajets/toggle/")) {
            String trajetId = pathInfo.substring("/trajets/toggle/".length());
            toggleTrajetActif(request, response, Long.parseLong(trajetId));
        } else {
            response.sendRedirect(request.getContextPath() + "/admin/trajets");
        }
    }

    private void traiterActionVoyages(HttpServletRequest request, HttpServletResponse response, String pathInfo)
            throws ServletException, IOException {

        if (pathInfo.equals("/voyages/save")) {
            sauvegarderVoyage(request, response);
        } else if (pathInfo.startsWith("/voyages/delete/")) {
            String voyageId = pathInfo.substring("/voyages/delete/".length());
            supprimerVoyage(request, response, Long.parseLong(voyageId));
        } else if (pathInfo.startsWith("/voyages/cancel/")) {
            String voyageId = pathInfo.substring("/voyages/cancel/".length());
            annulerVoyage(request, response, Long.parseLong(voyageId));
        } else {
            response.sendRedirect(request.getContextPath() + "/admin/voyages");
        }
    }

    private void traiterActionUtilisateurs(HttpServletRequest request, HttpServletResponse response, String pathInfo)
            throws ServletException, IOException {

        if (pathInfo.startsWith("/users/toggle/")) {
            // Activer/désactiver un utilisateur
            String userIdStr = pathInfo.substring("/users/toggle/".length());
            try {
                Long userId = Long.parseLong(userIdStr);
                boolean success = utilisateurService.toggleUtilisateurActif(userId);

                HttpSession session = request.getSession();
                if (success) {
                    session.setAttribute("successMessage", "Statut de l'utilisateur modifié avec succès");
                } else {
                    session.setAttribute("errorMessage", "Erreur lors de la modification du statut");
                }
            } catch (NumberFormatException e) {
                HttpSession session = request.getSession();
                session.setAttribute("errorMessage", "ID utilisateur invalide");
            }
        }

        response.sendRedirect(request.getContextPath() + "/admin/users");
    }

    private void traiterActionReservations(HttpServletRequest request, HttpServletResponse response, String pathInfo)
            throws ServletException, IOException {
        // TODO: Implémenter les actions POST pour les réservations
        response.sendRedirect(request.getContextPath() + "/admin/reservations");
    }

    // ========================================
    // MÉTHODES SPÉCIFIQUES POUR LES GARES
    // ========================================

    /**
     * Afficher la liste des gares
     */
    private void afficherListeGares(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        try {
            List<Gare> gares = gareService.obtenirToutesLesGares();
            request.setAttribute("gares", gares);

            // Statistiques
            long nbGaresActives = gares.stream().filter(Gare::isActive).count();
            long nbGaresInactives = gares.size() - nbGaresActives;

            request.setAttribute("nbGaresActives", nbGaresActives);
            request.setAttribute("nbGaresInactives", nbGaresInactives);

            request.getRequestDispatcher("/WEB-INF/views/admin/gares/list.jsp")
                   .forward(request, response);
        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors du chargement des gares : " + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/admin/gares/list.jsp")
                   .forward(request, response);
        }
    }

    /**
     * Afficher le formulaire de gare (création ou modification)
     */
    private void afficherFormulaireGare(HttpServletRequest request, HttpServletResponse response, Long gareId)
            throws ServletException, IOException {
        try {
            if (gareId != null) {
                // Mode modification
                Gare gare = gareService.obtenirGareParId(gareId);
                if (gare != null) {
                    request.setAttribute("gare", gare);
                } else {
                    request.setAttribute("error", "Gare non trouvée");
                }
            }
            // Mode création : pas besoin de charger une gare

            request.getRequestDispatcher("/WEB-INF/views/admin/gares/form_simple.jsp")
                   .forward(request, response);
        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors du chargement de la gare : " + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/admin/gares/form_simple.jsp")
                   .forward(request, response);
        }
    }

    /**
     * Sauvegarder une gare (création ou modification)
     */
    private void sauvegarderGare(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        try {
            // Récupérer les paramètres du formulaire
            String idStr = request.getParameter("id");
            String nom = request.getParameter("nom");
            String ville = request.getParameter("ville");
            String codeGare = request.getParameter("codeGare");
            String adresse = request.getParameter("adresse");
            String codePostal = request.getParameter("codePostal");
            String latitudeStr = request.getParameter("latitude");
            String longitudeStr = request.getParameter("longitude");
            String activeStr = request.getParameter("active");

            // Validation des champs obligatoires
            if (nom == null || nom.trim().isEmpty() ||
                ville == null || ville.trim().isEmpty() ||
                codeGare == null || codeGare.trim().isEmpty()) {

                request.setAttribute("error", "Les champs nom, ville et code gare sont obligatoires");
                afficherFormulaireGare(request, response, idStr != null ? Long.parseLong(idStr) : null);
                return;
            }

            // Vérifier l'unicité du code gare
            Gare gareExistante = gareService.obtenirGareParCode(codeGare.trim().toUpperCase());
            if (gareExistante != null && (idStr == null || !gareExistante.getId().equals(Long.parseLong(idStr)))) {
                request.setAttribute("error", "Une gare avec ce code existe déjà");
                afficherFormulaireGare(request, response, idStr != null ? Long.parseLong(idStr) : null);
                return;
            }

            // Créer ou modifier la gare
            Gare gare;
            if (idStr != null && !idStr.trim().isEmpty()) {
                // Modification
                gare = gareService.obtenirGareParId(Long.parseLong(idStr));
                if (gare == null) {
                    request.setAttribute("error", "Gare non trouvée");
                    afficherFormulaireGare(request, response, null);
                    return;
                }
                gare.setDateModification(java.time.LocalDateTime.now());
            } else {
                // Création
                gare = new Gare();
                gare.setDateCreation(java.time.LocalDateTime.now());
            }

            // Remplir les propriétés
            gare.setNom(nom.trim());
            gare.setVille(ville.trim());
            gare.setCodeGare(codeGare.trim().toUpperCase());
            gare.setAdresse(adresse != null ? adresse.trim() : null);
            gare.setCodePostal(codePostal != null ? codePostal.trim() : null);
            gare.setActive("true".equals(activeStr));

            // Gérer les coordonnées GPS
            if (latitudeStr != null && !latitudeStr.trim().isEmpty()) {
                try {
                    gare.setLatitude(Double.parseDouble(latitudeStr));
                } catch (NumberFormatException e) {
                    request.setAttribute("error", "Format de latitude invalide");
                    afficherFormulaireGare(request, response, idStr != null ? Long.parseLong(idStr) : null);
                    return;
                }
            }

            if (longitudeStr != null && !longitudeStr.trim().isEmpty()) {
                try {
                    gare.setLongitude(Double.parseDouble(longitudeStr));
                } catch (NumberFormatException e) {
                    request.setAttribute("error", "Format de longitude invalide");
                    afficherFormulaireGare(request, response, idStr != null ? Long.parseLong(idStr) : null);
                    return;
                }
            }

            // Sauvegarder
            if (gare.getId() == null) {
                gareService.creerGare(gare);
                request.getSession().setAttribute("success", "Gare créée avec succès");
            } else {
                gareService.modifierGare(gare);
                request.getSession().setAttribute("success", "Gare modifiée avec succès");
            }

            response.sendRedirect(request.getContextPath() + "/admin/gares");

        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors de la sauvegarde : " + e.getMessage());
            afficherFormulaireGare(request, response, null);
        }
    }

    /**
     * Supprimer une gare
     */
    private void supprimerGare(HttpServletRequest request, HttpServletResponse response, Long gareId)
            throws ServletException, IOException {
        try {
            boolean success = gareService.supprimerGare(gareId);

            HttpSession session = request.getSession();
            if (success) {
                session.setAttribute("success", "Gare supprimée avec succès");
            } else {
                session.setAttribute("error", "Impossible de supprimer la gare (elle est peut-être utilisée dans des trajets)");
            }
        } catch (Exception e) {
            HttpSession session = request.getSession();
            session.setAttribute("error", "Erreur lors de la suppression : " + e.getMessage());
        }

        response.sendRedirect(request.getContextPath() + "/admin/gares");
    }

    /**
     * Activer/désactiver une gare
     */
    private void toggleGareActive(HttpServletRequest request, HttpServletResponse response, Long gareId)
            throws ServletException, IOException {
        try {
            Gare gare = gareService.obtenirGareParId(gareId);
            if (gare != null) {
                gare.setActive(!gare.isActive());
                gare.setDateModification(java.time.LocalDateTime.now());
                gareService.modifierGare(gare);

                HttpSession session = request.getSession();
                session.setAttribute("success", "Statut de la gare modifié avec succès");
            } else {
                HttpSession session = request.getSession();
                session.setAttribute("error", "Gare non trouvée");
            }
        } catch (Exception e) {
            HttpSession session = request.getSession();
            session.setAttribute("error", "Erreur lors de la modification : " + e.getMessage());
        }

        response.sendRedirect(request.getContextPath() + "/admin/gares");
    }

    // ========================================
    // MÉTHODES SPÉCIFIQUES POUR LES TRAJETS
    // ========================================

    /**
     * Afficher la liste des trajets
     */
    private void afficherListeTrajets(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        try {
            List<Trajet> trajets = trajetService.obtenirTousLesTrajets();
            request.setAttribute("trajets", trajets);

            // Statistiques
            long nbTrajetsActifs = trajetService.compterTrajetsActifs();
            long nbTrajetsInactifs = trajetService.compterTrajetsInactifs();

            request.setAttribute("nbTrajetsActifs", nbTrajetsActifs);
            request.setAttribute("nbTrajetsInactifs", nbTrajetsInactifs);

            request.getRequestDispatcher("/WEB-INF/views/admin/trajets/list.jsp")
                   .forward(request, response);
        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors du chargement des trajets : " + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/admin/trajets/list.jsp")
                   .forward(request, response);
        }
    }

    /**
     * Afficher le formulaire de trajet (création ou modification)
     */
    private void afficherFormulaireTrajets(HttpServletRequest request, HttpServletResponse response, Long trajetId)
            throws ServletException, IOException {
        try {
            // Charger la liste des gares pour les sélecteurs
            List<Gare> gares = gareService.obtenirGaresActives();
            request.setAttribute("gares", gares);

            if (trajetId != null) {
                // Mode modification
                Trajet trajet = trajetService.trouverParId(trajetId).orElse(null);
                if (trajet != null) {
                    request.setAttribute("trajet", trajet);
                } else {
                    request.setAttribute("error", "Trajet non trouvé");
                }
            }
            // Mode création : pas besoin de charger un trajet

            request.getRequestDispatcher("/WEB-INF/views/admin/trajets/form.jsp")
                   .forward(request, response);
        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors du chargement du trajet : " + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/admin/trajets/form.jsp")
                   .forward(request, response);
        }
    }

    /**
     * Sauvegarder un trajet (création ou modification)
     */
    private void sauvegarderTrajet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        try {
            // Récupérer les paramètres du formulaire
            String idStr = request.getParameter("id");
            String gareDepartIdStr = request.getParameter("gareDepartId");
            String gareArriveeIdStr = request.getParameter("gareArriveeId");
            String heureDepart = request.getParameter("heureDepart");
            String heureArrivee = request.getParameter("heureArrivee");
            String prixStr = request.getParameter("prix");
            String nombrePlacesStr = request.getParameter("nombrePlaces");
            String typeTrain = request.getParameter("typeTrain");
            String actifStr = request.getParameter("actif");

            // Validation des champs obligatoires
            if (gareDepartIdStr == null || gareDepartIdStr.trim().isEmpty() ||
                gareArriveeIdStr == null || gareArriveeIdStr.trim().isEmpty() ||
                heureDepart == null || heureDepart.trim().isEmpty() ||
                heureArrivee == null || heureArrivee.trim().isEmpty() ||
                prixStr == null || prixStr.trim().isEmpty() ||
                nombrePlacesStr == null || nombrePlacesStr.trim().isEmpty()) {

                request.setAttribute("error", "Tous les champs sont obligatoires");
                afficherFormulaireTrajets(request, response, idStr != null ? Long.parseLong(idStr) : null);
                return;
            }

            // Créer ou modifier le trajet
            Trajet trajet;
            if (idStr != null && !idStr.trim().isEmpty()) {
                // Modification
                trajet = trajetService.trouverParId(Long.parseLong(idStr)).orElse(null);
                if (trajet == null) {
                    request.setAttribute("error", "Trajet non trouvé");
                    afficherFormulaireTrajets(request, response, null);
                    return;
                }
            } else {
                // Création
                trajet = new Trajet();
            }

            // Charger les gares
            Gare gareDepart = gareService.obtenirGareParId(Long.parseLong(gareDepartIdStr));
            Gare gareArrivee = gareService.obtenirGareParId(Long.parseLong(gareArriveeIdStr));

            if (gareDepart == null || gareArrivee == null) {
                request.setAttribute("error", "Gares non trouvées");
                afficherFormulaireTrajets(request, response, idStr != null ? Long.parseLong(idStr) : null);
                return;
            }

            // Remplir les propriétés
            trajet.setGareDepart(gareDepart);
            trajet.setGareArrivee(gareArrivee);
            trajet.setHeureDepart(java.time.LocalTime.parse(heureDepart));
            trajet.setHeureArrivee(java.time.LocalTime.parse(heureArrivee));
            trajet.setPrix(new java.math.BigDecimal(prixStr));
            trajet.setNombrePlaces(Integer.parseInt(nombrePlacesStr));
            trajet.setTypeTrain(typeTrain != null ? typeTrain : "TER");
            trajet.setActif("true".equals(actifStr));

            // Sauvegarder
            if (trajet.getId() == null) {
                trajetService.creerTrajet(trajet);
                request.getSession().setAttribute("success", "Trajet créé avec succès");
            } else {
                trajetService.mettreAJourTrajet(trajet);
                request.getSession().setAttribute("success", "Trajet modifié avec succès");
            }

            response.sendRedirect(request.getContextPath() + "/admin/trajets");

        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors de la sauvegarde : " + e.getMessage());
            afficherFormulaireTrajets(request, response, null);
        }
    }

    /**
     * Supprimer un trajet
     */
    private void supprimerTrajet(HttpServletRequest request, HttpServletResponse response, Long trajetId)
            throws ServletException, IOException {
        try {
            boolean success = trajetService.supprimerTrajet(trajetId);

            HttpSession session = request.getSession();
            if (success) {
                session.setAttribute("success", "Trajet supprimé avec succès");
            } else {
                session.setAttribute("error", "Impossible de supprimer le trajet");
            }
        } catch (Exception e) {
            HttpSession session = request.getSession();
            session.setAttribute("error", "Erreur lors de la suppression : " + e.getMessage());
        }

        response.sendRedirect(request.getContextPath() + "/admin/trajets");
    }

    /**
     * Activer/désactiver un trajet
     */
    private void toggleTrajetActif(HttpServletRequest request, HttpServletResponse response, Long trajetId)
            throws ServletException, IOException {
        try {
            boolean success = trajetService.changerStatutTrajet(trajetId);

            HttpSession session = request.getSession();
            if (success) {
                session.setAttribute("success", "Statut du trajet modifié avec succès");
            } else {
                session.setAttribute("error", "Trajet non trouvé");
            }
        } catch (Exception e) {
            HttpSession session = request.getSession();
            session.setAttribute("error", "Erreur lors de la modification : " + e.getMessage());
        }

        response.sendRedirect(request.getContextPath() + "/admin/trajets");
    }

    // ========================================
    // MÉTHODES SPÉCIFIQUES POUR LES VOYAGES
    // ========================================

    /**
     * Afficher la liste des voyages
     */
    private void afficherListeVoyages(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        try {
            List<Voyage> voyages = voyageService.obtenirTousLesVoyages();
            request.setAttribute("voyages", voyages);

            // Statistiques
            long nbVoyagesProgrammes = voyages.stream()
                    .filter(v -> v.getStatut() == StatutVoyage.PROGRAMME)
                    .count();
            long nbVoyagesTermines = voyages.stream()
                    .filter(v -> v.getStatut() == StatutVoyage.TERMINE)
                    .count();
            long nbVoyagesAnnules = voyages.stream()
                    .filter(v -> v.getStatut() == StatutVoyage.ANNULE)
                    .count();

            request.setAttribute("nbVoyagesProgrammes", nbVoyagesProgrammes);
            request.setAttribute("nbVoyagesTermines", nbVoyagesTermines);
            request.setAttribute("nbVoyagesAnnules", nbVoyagesAnnules);

            request.getRequestDispatcher("/WEB-INF/views/admin/voyages/list.jsp")
                   .forward(request, response);
        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors du chargement des voyages : " + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/admin/voyages/list.jsp")
                   .forward(request, response);
        }
    }

    /**
     * Afficher le formulaire de voyage (création ou modification)
     */
    private void afficherFormulaireVoyage(HttpServletRequest request, HttpServletResponse response, Long voyageId)
            throws ServletException, IOException {
        try {
            if (voyageId != null) {
                // Mode modification
                Voyage voyage = voyageService.trouverParId(voyageId).orElse(null);
                if (voyage != null) {
                    request.setAttribute("voyage", voyage);
                } else {
                    request.setAttribute("error", "Voyage non trouvé");
                }
            }

            // Charger la liste des trajets actifs pour le formulaire
            List<Trajet> trajets = trajetService.obtenirTrajetsActifs();
            request.setAttribute("trajets", trajets);

            request.getRequestDispatcher("/WEB-INF/views/admin/voyages/form.jsp")
                   .forward(request, response);
        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors du chargement du formulaire : " + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/admin/voyages/form.jsp")
                   .forward(request, response);
        }
    }

    /**
     * Sauvegarder un voyage (création ou modification)
     */
    private void sauvegarderVoyage(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        try {
            // Récupérer les paramètres du formulaire
            String idStr = request.getParameter("id");
            String trajetIdStr = request.getParameter("trajetId");
            String dateVoyageStr = request.getParameter("dateVoyage");
            String placesDisponiblesStr = request.getParameter("placesDisponibles");
            String statutStr = request.getParameter("statut");

            // Validation des champs obligatoires
            if (trajetIdStr == null || trajetIdStr.trim().isEmpty() ||
                dateVoyageStr == null || dateVoyageStr.trim().isEmpty()) {

                request.setAttribute("error", "Le trajet et la date sont obligatoires");
                afficherFormulaireVoyage(request, response, idStr != null ? Long.parseLong(idStr) : null);
                return;
            }

            // Récupérer le trajet
            Long trajetId = Long.parseLong(trajetIdStr);
            Optional<Trajet> trajetOpt = trajetService.trouverParId(trajetId);
            if (!trajetOpt.isPresent()) {
                request.setAttribute("error", "Trajet non trouvé");
                afficherFormulaireVoyage(request, response, idStr != null ? Long.parseLong(idStr) : null);
                return;
            }
            Trajet trajet = trajetOpt.get();

            // Parser la date
            LocalDate dateVoyage = LocalDate.parse(dateVoyageStr);

            // Vérifier que la date n'est pas dans le passé (sauf pour modification)
            if (idStr == null && dateVoyage.isBefore(LocalDate.now())) {
                request.setAttribute("error", "Impossible de créer un voyage dans le passé");
                afficherFormulaireVoyage(request, response, null);
                return;
            }

            // Créer ou modifier le voyage
            Voyage voyage;
            if (idStr != null && !idStr.trim().isEmpty()) {
                // Modification
                voyage = voyageService.trouverParId(Long.parseLong(idStr)).orElse(null);
                if (voyage == null) {
                    request.setAttribute("error", "Voyage non trouvé");
                    afficherFormulaireVoyage(request, response, null);
                    return;
                }
            } else {
                // Création
                voyage = new Voyage();
            }

            // Remplir les propriétés
            voyage.setTrajet(trajet);
            voyage.setDateVoyage(dateVoyage);

            // Places disponibles
            if (placesDisponiblesStr != null && !placesDisponiblesStr.trim().isEmpty()) {
                voyage.setPlacesDisponibles(Integer.parseInt(placesDisponiblesStr));
            } else {
                voyage.setPlacesDisponibles(trajet.getNombrePlaces());
            }

            // Statut
            if (statutStr != null && !statutStr.trim().isEmpty()) {
                voyage.setStatut(StatutVoyage.valueOf(statutStr));
            } else {
                voyage.setStatut(StatutVoyage.PROGRAMME);
            }

            // Sauvegarder
            if (voyage.getId() == null) {
                voyageService.creerVoyage(voyage);
                request.getSession().setAttribute("success", "Voyage créé avec succès");
            } else {
                voyageService.mettreAJourVoyage(voyage);
                request.getSession().setAttribute("success", "Voyage modifié avec succès");
            }

            response.sendRedirect(request.getContextPath() + "/admin/voyages");

        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors de la sauvegarde : " + e.getMessage());
            afficherFormulaireVoyage(request, response, null);
        }
    }

    /**
     * Supprimer un voyage
     */
    private void supprimerVoyage(HttpServletRequest request, HttpServletResponse response, Long voyageId)
            throws ServletException, IOException {
        try {
            boolean success = voyageService.supprimerVoyage(voyageId);

            HttpSession session = request.getSession();
            if (success) {
                session.setAttribute("success", "Voyage supprimé avec succès");
            } else {
                session.setAttribute("error", "Impossible de supprimer le voyage");
            }
        } catch (Exception e) {
            HttpSession session = request.getSession();
            session.setAttribute("error", "Erreur lors de la suppression : " + e.getMessage());
        }

        response.sendRedirect(request.getContextPath() + "/admin/voyages");
    }

    /**
     * Annuler un voyage
     */
    private void annulerVoyage(HttpServletRequest request, HttpServletResponse response, Long voyageId)
            throws ServletException, IOException {
        try {
            Voyage voyage = voyageService.trouverParId(voyageId).orElse(null);
            if (voyage != null) {
                voyage.setStatut(StatutVoyage.ANNULE);
                voyageService.mettreAJourVoyage(voyage);
                request.getSession().setAttribute("success", "Voyage annulé avec succès");
            } else {
                request.getSession().setAttribute("error", "Voyage non trouvé");
            }
        } catch (Exception e) {
            HttpSession session = request.getSession();
            session.setAttribute("error", "Erreur lors de l'annulation : " + e.getMessage());
        }

        response.sendRedirect(request.getContextPath() + "/admin/voyages");
    }
}
