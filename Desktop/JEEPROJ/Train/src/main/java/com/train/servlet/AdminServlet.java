package com.train.servlet;

import com.train.model.*;
import com.train.service.*;
import com.train.service.impl.*;
import com.train.service.GareService;
import com.train.service.impl.GareServiceImpl;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Servlet pour l'espace administrateur
 */
@WebServlet(name = "AdminServlet", urlPatterns = {"/admin", "/admin/*"})
public class AdminServlet extends HttpServlet {
    
    private UtilisateurService utilisateurService;
    private VoyageService voyageService;
    private ReservationService reservationService;
    private GareService gareService;
    
    @Override
    public void init() throws ServletException {
        // Utiliser les services de démonstration pour le moment
        utilisateurService = new DemoUtilisateurServiceImpl();
        voyageService = new DemoVoyageServiceImpl();
        reservationService = new DemoReservationServiceImpl();
        gareService = new GareServiceImpl();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // Vérifier que l'utilisateur est connecté et est admin
        if (!isAdminConnected(request, response)) {
            return;
        }
        
        String pathInfo = request.getPathInfo();
        
        if (pathInfo == null || pathInfo.equals("/")) {
            afficherDashboard(request, response);
        } else if (pathInfo.startsWith("/gares")) {
            gererGares(request, response, pathInfo);
        } else if (pathInfo.startsWith("/trajets")) {
            gererTrajets(request, response, pathInfo);
        } else if (pathInfo.startsWith("/voyages")) {
            gererVoyages(request, response, pathInfo);
        } else if (pathInfo.startsWith("/users")) {
            gererUtilisateurs(request, response, pathInfo);
        } else if (pathInfo.startsWith("/reservations")) {
            gererReservations(request, response, pathInfo);
        } else if (pathInfo.startsWith("/payments")) {
            gererPaiements(request, response, pathInfo);
        } else {
            response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // Vérifier que l'utilisateur est connecté et est admin
        if (!isAdminConnected(request, response)) {
            return;
        }
        
        String pathInfo = request.getPathInfo();
        
        if (pathInfo != null) {
            if (pathInfo.startsWith("/gares")) {
                traiterActionGares(request, response, pathInfo);
            } else if (pathInfo.startsWith("/trajets")) {
                traiterActionTrajets(request, response, pathInfo);
            } else if (pathInfo.startsWith("/voyages")) {
                traiterActionVoyages(request, response, pathInfo);
            } else if (pathInfo.startsWith("/users")) {
                traiterActionUtilisateurs(request, response, pathInfo);
            } else if (pathInfo.startsWith("/reservations")) {
                traiterActionReservations(request, response, pathInfo);
            } else {
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
            }
        }
    }
    
    /**
     * Vérifier que l'utilisateur connecté est un administrateur
     */
    private boolean isAdminConnected(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        HttpSession session = request.getSession(false);
        if (session == null) {
            response.sendRedirect(request.getContextPath() + "/login?redirect=" + 
                                request.getRequestURI());
            return false;
        }
        
        Utilisateur user = (Utilisateur) session.getAttribute("user");
        if (user == null || !user.isAdmin()) {
            response.sendError(HttpServletResponse.SC_FORBIDDEN, 
                             "Accès réservé aux administrateurs");
            return false;
        }
        
        return true;
    }
    
    /**
     * Afficher le tableau de bord administrateur
     */
    private void afficherDashboard(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // Statistiques pour le dashboard
        List<Utilisateur> utilisateurs = utilisateurService.obtenirTousLesUtilisateurs();
        List<Voyage> voyages = voyageService.obtenirTousLesVoyages();
        List<Reservation> reservations = reservationService.obtenirToutesLesReservations();
        
        // Calculer les statistiques
        long nbUtilisateurs = utilisateurs.size();
        long nbClients = utilisateurs.stream()
                .filter(u -> u.getTypeUtilisateur() == TypeUtilisateur.CLIENT)
                .count();
        long nbVoyages = voyages.size();
        long nbReservations = reservations.size();
        long nbReservationsConfirmees = reservations.stream()
                .filter(r -> r.getStatut() == StatutReservation.CONFIRMEE)
                .count();
        
        // Ajouter les attributs à la requête
        request.setAttribute("nbUtilisateurs", nbUtilisateurs);
        request.setAttribute("nbClients", nbClients);
        request.setAttribute("nbVoyages", nbVoyages);
        request.setAttribute("nbReservations", nbReservations);
        request.setAttribute("nbReservationsConfirmees", nbReservationsConfirmees);

        // Ajouter la date/heure actuelle pour l'affichage
        request.setAttribute("now", new java.util.Date());
        
        // Voyages récents
        List<Voyage> voyagesRecents = voyages.stream()
                .filter(v -> !v.isPasse())
                .sorted((v1, v2) -> v1.getDateVoyage().compareTo(v2.getDateVoyage()))
                .limit(5)
                .toList();
        request.setAttribute("voyagesRecents", voyagesRecents);
        
        // Réservations récentes
        List<Reservation> reservationsRecentes = reservations.stream()
                .sorted((r1, r2) -> r2.getDateReservation().compareTo(r1.getDateReservation()))
                .limit(5)
                .toList();
        request.setAttribute("reservationsRecentes", reservationsRecentes);
        
        request.getRequestDispatcher("/WEB-INF/views/admin/dashboard.jsp")
               .forward(request, response);
    }
    
    /**
     * Gérer les actions liées aux gares
     */
    private void gererGares(HttpServletRequest request, HttpServletResponse response, String pathInfo) 
            throws ServletException, IOException {
        
        if (pathInfo.equals("/gares") || pathInfo.equals("/gares/")) {
            // Afficher la liste des gares
            afficherListeGares(request, response);
        } else if (pathInfo.equals("/gares/new")) {
            // Afficher le formulaire de création
            afficherFormulaireGare(request, response, null);
        } else if (pathInfo.startsWith("/gares/edit/")) {
            // Afficher le formulaire d'édition
            String gareId = pathInfo.substring("/gares/edit/".length());
            afficherFormulaireGare(request, response, Long.parseLong(gareId));
        }
    }
    
    /**
     * Gérer les actions liées aux trajets
     */
    private void gererTrajets(HttpServletRequest request, HttpServletResponse response, String pathInfo) 
            throws ServletException, IOException {
        
        if (pathInfo.equals("/trajets") || pathInfo.equals("/trajets/")) {
            // Afficher la liste des trajets
            request.setAttribute("message", "Gestion des trajets - En cours d'implémentation");
            request.getRequestDispatcher("/WEB-INF/views/admin/trajets/list.jsp")
                   .forward(request, response);
        } else if (pathInfo.equals("/trajets/new")) {
            // Afficher le formulaire de création
            request.getRequestDispatcher("/WEB-INF/views/admin/trajets/form.jsp")
                   .forward(request, response);
        }
    }
    
    /**
     * Gérer les actions liées aux voyages
     */
    private void gererVoyages(HttpServletRequest request, HttpServletResponse response, String pathInfo) 
            throws ServletException, IOException {
        
        if (pathInfo.equals("/voyages") || pathInfo.equals("/voyages/")) {
            // Afficher la liste des voyages
            List<Voyage> voyages = voyageService.obtenirTousLesVoyages();
            request.setAttribute("voyages", voyages);
            request.getRequestDispatcher("/WEB-INF/views/admin/voyages/list.jsp")
                   .forward(request, response);
        } else if (pathInfo.equals("/voyages/new")) {
            // Afficher le formulaire de création
            request.getRequestDispatcher("/WEB-INF/views/admin/voyages/form.jsp")
                   .forward(request, response);
        }
    }
    
    /**
     * Gérer les actions liées aux utilisateurs
     */
    private void gererUtilisateurs(HttpServletRequest request, HttpServletResponse response, String pathInfo) 
            throws ServletException, IOException {
        
        if (pathInfo.equals("/users") || pathInfo.equals("/users/")) {
            // Afficher la liste des utilisateurs
            List<Utilisateur> utilisateurs = utilisateurService.obtenirTousLesUtilisateurs();
            request.setAttribute("utilisateurs", utilisateurs);
            request.getRequestDispatcher("/WEB-INF/views/admin/users/list.jsp")
                   .forward(request, response);
        }
    }
    
    /**
     * Gérer les actions liées aux réservations
     */
    private void gererReservations(HttpServletRequest request, HttpServletResponse response, String pathInfo) 
            throws ServletException, IOException {
        
        if (pathInfo.equals("/reservations") || pathInfo.equals("/reservations/")) {
            // Afficher la liste des réservations
            List<Reservation> reservations = reservationService.obtenirToutesLesReservations();
            request.setAttribute("reservations", reservations);
            request.getRequestDispatcher("/WEB-INF/views/admin/reservations/list.jsp")
                   .forward(request, response);
        }
    }
    
    /**
     * Gérer les actions liées aux paiements
     */
    private void gererPaiements(HttpServletRequest request, HttpServletResponse response, String pathInfo)
            throws ServletException, IOException {

        if (pathInfo.equals("/payments") || pathInfo.equals("/payments/")) {
            // Afficher la liste des paiements
            request.setAttribute("message", "Gestion des paiements - En cours d'implémentation");
            request.getRequestDispatcher("/WEB-INF/views/admin/payments/list.jsp")
                   .forward(request, response);
        }
    }

    // Méthodes pour traiter les actions POST
    private void traiterActionGares(HttpServletRequest request, HttpServletResponse response, String pathInfo)
            throws ServletException, IOException {

        if (pathInfo.equals("/gares/save")) {
            sauvegarderGare(request, response);
        } else if (pathInfo.startsWith("/gares/delete/")) {
            String gareId = pathInfo.substring("/gares/delete/".length());
            supprimerGare(request, response, Long.parseLong(gareId));
        } else if (pathInfo.startsWith("/gares/toggle/")) {
            String gareId = pathInfo.substring("/gares/toggle/".length());
            toggleGareActive(request, response, Long.parseLong(gareId));
        } else {
            response.sendRedirect(request.getContextPath() + "/admin/gares");
        }
    }

    private void traiterActionTrajets(HttpServletRequest request, HttpServletResponse response, String pathInfo)
            throws ServletException, IOException {
        // TODO: Implémenter les actions POST pour les trajets
        response.sendRedirect(request.getContextPath() + "/admin/trajets");
    }

    private void traiterActionVoyages(HttpServletRequest request, HttpServletResponse response, String pathInfo)
            throws ServletException, IOException {
        // TODO: Implémenter les actions POST pour les voyages
        response.sendRedirect(request.getContextPath() + "/admin/voyages");
    }

    private void traiterActionUtilisateurs(HttpServletRequest request, HttpServletResponse response, String pathInfo)
            throws ServletException, IOException {

        if (pathInfo.startsWith("/users/toggle/")) {
            // Activer/désactiver un utilisateur
            String userIdStr = pathInfo.substring("/users/toggle/".length());
            try {
                Long userId = Long.parseLong(userIdStr);
                boolean success = utilisateurService.toggleUtilisateurActif(userId);

                HttpSession session = request.getSession();
                if (success) {
                    session.setAttribute("successMessage", "Statut de l'utilisateur modifié avec succès");
                } else {
                    session.setAttribute("errorMessage", "Erreur lors de la modification du statut");
                }
            } catch (NumberFormatException e) {
                HttpSession session = request.getSession();
                session.setAttribute("errorMessage", "ID utilisateur invalide");
            }
        }

        response.sendRedirect(request.getContextPath() + "/admin/users");
    }

    private void traiterActionReservations(HttpServletRequest request, HttpServletResponse response, String pathInfo)
            throws ServletException, IOException {
        // TODO: Implémenter les actions POST pour les réservations
        response.sendRedirect(request.getContextPath() + "/admin/reservations");
    }

    // ========================================
    // MÉTHODES SPÉCIFIQUES POUR LES GARES
    // ========================================

    /**
     * Afficher la liste des gares
     */
    private void afficherListeGares(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        try {
            List<Gare> gares = gareService.obtenirToutesLesGares();
            request.setAttribute("gares", gares);

            // Statistiques
            long nbGaresActives = gares.stream().filter(Gare::isActive).count();
            long nbGaresInactives = gares.size() - nbGaresActives;

            request.setAttribute("nbGaresActives", nbGaresActives);
            request.setAttribute("nbGaresInactives", nbGaresInactives);

            request.getRequestDispatcher("/WEB-INF/views/admin/gares/list.jsp")
                   .forward(request, response);
        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors du chargement des gares : " + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/admin/gares/list.jsp")
                   .forward(request, response);
        }
    }

    /**
     * Afficher le formulaire de gare (création ou modification)
     */
    private void afficherFormulaireGare(HttpServletRequest request, HttpServletResponse response, Long gareId)
            throws ServletException, IOException {
        try {
            if (gareId != null) {
                // Mode modification
                Gare gare = gareService.obtenirGareParId(gareId);
                if (gare != null) {
                    request.setAttribute("gare", gare);
                } else {
                    request.setAttribute("error", "Gare non trouvée");
                }
            }
            // Mode création : pas besoin de charger une gare

            request.getRequestDispatcher("/WEB-INF/views/admin/gares/form_simple.jsp")
                   .forward(request, response);
        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors du chargement de la gare : " + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/admin/gares/form_simple.jsp")
                   .forward(request, response);
        }
    }

    /**
     * Sauvegarder une gare (création ou modification)
     */
    private void sauvegarderGare(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        try {
            // Récupérer les paramètres du formulaire
            String idStr = request.getParameter("id");
            String nom = request.getParameter("nom");
            String ville = request.getParameter("ville");
            String codeGare = request.getParameter("codeGare");
            String adresse = request.getParameter("adresse");
            String codePostal = request.getParameter("codePostal");
            String latitudeStr = request.getParameter("latitude");
            String longitudeStr = request.getParameter("longitude");
            String activeStr = request.getParameter("active");

            // Validation des champs obligatoires
            if (nom == null || nom.trim().isEmpty() ||
                ville == null || ville.trim().isEmpty() ||
                codeGare == null || codeGare.trim().isEmpty()) {

                request.setAttribute("error", "Les champs nom, ville et code gare sont obligatoires");
                afficherFormulaireGare(request, response, idStr != null ? Long.parseLong(idStr) : null);
                return;
            }

            // Vérifier l'unicité du code gare
            Gare gareExistante = gareService.obtenirGareParCode(codeGare.trim().toUpperCase());
            if (gareExistante != null && (idStr == null || !gareExistante.getId().equals(Long.parseLong(idStr)))) {
                request.setAttribute("error", "Une gare avec ce code existe déjà");
                afficherFormulaireGare(request, response, idStr != null ? Long.parseLong(idStr) : null);
                return;
            }

            // Créer ou modifier la gare
            Gare gare;
            if (idStr != null && !idStr.trim().isEmpty()) {
                // Modification
                gare = gareService.obtenirGareParId(Long.parseLong(idStr));
                if (gare == null) {
                    request.setAttribute("error", "Gare non trouvée");
                    afficherFormulaireGare(request, response, null);
                    return;
                }
                gare.setDateModification(java.time.LocalDateTime.now());
            } else {
                // Création
                gare = new Gare();
                gare.setDateCreation(java.time.LocalDateTime.now());
            }

            // Remplir les propriétés
            gare.setNom(nom.trim());
            gare.setVille(ville.trim());
            gare.setCodeGare(codeGare.trim().toUpperCase());
            gare.setAdresse(adresse != null ? adresse.trim() : null);
            gare.setCodePostal(codePostal != null ? codePostal.trim() : null);
            gare.setActive("true".equals(activeStr));

            // Gérer les coordonnées GPS
            if (latitudeStr != null && !latitudeStr.trim().isEmpty()) {
                try {
                    gare.setLatitude(Double.parseDouble(latitudeStr));
                } catch (NumberFormatException e) {
                    request.setAttribute("error", "Format de latitude invalide");
                    afficherFormulaireGare(request, response, idStr != null ? Long.parseLong(idStr) : null);
                    return;
                }
            }

            if (longitudeStr != null && !longitudeStr.trim().isEmpty()) {
                try {
                    gare.setLongitude(Double.parseDouble(longitudeStr));
                } catch (NumberFormatException e) {
                    request.setAttribute("error", "Format de longitude invalide");
                    afficherFormulaireGare(request, response, idStr != null ? Long.parseLong(idStr) : null);
                    return;
                }
            }

            // Sauvegarder
            if (gare.getId() == null) {
                gareService.creerGare(gare);
                request.getSession().setAttribute("success", "Gare créée avec succès");
            } else {
                gareService.modifierGare(gare);
                request.getSession().setAttribute("success", "Gare modifiée avec succès");
            }

            response.sendRedirect(request.getContextPath() + "/admin/gares");

        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors de la sauvegarde : " + e.getMessage());
            afficherFormulaireGare(request, response, null);
        }
    }

    /**
     * Supprimer une gare
     */
    private void supprimerGare(HttpServletRequest request, HttpServletResponse response, Long gareId)
            throws ServletException, IOException {
        try {
            boolean success = gareService.supprimerGare(gareId);

            HttpSession session = request.getSession();
            if (success) {
                session.setAttribute("success", "Gare supprimée avec succès");
            } else {
                session.setAttribute("error", "Impossible de supprimer la gare (elle est peut-être utilisée dans des trajets)");
            }
        } catch (Exception e) {
            HttpSession session = request.getSession();
            session.setAttribute("error", "Erreur lors de la suppression : " + e.getMessage());
        }

        response.sendRedirect(request.getContextPath() + "/admin/gares");
    }

    /**
     * Activer/désactiver une gare
     */
    private void toggleGareActive(HttpServletRequest request, HttpServletResponse response, Long gareId)
            throws ServletException, IOException {
        try {
            Gare gare = gareService.obtenirGareParId(gareId);
            if (gare != null) {
                gare.setActive(!gare.isActive());
                gare.setDateModification(java.time.LocalDateTime.now());
                gareService.modifierGare(gare);

                HttpSession session = request.getSession();
                session.setAttribute("success", "Statut de la gare modifié avec succès");
            } else {
                HttpSession session = request.getSession();
                session.setAttribute("error", "Gare non trouvée");
            }
        } catch (Exception e) {
            HttpSession session = request.getSession();
            session.setAttribute("error", "Erreur lors de la modification : " + e.getMessage());
        }

        response.sendRedirect(request.getContextPath() + "/admin/gares");
    }
}
