package com.train.service.impl;

import com.train.service.UtilisateurService;
import com.train.model.Utilisateur;
import com.train.model.TypeUtilisateur;
import com.train.util.DemoDataManager;
import com.train.util.PasswordUtil;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Implémentation du service utilisateur en mode démonstration
 */
public class DemoUtilisateurServiceImpl implements UtilisateurService {
    
    @Override
    public Utilisateur creerCompte(Utilisateur utilisateur, String motDePasseClair) {
        // Validation des données
        validerUtilisateur(utilisateur, motDePasseClair);
        
        // Vérifier que l'email n'existe pas déjà
        if (emailExiste(utilisateur.getEmail())) {
            throw new IllegalArgumentException("Un compte avec cet email existe déjà");
        }
        
        // Hacher le mot de passe
        String motDePasseHache = PasswordUtil.hashPassword(motDePasseClair);
        utilisateur.setMotDePasse(motDePasseHache);
        
        // Définir les valeurs par défaut
        utilisateur.setDateCreation(LocalDateTime.now());
        utilisateur.setActif(true);
        
        if (utilisateur.getTypeUtilisateur() == null) {
            utilisateur.setTypeUtilisateur(TypeUtilisateur.CLIENT);
        }
        
        return DemoDataManager.saveUtilisateur(utilisateur);
    }
    
    @Override
    public Optional<Utilisateur> authentifier(String email, String motDePasse) {
        if (email == null || email.trim().isEmpty() || motDePasse == null || motDePasse.trim().isEmpty()) {
            return Optional.empty();
        }
        
        Optional<Utilisateur> userOpt = DemoDataManager.findUtilisateurByEmail(email.trim().toLowerCase());
        if (userOpt.isPresent()) {
            Utilisateur user = userOpt.get();
            if (user.isActif() && PasswordUtil.verifyPassword(motDePasse, user.getMotDePasse())) {
                user.setDerniereConnexion(LocalDateTime.now());
                return Optional.of(user);
            }
        }
        return Optional.empty();
    }
    
    @Override
    public Utilisateur mettreAJourProfil(Utilisateur utilisateur) {
        if (utilisateur == null || utilisateur.getId() == null) {
            throw new IllegalArgumentException("Utilisateur invalide pour la mise à jour");
        }
        
        Optional<Utilisateur> existant = DemoDataManager.findUtilisateurById(utilisateur.getId());
        if (!existant.isPresent()) {
            throw new IllegalArgumentException("Utilisateur non trouvé");
        }
        
        validerUtilisateur(utilisateur, null);
        return DemoDataManager.saveUtilisateur(utilisateur);
    }
    
    @Override
    public boolean changerMotDePasse(Long utilisateurId, String ancienMotDePasse, String nouveauMotDePasse) {
        if (utilisateurId == null || ancienMotDePasse == null || nouveauMotDePasse == null) {
            return false;
        }
        
        Optional<Utilisateur> utilisateurOpt = DemoDataManager.findUtilisateurById(utilisateurId);
        if (!utilisateurOpt.isPresent()) {
            return false;
        }
        
        Utilisateur utilisateur = utilisateurOpt.get();
        
        if (!PasswordUtil.verifyPassword(ancienMotDePasse, utilisateur.getMotDePasse())) {
            return false;
        }
        
        String messageErreur = PasswordUtil.getPasswordStrengthMessage(nouveauMotDePasse);
        if (messageErreur != null) {
            throw new IllegalArgumentException(messageErreur);
        }
        
        String nouveauMotDePasseHache = PasswordUtil.hashPassword(nouveauMotDePasse);
        utilisateur.setMotDePasse(nouveauMotDePasseHache);
        DemoDataManager.saveUtilisateur(utilisateur);
        return true;
    }
    
    @Override
    public Optional<Utilisateur> trouverParId(Long id) {
        if (id == null) {
            return Optional.empty();
        }
        return DemoDataManager.findUtilisateurById(id);
    }
    
    @Override
    public Optional<Utilisateur> trouverParEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return Optional.empty();
        }
        return DemoDataManager.findUtilisateurByEmail(email.trim().toLowerCase());
    }
    
    @Override
    public boolean emailExiste(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        return DemoDataManager.findUtilisateurByEmail(email.trim().toLowerCase()).isPresent();
    }
    
    @Override
    public List<Utilisateur> obtenirTousLesUtilisateurs() {
        return DemoDataManager.getAllUtilisateurs();
    }
    
    @Override
    public List<Utilisateur> obtenirUtilisateursParType(TypeUtilisateur type) {
        if (type == null) {
            throw new IllegalArgumentException("Le type d'utilisateur ne peut pas être null");
        }
        return DemoDataManager.getAllUtilisateurs().stream()
                .filter(u -> u.getTypeUtilisateur() == type)
                .collect(java.util.stream.Collectors.toList());
    }
    
    @Override
    public List<Utilisateur> obtenirUtilisateursActifs() {
        return DemoDataManager.getAllUtilisateurs().stream()
                .filter(Utilisateur::isActif)
                .collect(java.util.stream.Collectors.toList());
    }
    
    @Override
    public boolean changerStatutUtilisateur(Long utilisateurId, boolean actif) {
        if (utilisateurId == null) {
            return false;
        }

        Optional<Utilisateur> utilisateurOpt = DemoDataManager.findUtilisateurById(utilisateurId);
        if (!utilisateurOpt.isPresent()) {
            return false;
        }

        Utilisateur utilisateur = utilisateurOpt.get();
        utilisateur.setActif(actif);
        DemoDataManager.saveUtilisateur(utilisateur);
        return true;
    }

    @Override
    public boolean toggleUtilisateurActif(Long utilisateurId) {
        if (utilisateurId == null) {
            return false;
        }

        Optional<Utilisateur> utilisateurOpt = DemoDataManager.findUtilisateurById(utilisateurId);
        if (!utilisateurOpt.isPresent()) {
            return false;
        }

        Utilisateur utilisateur = utilisateurOpt.get();
        utilisateur.setActif(!utilisateur.isActif());
        DemoDataManager.saveUtilisateur(utilisateur);
        return true;
    }
    
    @Override
    public void validerUtilisateur(Utilisateur utilisateur, String motDePasse) {
        if (utilisateur == null) {
            throw new IllegalArgumentException("L'utilisateur ne peut pas être null");
        }
        
        if (utilisateur.getNom() == null || utilisateur.getNom().trim().isEmpty()) {
            throw new IllegalArgumentException("Le nom est requis");
        }
        if (utilisateur.getNom().trim().length() < 2 || utilisateur.getNom().trim().length() > 100) {
            throw new IllegalArgumentException("Le nom doit contenir entre 2 et 100 caractères");
        }
        
        if (utilisateur.getPrenom() == null || utilisateur.getPrenom().trim().isEmpty()) {
            throw new IllegalArgumentException("Le prénom est requis");
        }
        if (utilisateur.getPrenom().trim().length() < 2 || utilisateur.getPrenom().trim().length() > 100) {
            throw new IllegalArgumentException("Le prénom doit contenir entre 2 et 100 caractères");
        }
        
        if (utilisateur.getEmail() == null || utilisateur.getEmail().trim().isEmpty()) {
            throw new IllegalArgumentException("L'email est requis");
        }
        if (!isValidEmail(utilisateur.getEmail())) {
            throw new IllegalArgumentException("Format d'email invalide");
        }
        
        if (utilisateur.getTelephone() != null && !utilisateur.getTelephone().trim().isEmpty()) {
            if (!isValidPhone(utilisateur.getTelephone())) {
                throw new IllegalArgumentException("Format de téléphone invalide");
            }
        }
        
        if (motDePasse != null) {
            String messageErreur = PasswordUtil.getPasswordStrengthMessage(motDePasse);
            if (messageErreur != null) {
                throw new IllegalArgumentException(messageErreur);
            }
        }
        
        // Normaliser les données
        utilisateur.setNom(utilisateur.getNom().trim());
        utilisateur.setPrenom(utilisateur.getPrenom().trim());
        utilisateur.setEmail(utilisateur.getEmail().trim().toLowerCase());
        if (utilisateur.getTelephone() != null) {
            utilisateur.setTelephone(utilisateur.getTelephone().trim());
        }
    }
    
    private boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        String emailRegex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        return email.matches(emailRegex);
    }
    
    private boolean isValidPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return true;
        }
        String phoneRegex = "^(?:(?:\\+33|0)[1-9](?:[0-9]{8}))$";
        String cleanPhone = phone.replaceAll("[\\s.-]", "");
        return cleanPhone.matches(phoneRegex);
    }
}
