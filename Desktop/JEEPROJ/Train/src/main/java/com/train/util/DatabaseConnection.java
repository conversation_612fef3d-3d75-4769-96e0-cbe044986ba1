package com.train.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * Classe utilitaire pour la gestion des connexions à la base de données
 * Fonctionne en mode dégradé si MySQL n'est pas disponible
 */
public class DatabaseConnection {
    
    private static final String URL = "**********************************************************************************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "";
    private static final String DRIVER_CLASS = "com.mysql.cj.jdbc.Driver";
    
    private static boolean databaseAvailable = false;
    private static boolean checkedDatabase = false;
    
    static {
        try {
            Class.forName(DRIVER_CLASS);
            System.out.println("✅ Driver MySQL chargé avec succès");
        } catch (ClassNotFoundException e) {
            System.err.println("❌ Driver MySQL non trouvé: " + e.getMessage());
        }
    }
    
    /**
     * Obtient une connexion à la base de données
     * @return Connection à la base de données
     * @throws SQLException si la connexion échoue
     */
    public static Connection getConnection() throws SQLException {
        try {
            Connection conn = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            if (!checkedDatabase) {
                System.out.println("✅ Connexion à la base de données réussie");
                databaseAvailable = true;
                checkedDatabase = true;
            }
            return conn;
        } catch (SQLException e) {
            if (!checkedDatabase) {
                System.err.println("⚠️ Base de données non disponible: " + e.getMessage());
                System.err.println("🔄 L'application fonctionnera en mode démonstration");
                databaseAvailable = false;
                checkedDatabase = true;
            }
            throw e;
        }
    }
    
    /**
     * Ferme une connexion de manière sécurisée
     * @param connection la connexion à fermer
     */
    public static void closeConnection(Connection connection) {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                System.err.println("Erreur lors de la fermeture de la connexion: " + e.getMessage());
            }
        }
    }
    
    /**
     * Vérifie si la base de données est disponible
     * @return true si la DB est disponible, false sinon
     */
    public static boolean isDatabaseAvailable() {
        if (!checkedDatabase) {
            try (Connection conn = getConnection()) {
                return conn != null && !conn.isClosed();
            } catch (SQLException e) {
                return false;
            }
        }
        return databaseAvailable;
    }
    
    /**
     * Test de connexion simple
     * @return true si la connexion fonctionne
     */
    public static boolean testConnection() {
        try (Connection conn = getConnection()) {
            return conn != null && !conn.isClosed();
        } catch (SQLException e) {
            return false;
        }
    }
}
