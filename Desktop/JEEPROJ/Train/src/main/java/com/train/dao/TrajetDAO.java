package com.train.dao;

import com.train.model.Trajet;
import com.train.model.Gare;
import java.util.List;

/**
 * Interface DAO pour les opérations sur les trajets
 */
public interface TrajetDAO extends BaseDAO<Trajet, Long> {
    
    /**
     * Trouve les trajets entre deux gares
     * @param gareDepart la gare de départ
     * @param gareArrivee la gare d'arrivée
     * @return liste des trajets entre ces gares
     */
    List<Trajet> findByGares(Gare gareDepart, Gare gareArrivee);
    
    /**
     * Trouve les trajets entre deux gares (par ID)
     * @param gareDepartId l'ID de la gare de départ
     * @param gareArriveeId l'ID de la gare d'arrivée
     * @return liste des trajets entre ces gares
     */
    List<Trajet> findByGareIds(Long gareDepartId, Long gareArriveeId);
    
    /**
     * Trouve les trajets partant d'une gare
     * @param gareDepart la gare de départ
     * @return liste des trajets partant de cette gare
     */
    List<Trajet> findByGareDepart(Gare gareDepart);
    
    /**
     * Trouve les trajets arrivant à une gare
     * @param gareArrivee la gare d'arrivée
     * @return liste des trajets arrivant à cette gare
     */
    List<Trajet> findByGareArrivee(Gare gareArrivee);
    
    /**
     * Trouve les trajets actifs
     * @return liste des trajets actifs
     */
    List<Trajet> findActiveTrajects();

    /**
     * Trouve les trajets par statut actif/inactif
     * @param actif true pour les trajets actifs, false pour les inactifs
     * @return liste des trajets filtrés par statut
     */
    List<Trajet> findByActif(boolean actif);
    
    /**
     * Trouve les trajets par gare (départ ou arrivée)
     * @param gare la gare
     * @return liste des trajets passant par cette gare
     */
    List<Trajet> findByGare(Gare gare);
    
    /**
     * Recherche les trajets disponibles
     * @param villeDepart ville de départ
     * @param villeArrivee ville d'arrivée
     * @return liste des trajets disponibles
     */
    List<Trajet> searchTrajets(String villeDepart, String villeArrivee);
}
