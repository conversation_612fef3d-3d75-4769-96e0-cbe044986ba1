package com.train.dao.impl;

import com.train.dao.AffectationDAO;
import com.train.model.Affectation;
import com.train.model.RoleVoyage;
import com.train.util.DatabaseConnection;
import java.sql.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Implémentation DAO pour la gestion des affectations d'employés
 */
public class AffectationDAOImpl implements AffectationDAO {
    
    private static final String INSERT_SQL = 
        "INSERT INTO affectations (voyage_id, employe_id, role_voyage, date_affectation, actif, commentaire) " +
        "VALUES (?, ?, ?, ?, ?, ?)";
    
    private static final String SELECT_BY_ID_SQL = 
        "SELECT * FROM affectations WHERE id = ?";
    
    private static final String SELECT_ALL_SQL = 
        "SELECT * FROM affectations ORDER BY date_affectation DESC";
    
    private static final String SELECT_BY_VOYAGE_SQL = 
        "SELECT * FROM affectations WHERE voyage_id = ? ORDER BY date_affectation DESC";
    
    private static final String SELECT_BY_EMPLOYE_SQL = 
        "SELECT * FROM affectations WHERE employe_id = ? ORDER BY date_affectation DESC";
    
    private static final String SELECT_ACTIVES_BY_EMPLOYE_SQL = 
        "SELECT * FROM affectations WHERE employe_id = ? AND actif = TRUE ORDER BY date_affectation DESC";
    
    private static final String SELECT_BY_ROLE_SQL = 
        "SELECT * FROM affectations WHERE role_voyage = ? ORDER BY date_affectation DESC";
    
    private static final String UPDATE_SQL = 
        "UPDATE affectations SET voyage_id = ?, employe_id = ?, role_voyage = ?, " +
        "date_modification = ?, actif = ?, commentaire = ? WHERE id = ?";
    
    private static final String DELETE_SQL = 
        "DELETE FROM affectations WHERE id = ?";
    
    private static final String DEACTIVATE_SQL = 
        "UPDATE affectations SET actif = FALSE, date_modification = ? WHERE id = ?";
    
    private static final String EXISTS_AFFECTATION_SQL = 
        "SELECT COUNT(*) FROM affectations WHERE voyage_id = ? AND employe_id = ? AND role_voyage = ? AND actif = TRUE";
    
    private static final String COUNT_BY_STATUS_SQL = 
        "SELECT COUNT(*) FROM affectations WHERE actif = ?";
    
    private static final String COUNT_BY_ROLE_SQL = 
        "SELECT COUNT(*) FROM affectations WHERE role_voyage = ? AND actif = TRUE";
    
    @Override
    public Affectation creer(Affectation affectation) throws SQLException {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(INSERT_SQL, Statement.RETURN_GENERATED_KEYS)) {
            
            stmt.setLong(1, affectation.getVoyageId());
            stmt.setLong(2, affectation.getEmployeId());
            stmt.setString(3, affectation.getRole().name());
            stmt.setTimestamp(4, Timestamp.valueOf(affectation.getDateAffectation()));
            stmt.setBoolean(5, affectation.isActif());
            stmt.setString(6, affectation.getCommentaire());
            
            int rowsAffected = stmt.executeUpdate();
            if (rowsAffected == 0) {
                throw new SQLException("Échec de la création de l'affectation");
            }
            
            try (ResultSet generatedKeys = stmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    affectation.setId(generatedKeys.getLong(1));
                }
            }
            
            return affectation;
        }
    }
    
    @Override
    public Optional<Affectation> obtenirParId(Long id) throws SQLException {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(SELECT_BY_ID_SQL)) {
            
            stmt.setLong(1, id);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToAffectation(rs));
                }
            }
        }
        return Optional.empty();
    }
    
    @Override
    public List<Affectation> obtenirTous() throws SQLException {
        List<Affectation> affectations = new ArrayList<>();
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(SELECT_ALL_SQL);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                affectations.add(mapResultSetToAffectation(rs));
            }
        }
        
        return affectations;
    }
    
    @Override
    public List<Affectation> obtenirParVoyage(Long voyageId) throws SQLException {
        List<Affectation> affectations = new ArrayList<>();
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(SELECT_BY_VOYAGE_SQL)) {
            
            stmt.setLong(1, voyageId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    affectations.add(mapResultSetToAffectation(rs));
                }
            }
        }
        
        return affectations;
    }
    
    @Override
    public List<Affectation> obtenirParEmploye(Long employeId) throws SQLException {
        List<Affectation> affectations = new ArrayList<>();
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(SELECT_BY_EMPLOYE_SQL)) {
            
            stmt.setLong(1, employeId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    affectations.add(mapResultSetToAffectation(rs));
                }
            }
        }
        
        return affectations;
    }
    
    @Override
    public List<Affectation> obtenirActivesParEmploye(Long employeId) throws SQLException {
        List<Affectation> affectations = new ArrayList<>();
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(SELECT_ACTIVES_BY_EMPLOYE_SQL)) {
            
            stmt.setLong(1, employeId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    affectations.add(mapResultSetToAffectation(rs));
                }
            }
        }
        
        return affectations;
    }
    
    @Override
    public List<Affectation> obtenirParRole(RoleVoyage role) throws SQLException {
        List<Affectation> affectations = new ArrayList<>();
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(SELECT_BY_ROLE_SQL)) {
            
            stmt.setString(1, role.name());
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    affectations.add(mapResultSetToAffectation(rs));
                }
            }
        }
        
        return affectations;
    }
    
    @Override
    public List<Affectation> obtenirParPeriode(LocalDate dateDebut, LocalDate dateFin) throws SQLException {
        String sql = "SELECT a.* FROM affectations a " +
                    "JOIN voyages v ON a.voyage_id = v.id " +
                    "WHERE v.date_voyage BETWEEN ? AND ? " +
                    "ORDER BY v.date_voyage, a.date_affectation";
        
        List<Affectation> affectations = new ArrayList<>();
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setDate(1, Date.valueOf(dateDebut));
            stmt.setDate(2, Date.valueOf(dateFin));
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    affectations.add(mapResultSetToAffectation(rs));
                }
            }
        }
        
        return affectations;
    }
    
    @Override
    public Affectation mettreAJour(Affectation affectation) throws SQLException {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(UPDATE_SQL)) {
            
            stmt.setLong(1, affectation.getVoyageId());
            stmt.setLong(2, affectation.getEmployeId());
            stmt.setString(3, affectation.getRole().name());
            stmt.setTimestamp(4, Timestamp.valueOf(LocalDateTime.now()));
            stmt.setBoolean(5, affectation.isActif());
            stmt.setString(6, affectation.getCommentaire());
            stmt.setLong(7, affectation.getId());
            
            int rowsAffected = stmt.executeUpdate();
            if (rowsAffected == 0) {
                throw new SQLException("Échec de la mise à jour de l'affectation");
            }
            
            affectation.setDateModification(LocalDateTime.now());
            return affectation;
        }
    }
    
    @Override
    public boolean supprimer(Long id) throws SQLException {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(DELETE_SQL)) {
            
            stmt.setLong(1, id);
            return stmt.executeUpdate() > 0;
        }
    }
    
    @Override
    public boolean desactiver(Long id) throws SQLException {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(DEACTIVATE_SQL)) {
            
            stmt.setTimestamp(1, Timestamp.valueOf(LocalDateTime.now()));
            stmt.setLong(2, id);
            
            return stmt.executeUpdate() > 0;
        }
    }
    
    @Override
    public boolean existeAffectation(Long voyageId, Long employeId, RoleVoyage role) throws SQLException {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(EXISTS_AFFECTATION_SQL)) {
            
            stmt.setLong(1, voyageId);
            stmt.setLong(2, employeId);
            stmt.setString(3, role.name());
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        }
        return false;
    }
    
    @Override
    public long compterParStatut(boolean actif) throws SQLException {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(COUNT_BY_STATUS_SQL)) {
            
            stmt.setBoolean(1, actif);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getLong(1);
                }
            }
        }
        return 0;
    }
    
    @Override
    public long compterParRole(RoleVoyage role) throws SQLException {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(COUNT_BY_ROLE_SQL)) {
            
            stmt.setString(1, role.name());
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getLong(1);
                }
            }
        }
        return 0;
    }
    
    @Override
    public List<Long> obtenirEmployesAffectes(Long voyageId) throws SQLException {
        String sql = "SELECT DISTINCT employe_id FROM affectations WHERE voyage_id = ? AND actif = TRUE";
        List<Long> employeIds = new ArrayList<>();
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setLong(1, voyageId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    employeIds.add(rs.getLong("employe_id"));
                }
            }
        }
        
        return employeIds;
    }
    
    @Override
    public List<Long> obtenirVoyagesAssignes(Long employeId, LocalDate dateDebut, LocalDate dateFin) throws SQLException {
        String sql = "SELECT DISTINCT a.voyage_id FROM affectations a " +
                    "JOIN voyages v ON a.voyage_id = v.id " +
                    "WHERE a.employe_id = ? AND a.actif = TRUE " +
                    "AND v.date_voyage BETWEEN ? AND ? " +
                    "ORDER BY v.date_voyage";
        
        List<Long> voyageIds = new ArrayList<>();
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setLong(1, employeId);
            stmt.setDate(2, Date.valueOf(dateDebut));
            stmt.setDate(3, Date.valueOf(dateFin));
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    voyageIds.add(rs.getLong("voyage_id"));
                }
            }
        }
        
        return voyageIds;
    }
    
    /**
     * Mapper un ResultSet vers un objet Affectation
     */
    private Affectation mapResultSetToAffectation(ResultSet rs) throws SQLException {
        Affectation affectation = new Affectation();
        
        affectation.setId(rs.getLong("id"));
        affectation.setVoyageId(rs.getLong("voyage_id"));
        affectation.setEmployeId(rs.getLong("employe_id"));
        affectation.setRole(RoleVoyage.valueOf(rs.getString("role_voyage")));
        
        Timestamp dateAffectation = rs.getTimestamp("date_affectation");
        if (dateAffectation != null) {
            affectation.setDateAffectation(dateAffectation.toLocalDateTime());
        }
        
        Timestamp dateModification = rs.getTimestamp("date_modification");
        if (dateModification != null) {
            affectation.setDateModification(dateModification.toLocalDateTime());
        }
        
        affectation.setActif(rs.getBoolean("actif"));
        affectation.setCommentaire(rs.getString("commentaire"));
        
        return affectation;
    }
}
