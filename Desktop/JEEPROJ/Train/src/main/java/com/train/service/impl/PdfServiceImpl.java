package com.train.service.impl;

import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.property.TextAlignment;
import com.itextpdf.layout.property.UnitValue;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.colors.ColorConstants;
import com.train.model.Reservation;
import com.train.service.PdfService;

import java.io.ByteArrayOutputStream;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * Implémentation du service de génération PDF
 */
public class PdfServiceImpl implements PdfService {
    
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("dd/MM/yyyy HH:mm");
    private static final SimpleDateFormat DATE_ONLY_FORMAT = new SimpleDateFormat("dd/MM/yyyy");
    
    @Override
    public byte[] genererBilletPdf(Reservation reservation) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        PdfWriter writer = new PdfWriter(baos);
        PdfDocument pdf = new PdfDocument(writer);
        Document document = new Document(pdf);
        
        try {
            // Police pour le titre
            PdfFont titleFont = PdfFontFactory.createFont();
            PdfFont normalFont = PdfFontFactory.createFont();
            
            // En-tête
            Paragraph title = new Paragraph("BILLET DE TRAIN")
                .setFont(titleFont)
                .setFontSize(20)
                .setTextAlignment(TextAlignment.CENTER)
                .setFontColor(ColorConstants.BLUE)
                .setMarginBottom(20);
            document.add(title);
            
            // Informations de la compagnie
            Paragraph company = new Paragraph("TrainSystem - Compagnie Ferroviaire")
                .setFont(normalFont)
                .setFontSize(12)
                .setTextAlignment(TextAlignment.CENTER)
                .setMarginBottom(30);
            document.add(company);
            
            // Informations du voyage
            Table voyageTable = new Table(UnitValue.createPercentArray(new float[]{1, 1}))
                .setWidth(UnitValue.createPercentValue(100));
            
            // Ligne 1
            voyageTable.addCell(new Cell().add(new Paragraph("Numéro de réservation:").setFont(normalFont).setBold()));
            voyageTable.addCell(new Cell().add(new Paragraph("RES-" + reservation.getId()).setFont(normalFont)));
            
            // Ligne 2
            voyageTable.addCell(new Cell().add(new Paragraph("Passager:").setFont(normalFont).setBold()));
            voyageTable.addCell(new Cell().add(new Paragraph(
                reservation.getUtilisateur().getPrenom() + " " + 
                reservation.getUtilisateur().getNom()).setFont(normalFont)));
            
            // Ligne 3
            voyageTable.addCell(new Cell().add(new Paragraph("Date de voyage:").setFont(normalFont).setBold()));
            voyageTable.addCell(new Cell().add(new Paragraph(
                DATE_ONLY_FORMAT.format(reservation.getVoyage().getDateDepart())).setFont(normalFont)));
            
            // Ligne 4
            voyageTable.addCell(new Cell().add(new Paragraph("Heure de départ:").setFont(normalFont).setBold()));
            voyageTable.addCell(new Cell().add(new Paragraph(
                DATE_FORMAT.format(reservation.getVoyage().getDateDepart())).setFont(normalFont)));
            
            // Ligne 5
            voyageTable.addCell(new Cell().add(new Paragraph("Gare de départ:").setFont(normalFont).setBold()));
            voyageTable.addCell(new Cell().add(new Paragraph(
                reservation.getVoyage().getTrajet().getGareDepart().getNom()).setFont(normalFont)));
            
            // Ligne 6
            voyageTable.addCell(new Cell().add(new Paragraph("Gare d'arrivée:").setFont(normalFont).setBold()));
            voyageTable.addCell(new Cell().add(new Paragraph(
                reservation.getVoyage().getTrajet().getGareArrivee().getNom()).setFont(normalFont)));
            
            // Ligne 7
            voyageTable.addCell(new Cell().add(new Paragraph("Nombre de places:").setFont(normalFont).setBold()));
            voyageTable.addCell(new Cell().add(new Paragraph(
                String.valueOf(reservation.getNombrePlaces())).setFont(normalFont)));
            
            // Ligne 8
            voyageTable.addCell(new Cell().add(new Paragraph("Prix total:").setFont(normalFont).setBold()));
            voyageTable.addCell(new Cell().add(new Paragraph(
                String.format("%.2f €", reservation.getPrixTotal())).setFont(normalFont)));
            
            // Ligne 9
            voyageTable.addCell(new Cell().add(new Paragraph("Statut:").setFont(normalFont).setBold()));
            voyageTable.addCell(new Cell().add(new Paragraph(
                reservation.getStatut().toString()).setFont(normalFont)));
            
            document.add(voyageTable);
            
            // Instructions
            Paragraph instructions = new Paragraph("\n\nInstructions:")
                .setFont(normalFont)
                .setBold()
                .setFontSize(14)
                .setMarginTop(30);
            document.add(instructions);
            
            Paragraph instructionText = new Paragraph(
                "• Présentez-vous en gare 30 minutes avant le départ\n" +
                "• Munissez-vous d'une pièce d'identité valide\n" +
                "• Ce billet est nominatif et non remboursable sauf conditions particulières\n" +
                "• En cas de retard, contactez le service client")
                .setFont(normalFont)
                .setFontSize(10)
                .setMarginTop(10);
            document.add(instructionText);
            
            // Code-barres simulé
            Paragraph barcode = new Paragraph("\n\n||||| |||| | ||| |||| ||||| | |||| |||||")
                .setFont(normalFont)
                .setTextAlignment(TextAlignment.CENTER)
                .setFontSize(16)
                .setMarginTop(30);
            document.add(barcode);
            
            Paragraph barcodeText = new Paragraph("Code: RES-" + reservation.getId() + "-" + 
                reservation.getVoyage().getId())
                .setFont(normalFont)
                .setTextAlignment(TextAlignment.CENTER)
                .setFontSize(8);
            document.add(barcodeText);
            
        } finally {
            document.close();
        }
        
        return baos.toByteArray();
    }
    
    @Override
    public byte[] genererRapportReservations(List<Reservation> reservations, String titre) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        PdfWriter writer = new PdfWriter(baos);
        PdfDocument pdf = new PdfDocument(writer);
        Document document = new Document(pdf);
        
        try {
            PdfFont titleFont = PdfFontFactory.createFont();
            PdfFont normalFont = PdfFontFactory.createFont();
            
            // Titre
            Paragraph titlePara = new Paragraph(titre)
                .setFont(titleFont)
                .setFontSize(18)
                .setTextAlignment(TextAlignment.CENTER)
                .setMarginBottom(20);
            document.add(titlePara);
            
            // Date de génération
            Paragraph datePara = new Paragraph("Généré le: " + DATE_FORMAT.format(new java.util.Date()))
                .setFont(normalFont)
                .setFontSize(10)
                .setTextAlignment(TextAlignment.RIGHT)
                .setMarginBottom(20);
            document.add(datePara);
            
            // Tableau des réservations
            Table table = new Table(UnitValue.createPercentArray(new float[]{1, 2, 2, 2, 1, 1, 1}))
                .setWidth(UnitValue.createPercentValue(100));
            
            // En-têtes
            table.addHeaderCell(new Cell().add(new Paragraph("ID").setFont(normalFont).setBold()));
            table.addHeaderCell(new Cell().add(new Paragraph("Passager").setFont(normalFont).setBold()));
            table.addHeaderCell(new Cell().add(new Paragraph("Trajet").setFont(normalFont).setBold()));
            table.addHeaderCell(new Cell().add(new Paragraph("Date").setFont(normalFont).setBold()));
            table.addHeaderCell(new Cell().add(new Paragraph("Places").setFont(normalFont).setBold()));
            table.addHeaderCell(new Cell().add(new Paragraph("Prix").setFont(normalFont).setBold()));
            table.addHeaderCell(new Cell().add(new Paragraph("Statut").setFont(normalFont).setBold()));
            
            // Données
            for (Reservation reservation : reservations) {
                table.addCell(new Cell().add(new Paragraph(String.valueOf(reservation.getId())).setFont(normalFont)));
                table.addCell(new Cell().add(new Paragraph(
                    reservation.getUtilisateur().getPrenom() + " " + 
                    reservation.getUtilisateur().getNom()).setFont(normalFont)));
                table.addCell(new Cell().add(new Paragraph(
                    reservation.getVoyage().getTrajet().getGareDepart().getNom() + " → " +
                    reservation.getVoyage().getTrajet().getGareArrivee().getNom()).setFont(normalFont)));
                table.addCell(new Cell().add(new Paragraph(
                    DATE_ONLY_FORMAT.format(reservation.getVoyage().getDateDepart())).setFont(normalFont)));
                table.addCell(new Cell().add(new Paragraph(
                    String.valueOf(reservation.getNombrePlaces())).setFont(normalFont)));
                table.addCell(new Cell().add(new Paragraph(
                    String.format("%.2f €", reservation.getPrixTotal())).setFont(normalFont)));
                table.addCell(new Cell().add(new Paragraph(
                    reservation.getStatut().toString()).setFont(normalFont)));
            }
            
            document.add(table);
            
            // Résumé
            double totalRevenu = reservations.stream()
                .mapToDouble(Reservation::getPrixTotal)
                .sum();
            
            Paragraph summary = new Paragraph("\n\nRésumé:")
                .setFont(normalFont)
                .setBold()
                .setFontSize(14);
            document.add(summary);
            
            Paragraph summaryText = new Paragraph(
                "Nombre total de réservations: " + reservations.size() + "\n" +
                "Revenu total: " + String.format("%.2f €", totalRevenu))
                .setFont(normalFont)
                .setFontSize(12);
            document.add(summaryText);
            
        } finally {
            document.close();
        }
        
        return baos.toByteArray();
    }
}
