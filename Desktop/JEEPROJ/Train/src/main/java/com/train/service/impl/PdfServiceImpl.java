package com.train.service.impl;

import com.lowagie.text.Document;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Element;
import com.lowagie.text.Font;
import com.lowagie.text.FontFactory;
import com.lowagie.text.Paragraph;
import com.lowagie.text.Table;
import com.lowagie.text.pdf.PdfWriter;
import com.train.model.Reservation;
import com.train.service.PdfService;

import java.awt.Color;
import java.io.ByteArrayOutputStream;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * Implémentation du service de génération PDF
 */
public class PdfServiceImpl implements PdfService {
    
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("dd/MM/yyyy HH:mm");
    private static final SimpleDateFormat DATE_ONLY_FORMAT = new SimpleDateFormat("dd/MM/yyyy");
    
    @Override
    public byte[] genererBilletPdf(Reservation reservation) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Document document = new Document();
        PdfWriter writer = PdfWriter.getInstance(document, baos);

        try {
            document.open();

            // Polices
            Font titleFont = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 20, Color.BLUE);
            Font normalFont = FontFactory.getFont(FontFactory.HELVETICA, 12, Color.BLACK);
            Font boldFont = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 12, Color.BLACK);

            // En-tête
            Paragraph title = new Paragraph("BILLET DE TRAIN", titleFont);
            title.setAlignment(Element.ALIGN_CENTER);
            title.setSpacingAfter(20);
            document.add(title);

            // Informations de la compagnie
            Paragraph company = new Paragraph("TrainSystem - Compagnie Ferroviaire", normalFont);
            company.setAlignment(Element.ALIGN_CENTER);
            company.setSpacingAfter(30);
            document.add(company);

            // Informations du billet
            document.add(new Paragraph("Numéro de réservation: RES-" + reservation.getId(), boldFont));
            document.add(new Paragraph("Passager: " + reservation.getUtilisateur().getPrenom() + " " +
                reservation.getUtilisateur().getNom(), normalFont));
            document.add(new Paragraph("Date de voyage: " +
                DATE_ONLY_FORMAT.format(reservation.getVoyage().getDateVoyage()), normalFont));
            document.add(new Paragraph("Heure de départ: " +
                DATE_FORMAT.format(reservation.getVoyage().getDateVoyage()), normalFont));
            document.add(new Paragraph("Gare de départ: " +
                reservation.getVoyage().getTrajet().getGareDepart().getNom(), normalFont));
            document.add(new Paragraph("Gare d'arrivée: " +
                reservation.getVoyage().getTrajet().getGareArrivee().getNom(), normalFont));
            document.add(new Paragraph("Nombre de places: " +
                reservation.getNombrePlaces(), normalFont));
            document.add(new Paragraph("Prix total: " +
                String.format("%.2f €", reservation.getPrixTotal()), boldFont));
            document.add(new Paragraph("Statut: " +
                reservation.getStatut().toString(), normalFont));

            // Instructions
            Paragraph instructions = new Paragraph("\nInstructions:", boldFont);
            instructions.setSpacingBefore(20);
            document.add(instructions);

            document.add(new Paragraph("• Présentez-vous en gare 30 minutes avant le départ", normalFont));
            document.add(new Paragraph("• Munissez-vous d'une pièce d'identité valide", normalFont));
            document.add(new Paragraph("• Ce billet est nominatif et non remboursable sauf conditions particulières", normalFont));
            document.add(new Paragraph("• En cas de retard, contactez le service client", normalFont));

            // Code-barres simulé
            Paragraph barcode = new Paragraph("\n||||| |||| | ||| |||| ||||| | |||| |||||", normalFont);
            barcode.setAlignment(Element.ALIGN_CENTER);
            barcode.setSpacingBefore(30);
            document.add(barcode);

            Paragraph barcodeText = new Paragraph("Code: RES-" + reservation.getId() + "-" +
                reservation.getVoyage().getId(), normalFont);
            barcodeText.setAlignment(Element.ALIGN_CENTER);
            document.add(barcodeText);

            
        } finally {
            document.close();
        }
        
        return baos.toByteArray();
    }
    
    @Override
    public byte[] genererRapportReservations(List<Reservation> reservations, String titre) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Document document = new Document();
        PdfWriter writer = PdfWriter.getInstance(document, baos);

        try {
            document.open();

            Font titleFont = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 18, Color.BLACK);
            Font normalFont = FontFactory.getFont(FontFactory.HELVETICA, 12, Color.BLACK);

            // Titre
            Paragraph titlePara = new Paragraph(titre, titleFont);
            titlePara.setAlignment(Element.ALIGN_CENTER);
            titlePara.setSpacingAfter(20);
            document.add(titlePara);

            // Date de génération
            Paragraph datePara = new Paragraph("Généré le: " + DATE_FORMAT.format(new java.util.Date()), normalFont);
            datePara.setAlignment(Element.ALIGN_RIGHT);
            datePara.setSpacingAfter(20);
            document.add(datePara);

            // Liste des réservations
            for (Reservation reservation : reservations) {
                document.add(new Paragraph("ID: " + reservation.getId() +
                    " | Passager: " + reservation.getUtilisateur().getPrenom() + " " +
                    reservation.getUtilisateur().getNom() +
                    " | Trajet: " + reservation.getVoyage().getTrajet().getGareDepart().getNom() + " → " +
                    reservation.getVoyage().getTrajet().getGareArrivee().getNom() +
                    " | Prix: " + String.format("%.2f €", reservation.getPrixTotal()), normalFont));
            }

            // Résumé
            double totalRevenu = reservations.stream()
                .mapToDouble(r -> r.getPrixTotal().doubleValue())
                .sum();

            Paragraph summary = new Paragraph("\nRésumé:", titleFont);
            summary.setSpacingBefore(20);
            document.add(summary);

            document.add(new Paragraph("Nombre total de réservations: " + reservations.size(), normalFont));
            document.add(new Paragraph("Revenu total: " + String.format("%.2f €", totalRevenu), normalFont));

        } finally {
            document.close();
        }

        return baos.toByteArray();
    }
}
