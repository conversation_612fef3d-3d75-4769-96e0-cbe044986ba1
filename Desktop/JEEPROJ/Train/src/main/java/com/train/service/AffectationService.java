package com.train.service;

import com.train.model.Affectation;
import com.train.model.RoleVoyage;
import com.train.model.Utilisateur;
import com.train.model.Voyage;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Interface pour la gestion des affectations d'employés aux voyages
 */
public interface AffectationService {
    
    /**
     * Créer une nouvelle affectation
     */
    Affectation creerAffectation(Affectation affectation);
    
    /**
     * Obtenir une affectation par son ID
     */
    Optional<Affectation> obtenirAffectationParId(Long id);
    
    /**
     * Obtenir toutes les affectations
     */
    List<Affectation> obtenirToutesLesAffectations();
    
    /**
     * Obtenir les affectations par voyage
     */
    List<Affectation> obtenirAffectationsParVoyage(Long voyageId);
    
    /**
     * Obtenir les affectations par employé
     */
    List<Affectation> obtenirAffectationsParEmploye(Long employeId);
    
    /**
     * Obtenir les affectations par rôle
     */
    List<Affectation> obtenirAffectationsParRole(RoleVoyage role);
    
    /**
     * Obtenir les affectations actives d'un employé
     */
    List<Affectation> obtenirAffectationsActivesParEmploye(Long employeId);
    
    /**
     * Obtenir les voyages assignés à un employé pour une période
     */
    List<Voyage> obtenirVoyagesAssignes(Long employeId, LocalDate dateDebut, LocalDate dateFin);
    
    /**
     * Mettre à jour une affectation
     */
    Affectation mettreAJourAffectation(Affectation affectation);
    
    /**
     * Supprimer une affectation
     */
    boolean supprimerAffectation(Long affectationId);
    
    /**
     * Désactiver une affectation
     */
    boolean desactiverAffectation(Long affectationId);
    
    /**
     * Affecter un employé à un voyage avec un rôle
     */
    Affectation affecterEmploye(Long voyageId, Long employeId, RoleVoyage role, String commentaire);
    
    /**
     * Désaffecter un employé d'un voyage
     */
    boolean desaffecterEmploye(Long voyageId, Long employeId);
    
    /**
     * Vérifier si un employé est disponible pour un voyage
     */
    boolean isEmployeDisponible(Long employeId, Long voyageId);
    
    /**
     * Obtenir les employés disponibles pour un voyage
     */
    List<Utilisateur> obtenirEmployesDisponibles(Long voyageId);
    
    /**
     * Obtenir les employés par type (conducteurs, contrôleurs, etc.)
     */
    List<Utilisateur> obtenirEmployesParType(RoleVoyage role);
    
    /**
     * Vérifier si un voyage a tous les rôles obligatoires assignés
     */
    boolean isVoyageCompletementAffecte(Long voyageId);
    
    /**
     * Obtenir les rôles manquants pour un voyage
     */
    List<RoleVoyage> obtenirRolesManquants(Long voyageId);
    
    /**
     * Obtenir les statistiques d'affectation
     */
    StatistiquesAffectation obtenirStatistiques();
    
    /**
     * Classe pour les statistiques d'affectation
     */
    class StatistiquesAffectation {
        private long totalAffectations;
        private long affectationsActives;
        private long voyagesCompletementAffecter;
        private long voyagesPartiellementAffecter;
        private long employesAffecter;
        private long conducteurs;
        private long controleurs;
        private long chefsDebord;
        
        // Constructeurs
        public StatistiquesAffectation() {}
        
        // Getters et Setters
        public long getTotalAffectations() { return totalAffectations; }
        public void setTotalAffectations(long totalAffectations) { this.totalAffectations = totalAffectations; }
        
        public long getAffectationsActives() { return affectationsActives; }
        public void setAffectationsActives(long affectationsActives) { this.affectationsActives = affectationsActives; }
        
        public long getVoyagesCompletementAffecter() { return voyagesCompletementAffecter; }
        public void setVoyagesCompletementAffecter(long voyagesCompletementAffecter) { this.voyagesCompletementAffecter = voyagesCompletementAffecter; }
        
        public long getVoyagesPartiellementAffecter() { return voyagesPartiellementAffecter; }
        public void setVoyagesPartiellementAffecter(long voyagesPartiellementAffecter) { this.voyagesPartiellementAffecter = voyagesPartiellementAffecter; }
        
        public long getEmployesAffecter() { return employesAffecter; }
        public void setEmployesAffecter(long employesAffecter) { this.employesAffecter = employesAffecter; }
        
        public long getConducteurs() { return conducteurs; }
        public void setConducteurs(long conducteurs) { this.conducteurs = conducteurs; }
        
        public long getControleurs() { return controleurs; }
        public void setControleurs(long controleurs) { this.controleurs = controleurs; }
        
        public long getChefsDebord() { return chefsDebord; }
        public void setChefsDebord(long chefsDebord) { this.chefsDebord = chefsDebord; }
    }
}
