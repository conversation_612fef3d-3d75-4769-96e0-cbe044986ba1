package com.train.dao;

import com.train.model.Gare;
import java.util.List;
import java.util.Optional;

/**
 * Interface DAO pour les opérations sur les gares
 */
public interface GareDAO extends BaseDAO<Gare, Long> {
    
    /**
     * Trouve une gare par son code
     * @param codeGare le code de la gare
     * @return Optional contenant la gare si trouvée
     */
    Optional<Gare> findByCode(String codeGare);
    
    /**
     * Trouve les gares par ville
     * @param ville le nom de la ville
     * @return liste des gares de la ville
     */
    List<Gare> findByVille(String ville);
    
    /**
     * Trouve les gares actives
     * @return liste des gares actives
     */
    List<Gare> findActiveGares();
    
    /**
     * Recherche les gares par nom (recherche partielle)
     * @param nom le nom ou partie du nom à rechercher
     * @return liste des gares correspondantes
     */
    List<Gare> searchByName(String nom);
    
    /**
     * Vérifie si un code de gare existe déjà
     * @param codeGare le code à vérifier
     * @return true si le code existe, false sinon
     */
    boolean existsByCode(String codeGare);
    
    /**
     * Trouve toutes les villes ayant des gares
     * @return liste des noms de villes
     */
    List<String> findAllCities();
}
