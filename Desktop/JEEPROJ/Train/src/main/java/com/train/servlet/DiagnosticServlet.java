package com.train.servlet;

import com.train.migration.DatabaseMigration;
import com.train.util.DatabaseConnection;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Servlet de diagnostic pour vérifier l'état de la base de données et des migrations
 */
@WebServlet("/diagnostic")
public class DiagnosticServlet extends HttpServlet {
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        out.println("<!DOCTYPE html>");
        out.println("<html lang='fr'>");
        out.println("<head>");
        out.println("<meta charset='UTF-8'>");
        out.println("<title>Diagnostic TrainSystem</title>");
        out.println("<style>");
        out.println("body { font-family: Arial, sans-serif; margin: 20px; }");
        out.println(".success { color: green; }");
        out.println(".error { color: red; }");
        out.println(".warning { color: orange; }");
        out.println("table { border-collapse: collapse; width: 100%; margin: 20px 0; }");
        out.println("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }");
        out.println("th { background-color: #f2f2f2; }");
        out.println(".btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; }");
        out.println("</style>");
        out.println("</head>");
        out.println("<body>");
        
        out.println("<h1>🔍 Diagnostic TrainSystem</h1>");
        
        // Test de connexion à la base de données
        testConnexionDB(out);
        
        // Vérification des tables
        verifierTables(out);
        
        // Vérification des migrations
        verifierMigrations(out);
        
        // Actions disponibles
        out.println("<h2>🛠️ Actions</h2>");
        out.println("<a href='?action=migrate' class='btn'>Exécuter les migrations</a>");
        out.println("<a href='?action=refresh' class='btn'>Actualiser</a>");
        out.println("<a href='" + request.getContextPath() + "/admin' class='btn'>Retour Admin</a>");
        
        // Traitement des actions
        String action = request.getParameter("action");
        if ("migrate".equals(action)) {
            executerMigrations(out);
        }
        
        out.println("</body>");
        out.println("</html>");
    }
    
    private void testConnexionDB(PrintWriter out) {
        out.println("<h2>🔌 Connexion à la base de données</h2>");
        
        try (Connection conn = DatabaseConnection.getConnection()) {
            if (conn != null && !conn.isClosed()) {
                out.println("<p class='success'>✅ Connexion à la base de données réussie</p>");
                out.println("<p>URL: " + conn.getMetaData().getURL() + "</p>");
                out.println("<p>Driver: " + conn.getMetaData().getDriverName() + "</p>");
            } else {
                out.println("<p class='error'>❌ Connexion fermée ou nulle</p>");
            }
        } catch (SQLException e) {
            out.println("<p class='error'>❌ Erreur de connexion: " + e.getMessage() + "</p>");
        }
    }
    
    private void verifierTables(PrintWriter out) {
        out.println("<h2>📋 Tables de la base de données</h2>");
        
        String[] tablesRequises = {
            "utilisateurs", "gares", "trajets", "voyages", "reservations",
            "affectations", "paiements", "demandes_annulation", "schema_migrations"
        };
        
        out.println("<table>");
        out.println("<tr><th>Table</th><th>Statut</th><th>Nombre d'enregistrements</th></tr>");
        
        for (String table : tablesRequises) {
            out.println("<tr>");
            out.println("<td>" + table + "</td>");
            
            if (DatabaseMigration.tableExiste(table)) {
                out.println("<td class='success'>✅ Existe</td>");
                
                // Compter les enregistrements
                try (Connection conn = DatabaseConnection.getConnection();
                     Statement stmt = conn.createStatement();
                     ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM " + table)) {
                    
                    if (rs.next()) {
                        out.println("<td>" + rs.getInt(1) + "</td>");
                    } else {
                        out.println("<td>0</td>");
                    }
                } catch (SQLException e) {
                    out.println("<td class='error'>Erreur: " + e.getMessage() + "</td>");
                }
            } else {
                out.println("<td class='error'>❌ Manquante</td>");
                out.println("<td>-</td>");
            }
            
            out.println("</tr>");
        }
        
        out.println("</table>");
    }
    
    private void verifierMigrations(PrintWriter out) {
        out.println("<h2>🔄 État des migrations</h2>");
        
        if (!DatabaseMigration.tableExiste("schema_migrations")) {
            out.println("<p class='warning'>⚠️ Table de migrations non trouvée</p>");
            return;
        }
        
        try (Connection conn = DatabaseConnection.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT migration_name, executed_at FROM schema_migrations ORDER BY executed_at")) {
            
            out.println("<table>");
            out.println("<tr><th>Migration</th><th>Exécutée le</th></tr>");
            
            boolean hasMigrations = false;
            while (rs.next()) {
                hasMigrations = true;
                out.println("<tr>");
                out.println("<td>" + rs.getString("migration_name") + "</td>");
                out.println("<td>" + rs.getTimestamp("executed_at") + "</td>");
                out.println("</tr>");
            }
            
            if (!hasMigrations) {
                out.println("<tr><td colspan='2'>Aucune migration exécutée</td></tr>");
            }
            
            out.println("</table>");
            
        } catch (SQLException e) {
            out.println("<p class='error'>❌ Erreur lors de la vérification des migrations: " + e.getMessage() + "</p>");
        }
    }
    
    private void executerMigrations(PrintWriter out) {
        out.println("<h2>🔄 Exécution des migrations</h2>");
        
        try {
            DatabaseMigration.executerMigrations();
            out.println("<p class='success'>✅ Migrations exécutées avec succès !</p>");
            out.println("<script>setTimeout(function(){ window.location.reload(); }, 2000);</script>");
        } catch (Exception e) {
            out.println("<p class='error'>❌ Erreur lors de l'exécution des migrations: " + e.getMessage() + "</p>");
        }
    }
}
