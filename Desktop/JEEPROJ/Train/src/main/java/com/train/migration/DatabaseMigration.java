package com.train.migration;

import com.train.util.DatabaseConnection;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Gestionnaire de migrations automatiques de la base de données
 */
public class DatabaseMigration {
    
    private static final String MIGRATION_TABLE = "schema_migrations";
    
    /**
     * Exécuter toutes les migrations nécessaires
     */
    public static void executerMigrations() {
        try {
            System.out.println("🔄 Démarrage des migrations de base de données...");
            
            // Créer la table de suivi des migrations si elle n'existe pas
            creerTableMigrations();
            
            // Obtenir les migrations déjà exécutées
            List<String> migrationsExecutees = obtenirMigrationsExecutees();
            
            // Exécuter les nouvelles migrations
            if (!migrationsExecutees.contains("001_create_employees_tables")) {
                executerMigration001();
                marquerMigrationExecutee("001_create_employees_tables");
            }
            
            if (!migrationsExecutees.contains("002_update_users_employees")) {
                executerMigration002();
                marquerMigrationExecutee("002_update_users_employees");
            }
            
            if (!migrationsExecutees.contains("003_insert_demo_data")) {
                executerMigration003();
                marquerMigrationExecutee("003_insert_demo_data");
            }
            
            System.out.println("✅ Migrations terminées avec succès !");
            
        } catch (SQLException e) {
            System.err.println("❌ Erreur lors des migrations : " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Créer la table de suivi des migrations
     */
    private static void creerTableMigrations() throws SQLException {
        String sql = "CREATE TABLE IF NOT EXISTS schema_migrations (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "migration_name VARCHAR(255) NOT NULL UNIQUE, " +
                    "executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                    ")";

        try (Connection conn = DatabaseConnection.getConnection();
             Statement stmt = conn.createStatement()) {
            stmt.executeUpdate(sql);
            System.out.println("📋 Table de migrations créée/vérifiée");
        }
    }
    
    /**
     * Obtenir la liste des migrations déjà exécutées
     */
    private static List<String> obtenirMigrationsExecutees() throws SQLException {
        List<String> migrations = new ArrayList<>();
        String sql = "SELECT migration_name FROM schema_migrations";
        
        try (Connection conn = DatabaseConnection.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            while (rs.next()) {
                migrations.add(rs.getString("migration_name"));
            }
        }
        
        return migrations;
    }
    
    /**
     * Marquer une migration comme exécutée
     */
    private static void marquerMigrationExecutee(String migrationName) throws SQLException {
        String sql = "INSERT INTO schema_migrations (migration_name) VALUES (?)";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, migrationName);
            stmt.executeUpdate();
            System.out.println("✅ Migration marquée comme exécutée : " + migrationName);
        }
    }
    
    /**
     * Migration 001 : Création des tables employés, affectations, paiements, annulations
     */
    private static void executerMigration001() throws SQLException {
        System.out.println("🔄 Exécution de la migration 001 : Création des tables...");
        
        String[] sqls = {
            // Table des affectations
            "CREATE TABLE IF NOT EXISTS affectations (" +
            "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
            "voyage_id BIGINT NOT NULL, " +
            "employe_id BIGINT NOT NULL, " +
            "role_voyage ENUM('CONDUCTEUR', 'CONTROLEUR', 'CHEF_DE_BORD', 'AGENT_COMMERCIAL', 'TECHNICIEN') NOT NULL, " +
            "date_affectation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, " +
            "date_modification DATETIME NULL, " +
            "actif BOOLEAN NOT NULL DEFAULT TRUE, " +
            "commentaire TEXT NULL, " +
            "FOREIGN KEY (voyage_id) REFERENCES voyages(id) ON DELETE CASCADE, " +
            "FOREIGN KEY (employe_id) REFERENCES utilisateurs(id) ON DELETE CASCADE, " +
            "INDEX idx_voyage_id (voyage_id), " +
            "INDEX idx_employe_id (employe_id), " +
            "INDEX idx_role_voyage (role_voyage), " +
            "INDEX idx_actif (actif), " +
            "UNIQUE KEY unique_affectation (voyage_id, employe_id, role_voyage)" +
            ")",
            
            // Table des paiements
            "CREATE TABLE IF NOT EXISTS paiements (" +
            "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
            "reservation_id BIGINT NOT NULL, " +
            "montant DECIMAL(10,2) NOT NULL, " +
            "statut_paiement ENUM('EN_ATTENTE', 'CONFIRME', 'ECHEC', 'ANNULE', 'REMBOURSE') NOT NULL DEFAULT 'EN_ATTENTE', " +
            "type_paiement ENUM('PAIEMENT', 'REMBOURSEMENT', 'FRAIS') NOT NULL DEFAULT 'PAIEMENT', " +
            "methode_paiement VARCHAR(50) NOT NULL, " +
            "transaction_id VARCHAR(100) NULL, " +
            "date_paiement DATETIME NULL, " +
            "date_creation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, " +
            "date_modification DATETIME NULL, " +
            "commentaire TEXT NULL, " +
            "FOREIGN KEY (reservation_id) REFERENCES reservations(id) ON DELETE CASCADE, " +
            "INDEX idx_reservation_id (reservation_id), " +
            "INDEX idx_statut_paiement (statut_paiement), " +
            "INDEX idx_type_paiement (type_paiement), " +
            "INDEX idx_date_paiement (date_paiement), " +
            "INDEX idx_transaction_id (transaction_id)" +
            ")",
            
            // Table des demandes d'annulation
            "CREATE TABLE IF NOT EXISTS demandes_annulation (" +
            "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
            "reservation_id BIGINT NOT NULL, " +
            "utilisateur_id BIGINT NOT NULL, " +
            "motif TEXT NOT NULL, " +
            "statut_annulation ENUM('EN_ATTENTE', 'APPROUVEE', 'REJETEE', 'TRAITEE') NOT NULL DEFAULT 'EN_ATTENTE', " +
            "montant_paye DECIMAL(10,2) NOT NULL, " +
            "frais_annulation DECIMAL(10,2) NOT NULL DEFAULT 0.00, " +
            "montant_remboursement DECIMAL(10,2) NOT NULL DEFAULT 0.00, " +
            "date_demande DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, " +
            "date_traitement DATETIME NULL, " +
            "commentaire_admin TEXT NULL, " +
            "admin_id BIGINT NULL, " +
            "FOREIGN KEY (reservation_id) REFERENCES reservations(id) ON DELETE CASCADE, " +
            "FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id) ON DELETE CASCADE, " +
            "FOREIGN KEY (admin_id) REFERENCES utilisateurs(id) ON DELETE SET NULL, " +
            "INDEX idx_reservation_id (reservation_id), " +
            "INDEX idx_utilisateur_id (utilisateur_id), " +
            "INDEX idx_statut_annulation (statut_annulation), " +
            "INDEX idx_date_demande (date_demande), " +
            "INDEX idx_admin_id (admin_id)" +
            ")"
        };
        
        try (Connection conn = DatabaseConnection.getConnection()) {
            for (String sql : sqls) {
                try (Statement stmt = conn.createStatement()) {
                    stmt.executeUpdate(sql);
                }
            }
        }
        
        System.out.println("✅ Tables créées avec succès");
    }
    
    /**
     * Migration 002 : Mise à jour des utilisateurs pour avoir des employés
     */
    private static void executerMigration002() throws SQLException {
        System.out.println("🔄 Exécution de la migration 002 : Mise à jour des utilisateurs...");
        
        String sql = "UPDATE utilisateurs SET type_utilisateur = 'EMPLOYE' WHERE id IN (2, 3, 4, 5)";
        
        try (Connection conn = DatabaseConnection.getConnection();
             Statement stmt = conn.createStatement()) {
            int rowsUpdated = stmt.executeUpdate(sql);
            System.out.println("✅ " + rowsUpdated + " utilisateurs mis à jour comme employés");
        }
    }
    
    /**
     * Migration 003 : Insertion des données de démonstration
     */
    private static void executerMigration003() throws SQLException {
        System.out.println("🔄 Exécution de la migration 003 : Insertion des données de démonstration...");
        
        String[] sqls = {
            // Données d'affectations
            "INSERT IGNORE INTO affectations (voyage_id, employe_id, role_voyage, commentaire) VALUES " +
            "(1, 2, 'CONDUCTEUR', 'Conducteur principal pour ce trajet'), " +
            "(1, 3, 'CONTROLEUR', 'Contrôle des billets'), " +
            "(2, 2, 'CONDUCTEUR', 'Conducteur expérimenté'), " +
            "(2, 4, 'CHEF_DE_BORD', 'Responsable de l\\'équipe'), " +
            "(3, 3, 'CONTROLEUR', 'Service voyageurs'), " +
            "(3, 5, 'TECHNICIEN', 'Maintenance préventive')",
            
            // Données de paiements
            "INSERT IGNORE INTO paiements (reservation_id, montant, statut_paiement, type_paiement, methode_paiement, transaction_id, date_paiement) VALUES " +
            "(1, 89.50, 'CONFIRME', 'PAIEMENT', 'CARTE', 'TXN001', NOW() - INTERVAL 2 DAY), " +
            "(2, 65.00, 'CONFIRME', 'PAIEMENT', 'PAYPAL', 'TXN002', NOW() - INTERVAL 1 DAY), " +
            "(3, 120.00, 'EN_ATTENTE', 'PAIEMENT', 'CARTE', NULL, NULL), " +
            "(4, -55.00, 'CONFIRME', 'REMBOURSEMENT', 'CARTE', 'REF001', NOW() - INTERVAL 3 DAY), " +
            "(5, 110.00, 'ECHEC', 'PAIEMENT', 'CARTE', NULL, NULL)",
            
            // Données d'annulations
            "INSERT IGNORE INTO demandes_annulation (reservation_id, utilisateur_id, motif, statut_annulation, montant_paye, frais_annulation, montant_remboursement) VALUES " +
            "(1, 1, 'Changement de programme personnel', 'EN_ATTENTE', 89.50, 15.00, 74.50), " +
            "(2, 2, 'Problème de santé', 'APPROUVEE', 65.00, 10.00, 55.00), " +
            "(3, 3, 'Urgence familiale', 'TRAITEE', 120.00, 20.00, 100.00)"
        };
        
        try (Connection conn = DatabaseConnection.getConnection()) {
            for (String sql : sqls) {
                try (Statement stmt = conn.createStatement()) {
                    stmt.executeUpdate(sql);
                }
            }
        }
        
        System.out.println("✅ Données de démonstration insérées");
    }
    
    /**
     * Vérifier si une table existe
     */
    public static boolean tableExiste(String tableName) {
        try (Connection conn = DatabaseConnection.getConnection()) {
            DatabaseMetaData metaData = conn.getMetaData();
            try (ResultSet rs = metaData.getTables(null, null, tableName, new String[]{"TABLE"})) {
                return rs.next();
            }
        } catch (SQLException e) {
            System.err.println("Erreur lors de la vérification de la table " + tableName + " : " + e.getMessage());
            return false;
        }
    }
}
