package com.train.service;

import com.train.model.Paiement;
import com.train.model.StatutPaiement;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Interface pour la gestion des paiements
 */
public interface PaiementService {
    
    /**
     * Créer un nouveau paiement
     */
    Paiement creerPaiement(Paiement paiement);
    
    /**
     * Obtenir un paiement par son ID
     */
    Optional<Paiement> obtenirPaiementParId(Long id);
    
    /**
     * Obtenir tous les paiements
     */
    List<Paiement> obtenirTousLesPaiements();
    
    /**
     * Obtenir les paiements par réservation
     */
    List<Paiement> obtenirPaiementsParReservation(Long reservationId);
    
    /**
     * Obtenir les paiements par statut
     */
    List<Paiement> obtenirPaiementsParStatut(StatutPaiement statut);
    
    /**
     * Obtenir les paiements entre deux dates
     */
    List<Paiement> obtenirPaiementsParPeriode(LocalDateTime dateDebut, LocalDateTime dateFin);
    
    /**
     * Mettre à jour un paiement
     */
    Paiement mettreAJourPaiement(Paiement paiement);
    
    /**
     * Confirmer un paiement
     */
    boolean confirmerPaiement(Long paiementId, String transactionId);
    
    /**
     * Marquer un paiement comme échoué
     */
    boolean marquerPaiementEchec(Long paiementId, String motif);
    
    /**
     * Annuler un paiement
     */
    boolean annulerPaiement(Long paiementId, String motif);
    
    /**
     * Créer un remboursement
     */
    Paiement creerRemboursement(Long reservationId, BigDecimal montant, String motif);
    
    /**
     * Calculer le total des revenus
     */
    BigDecimal calculerRevenuTotal();
    
    /**
     * Calculer les revenus par période
     */
    BigDecimal calculerRevenuParPeriode(LocalDateTime dateDebut, LocalDateTime dateFin);
    
    /**
     * Compter les paiements par statut
     */
    long compterPaiementsParStatut(StatutPaiement statut);
    
    /**
     * Obtenir les statistiques de paiement
     */
    StatistiquesPaiement obtenirStatistiques();
    
    /**
     * Classe pour les statistiques de paiement
     */
    class StatistiquesPaiement {
        private BigDecimal revenuTotal;
        private BigDecimal revenuMois;
        private BigDecimal paiementsEnAttente;
        private BigDecimal remboursementsATraiter;
        private BigDecimal remboursementsEffectues;
        private long nombrePaiements;
        private long nombreRemboursements;
        
        // Constructeurs
        public StatistiquesPaiement() {}
        
        // Getters et Setters
        public BigDecimal getRevenuTotal() { return revenuTotal; }
        public void setRevenuTotal(BigDecimal revenuTotal) { this.revenuTotal = revenuTotal; }
        
        public BigDecimal getRevenuMois() { return revenuMois; }
        public void setRevenuMois(BigDecimal revenuMois) { this.revenuMois = revenuMois; }
        
        public BigDecimal getPaiementsEnAttente() { return paiementsEnAttente; }
        public void setPaiementsEnAttente(BigDecimal paiementsEnAttente) { this.paiementsEnAttente = paiementsEnAttente; }
        
        public BigDecimal getRemboursementsATraiter() { return remboursementsATraiter; }
        public void setRemboursementsATraiter(BigDecimal remboursementsATraiter) { this.remboursementsATraiter = remboursementsATraiter; }
        
        public BigDecimal getRemboursementsEffectues() { return remboursementsEffectues; }
        public void setRemboursementsEffectues(BigDecimal remboursementsEffectues) { this.remboursementsEffectues = remboursementsEffectues; }
        
        public long getNombrePaiements() { return nombrePaiements; }
        public void setNombrePaiements(long nombrePaiements) { this.nombrePaiements = nombrePaiements; }
        
        public long getNombreRemboursements() { return nombreRemboursements; }
        public void setNombreRemboursements(long nombreRemboursements) { this.nombreRemboursements = nombreRemboursements; }
    }
}
