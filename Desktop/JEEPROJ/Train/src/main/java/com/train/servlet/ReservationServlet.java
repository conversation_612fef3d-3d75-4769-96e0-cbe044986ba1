package com.train.servlet;

import com.train.service.ReservationService;
import com.train.service.VoyageService;
import com.train.service.impl.ReservationServiceImpl;
import com.train.service.impl.VoyageServiceImpl;
import java.math.BigDecimal;
import com.train.model.Reservation;
import com.train.model.Voyage;
import com.train.model.Utilisateur;
import com.train.model.StatutReservation;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 * Servlet pour la gestion des réservations
 */
@WebServlet(name = "ReservationServlet", urlPatterns = {"/reservation/*"})
public class ReservationServlet extends HttpServlet {
    
    private ReservationService reservationService;
    private VoyageService voyageService;
    
    @Override
    public void init() throws ServletException {
        reservationService = new ReservationServiceImpl();
        voyageService = new VoyageServiceImpl();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        
        if (pathInfo == null || pathInfo.equals("/")) {
            afficherMesReservations(request, response);
        } else if (pathInfo.startsWith("/create/")) {
            String voyageIdStr = pathInfo.substring("/create/".length());
            afficherPageReservation(request, response, voyageIdStr);
        } else if (pathInfo.startsWith("/details/")) {
            String reservationIdStr = pathInfo.substring("/details/".length());
            afficherDetailsReservation(request, response, reservationIdStr);
        } else if (pathInfo.startsWith("/edit/")) {
            String reservationIdStr = pathInfo.substring("/edit/".length());
            afficherPageModification(request, response, reservationIdStr);
        } else if (pathInfo.startsWith("/cancel/")) {
            String reservationIdStr = pathInfo.substring("/cancel/".length());
            afficherPageAnnulation(request, response, reservationIdStr);
        } else if (pathInfo.startsWith("/pdf/")) {
            String reservationIdStr = pathInfo.substring("/pdf/".length());
            genererPDFReservation(request, response, reservationIdStr);
        } else {
            response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String pathInfo = request.getPathInfo();
        
        if (pathInfo != null && pathInfo.startsWith("/create/")) {
            String voyageIdStr = pathInfo.substring("/create/".length());
            traiterNouvelleReservation(request, response, voyageIdStr);
        } else if (pathInfo != null && pathInfo.startsWith("/edit/")) {
            String reservationIdStr = pathInfo.substring("/edit/".length());
            traiterModificationReservation(request, response, reservationIdStr);
        } else if (pathInfo != null && pathInfo.startsWith("/cancel/")) {
            String reservationIdStr = pathInfo.substring("/cancel/".length());
            traiterDemandeAnnulation(request, response, reservationIdStr);
        } else {
            response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    private void afficherMesReservations(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // Vérifier l'authentification
        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login?redirectUrl=" + 
                java.net.URLEncoder.encode(request.getRequestURI(), "UTF-8"));
            return;
        }
        
        try {
            Long userId = (Long) session.getAttribute("userId");
            List<Reservation> reservations = reservationService.obtenirReservationsUtilisateur(userId);
            
            request.setAttribute("reservations", reservations);
            request.getRequestDispatcher("/WEB-INF/views/reservation/list.jsp").forward(request, response);
            
        } catch (Exception e) {
            request.setAttribute("errorMessage", "Erreur lors de la récupération des réservations : " + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/reservation/list.jsp").forward(request, response);
        }
    }
    
    private void afficherPageReservation(HttpServletRequest request, HttpServletResponse response, String voyageIdStr) 
            throws ServletException, IOException {
        
        // Vérifier l'authentification
        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login?redirectUrl=" + 
                java.net.URLEncoder.encode(request.getRequestURI(), "UTF-8"));
            return;
        }
        
        try {
            Long voyageId = Long.parseLong(voyageIdStr);
            Optional<Voyage> voyageOpt = voyageService.trouverParId(voyageId);
            
            if (!voyageOpt.isPresent()) {
                request.setAttribute("errorMessage", "Voyage non trouvé");
                response.sendRedirect(request.getContextPath() + "/search");
                return;
            }
            
            Voyage voyage = voyageOpt.get();
            
            // Vérifier si l'utilisateur a déjà une réservation pour ce voyage
            Long userId = (Long) session.getAttribute("userId");
            if (reservationService.aDejaReservation(userId, voyageId)) {
                request.setAttribute("errorMessage", "Vous avez déjà une réservation pour ce voyage");
                response.sendRedirect(request.getContextPath() + "/reservation");
                return;
            }
            
            request.setAttribute("voyage", voyage);
            request.getRequestDispatcher("/WEB-INF/views/reservation/create.jsp").forward(request, response);
            
        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID de voyage invalide");
        } catch (Exception e) {
            request.setAttribute("errorMessage", "Erreur : " + e.getMessage());
            response.sendRedirect(request.getContextPath() + "/search");
        }
    }
    
    private void afficherDetailsReservation(HttpServletRequest request, HttpServletResponse response, String reservationIdStr) 
            throws ServletException, IOException {
        
        // Vérifier l'authentification
        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        try {
            Long reservationId = Long.parseLong(reservationIdStr);
            Optional<Reservation> reservationOpt = reservationService.trouverParId(reservationId);
            
            if (!reservationOpt.isPresent()) {
                request.setAttribute("errorMessage", "Réservation non trouvée");
                response.sendRedirect(request.getContextPath() + "/reservation");
                return;
            }
            
            Reservation reservation = reservationOpt.get();
            Long userId = (Long) session.getAttribute("userId");
            
            // Vérifier que la réservation appartient à l'utilisateur connecté
            if (!reservation.getUtilisateur().getId().equals(userId)) {
                response.sendError(HttpServletResponse.SC_FORBIDDEN, "Accès non autorisé");
                return;
            }
            
            request.setAttribute("reservation", reservation);
            request.getRequestDispatcher("/WEB-INF/views/reservation/details.jsp").forward(request, response);
            
        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID de réservation invalide");
        } catch (Exception e) {
            request.setAttribute("errorMessage", "Erreur : " + e.getMessage());
            response.sendRedirect(request.getContextPath() + "/reservation");
        }
    }
    
    private void afficherPageAnnulation(HttpServletRequest request, HttpServletResponse response, String reservationIdStr) 
            throws ServletException, IOException {
        
        // Vérifier l'authentification
        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        try {
            Long reservationId = Long.parseLong(reservationIdStr);
            Optional<Reservation> reservationOpt = reservationService.trouverParId(reservationId);
            
            if (!reservationOpt.isPresent()) {
                request.setAttribute("errorMessage", "Réservation non trouvée");
                response.sendRedirect(request.getContextPath() + "/reservation");
                return;
            }
            
            Reservation reservation = reservationOpt.get();
            Long userId = (Long) session.getAttribute("userId");
            
            // Vérifier que la réservation appartient à l'utilisateur connecté
            if (!reservation.getUtilisateur().getId().equals(userId)) {
                response.sendError(HttpServletResponse.SC_FORBIDDEN, "Accès non autorisé");
                return;
            }
            
            // Vérifier que la réservation peut être annulée
            if (!reservationService.peutEtreAnnulee(reservationId)) {
                request.setAttribute("errorMessage", "Cette réservation ne peut pas être annulée");
                response.sendRedirect(request.getContextPath() + "/reservation/details/" + reservationId);
                return;
            }
            
            request.setAttribute("reservation", reservation);
            request.getRequestDispatcher("/WEB-INF/views/reservation/cancel.jsp").forward(request, response);
            
        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID de réservation invalide");
        } catch (Exception e) {
            request.setAttribute("errorMessage", "Erreur : " + e.getMessage());
            response.sendRedirect(request.getContextPath() + "/reservation");
        }
    }
    
    private void traiterNouvelleReservation(HttpServletRequest request, HttpServletResponse response, String voyageIdStr) 
            throws ServletException, IOException {
        
        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        try {
            Long voyageId = Long.parseLong(voyageIdStr);
            Long userId = (Long) session.getAttribute("userId");
            
            String nombrePlacesStr = request.getParameter("nombrePlaces");
            int nombrePlaces = Integer.parseInt(nombrePlacesStr);
            
            // Créer la réservation
            Reservation reservation = reservationService.creerReservation(userId, voyageId, nombrePlaces);
            
            // Confirmer automatiquement la réservation (dans un vrai système, il y aurait un processus de paiement)
            reservationService.confirmerReservation(reservation.getId());
            
            session.setAttribute("successMessage", 
                "Réservation créée avec succès ! Numéro de réservation : " + reservation.getNumeroReservation());
            
            response.sendRedirect(request.getContextPath() + "/reservation/details/" + reservation.getId());
            
        } catch (NumberFormatException e) {
            request.setAttribute("errorMessage", "Données invalides");
            afficherPageReservation(request, response, voyageIdStr);
        } catch (Exception e) {
            request.setAttribute("errorMessage", "Erreur lors de la réservation : " + e.getMessage());
            afficherPageReservation(request, response, voyageIdStr);
        }
    }
    
    private void traiterAnnulationReservation(HttpServletRequest request, HttpServletResponse response, String reservationIdStr) 
            throws ServletException, IOException {
        
        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        try {
            Long reservationId = Long.parseLong(reservationIdStr);
            String motifAnnulation = request.getParameter("motifAnnulation");
            
            if (motifAnnulation == null || motifAnnulation.trim().isEmpty()) {
                motifAnnulation = "Annulation demandée par le client";
            }
            
            boolean success = reservationService.annulerReservation(reservationId, motifAnnulation);
            
            if (success) {
                session.setAttribute("successMessage", "Réservation annulée avec succès");
            } else {
                session.setAttribute("errorMessage", "Erreur lors de l'annulation de la réservation");
            }
            
            response.sendRedirect(request.getContextPath() + "/reservation");
            
        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID de réservation invalide");
        } catch (Exception e) {
            request.setAttribute("errorMessage", "Erreur : " + e.getMessage());
            response.sendRedirect(request.getContextPath() + "/reservation");
        }
    }

    // ========================================
    // NOUVELLES MÉTHODES POUR LES FONCTIONNALITÉS COMPLÈTES
    // ========================================

    /**
     * Afficher la page de modification d'une réservation
     */
    private void afficherPageModification(HttpServletRequest request, HttpServletResponse response, String reservationIdStr)
            throws ServletException, IOException {

        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }

        try {
            Long reservationId = Long.parseLong(reservationIdStr);
            Long userId = (Long) session.getAttribute("userId");

            Optional<Reservation> reservationOpt = reservationService.trouverParId(reservationId);
            if (!reservationOpt.isPresent()) {
                request.setAttribute("errorMessage", "Réservation non trouvée");
                response.sendRedirect(request.getContextPath() + "/reservation");
                return;
            }

            Reservation reservation = reservationOpt.get();

            // Vérifier que la réservation appartient à l'utilisateur
            if (!reservation.getUtilisateur().getId().equals(userId)) {
                response.sendError(HttpServletResponse.SC_FORBIDDEN);
                return;
            }

            // Vérifier que la réservation peut être modifiée
            if (reservation.getStatut() != StatutReservation.CONFIRMEE &&
                reservation.getStatut() != StatutReservation.EN_ATTENTE) {
                request.setAttribute("errorMessage", "Cette réservation ne peut plus être modifiée");
                response.sendRedirect(request.getContextPath() + "/reservation");
                return;
            }

            // Calculer les places disponibles (incluant les places actuelles de la réservation)
            int placesDisponibles = reservation.getVoyage().getPlacesDisponibles() + reservation.getNombrePlaces();

            request.setAttribute("reservation", reservation);
            request.setAttribute("placesDisponibles", placesDisponibles);
            request.getRequestDispatcher("/WEB-INF/views/reservation/edit.jsp").forward(request, response);

        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID de réservation invalide");
        } catch (Exception e) {
            request.setAttribute("errorMessage", "Erreur : " + e.getMessage());
            response.sendRedirect(request.getContextPath() + "/reservation");
        }
    }

    /**
     * Traiter la modification d'une réservation
     */
    private void traiterModificationReservation(HttpServletRequest request, HttpServletResponse response, String reservationIdStr)
            throws ServletException, IOException {

        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }

        try {
            Long reservationId = Long.parseLong(reservationIdStr);
            Long userId = (Long) session.getAttribute("userId");

            String nombrePlacesStr = request.getParameter("nombrePlaces");
            String motifModification = request.getParameter("motifModification");

            int nouveauNombrePlaces = Integer.parseInt(nombrePlacesStr);

            Optional<Reservation> reservationOpt = reservationService.trouverParId(reservationId);
            if (!reservationOpt.isPresent()) {
                session.setAttribute("errorMessage", "Réservation non trouvée");
                response.sendRedirect(request.getContextPath() + "/reservation");
                return;
            }

            Reservation reservation = reservationOpt.get();

            // Vérifier que la réservation appartient à l'utilisateur
            if (!reservation.getUtilisateur().getId().equals(userId)) {
                response.sendError(HttpServletResponse.SC_FORBIDDEN);
                return;
            }

            // Calculer la différence de places
            int ancienNombrePlaces = reservation.getNombrePlaces();
            int differencePlaces = nouveauNombrePlaces - ancienNombrePlaces;

            // Vérifier la disponibilité si on augmente le nombre de places
            if (differencePlaces > 0 && differencePlaces > reservation.getVoyage().getPlacesDisponibles()) {
                request.setAttribute("errorMessage", "Pas assez de places disponibles");
                afficherPageModification(request, response, reservationIdStr);
                return;
            }

            // Mettre à jour la réservation
            reservation.setNombrePlaces(nouveauNombrePlaces);
            reservation.setPrixTotal(reservation.getVoyage().getTrajet().getPrix().multiply(BigDecimal.valueOf(nouveauNombrePlaces)));

            if (motifModification != null && !motifModification.trim().isEmpty()) {
                reservation.setMotifModification(motifModification);
            }

            // Mettre à jour les places du voyage
            if (differencePlaces > 0) {
                // Réserver des places supplémentaires
                voyageService.reserverPlaces(reservation.getVoyage().getId(), differencePlaces);
            } else if (differencePlaces < 0) {
                // Libérer des places
                voyageService.libererPlaces(reservation.getVoyage().getId(), Math.abs(differencePlaces));
            }

            // Sauvegarder la réservation modifiée
            // Note: Dans un vrai système, il faudrait implémenter une méthode de mise à jour
            // Pour le moment, on simule la mise à jour

            session.setAttribute("successMessage",
                "Réservation modifiée avec succès ! " +
                (differencePlaces > 0 ? "Places ajoutées : " + differencePlaces :
                 differencePlaces < 0 ? "Places supprimées : " + Math.abs(differencePlaces) : "Aucun changement de places"));

            response.sendRedirect(request.getContextPath() + "/reservation/details/" + reservationId);

        } catch (NumberFormatException e) {
            request.setAttribute("errorMessage", "Données invalides");
            afficherPageModification(request, response, reservationIdStr);
        } catch (Exception e) {
            request.setAttribute("errorMessage", "Erreur lors de la modification : " + e.getMessage());
            afficherPageModification(request, response, reservationIdStr);
        }
    }

    /**
     * Traiter une demande d'annulation (avec validation admin)
     */
    private void traiterDemandeAnnulation(HttpServletRequest request, HttpServletResponse response, String reservationIdStr)
            throws ServletException, IOException {

        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }

        try {
            Long reservationId = Long.parseLong(reservationIdStr);
            Long userId = (Long) session.getAttribute("userId");

            String motifAnnulation = request.getParameter("motifAnnulation");
            String explicationDetaillee = request.getParameter("explicationDetaillee");
            String methodePaiement = request.getParameter("methodePaiement");

            Optional<Reservation> reservationOpt = reservationService.trouverParId(reservationId);
            if (!reservationOpt.isPresent()) {
                session.setAttribute("errorMessage", "Réservation non trouvée");
                response.sendRedirect(request.getContextPath() + "/reservation");
                return;
            }

            Reservation reservation = reservationOpt.get();

            // Vérifier que la réservation appartient à l'utilisateur
            if (!reservation.getUtilisateur().getId().equals(userId)) {
                response.sendError(HttpServletResponse.SC_FORBIDDEN);
                return;
            }

            // Dans un vrai système, on créerait une demande d'annulation en attente de validation admin
            // Pour le moment, on simule en changeant le statut à EN_ATTENTE_ANNULATION

            String motifComplet = motifAnnulation + " - " + explicationDetaillee +
                                 " (Remboursement souhaité: " + methodePaiement + ")";

            // Simuler l'envoi de la demande à l'admin
            // Dans un vrai système, on créerait un objet DemandeAnnulation

            session.setAttribute("successMessage",
                "Votre demande d'annulation a été envoyée avec succès ! " +
                "Un administrateur l'examinera sous 24-48h. Vous recevrez un email de confirmation.");

            response.sendRedirect(request.getContextPath() + "/reservation");

        } catch (NumberFormatException e) {
            request.setAttribute("errorMessage", "ID de réservation invalide");
            afficherPageAnnulation(request, response, reservationIdStr);
        } catch (Exception e) {
            request.setAttribute("errorMessage", "Erreur lors de l'envoi de la demande : " + e.getMessage());
            afficherPageAnnulation(request, response, reservationIdStr);
        }
    }

    /**
     * Générer et télécharger le PDF d'une réservation
     */
    private void genererPDFReservation(HttpServletRequest request, HttpServletResponse response, String reservationIdStr)
            throws ServletException, IOException {

        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }

        try {
            Long reservationId = Long.parseLong(reservationIdStr);
            Long userId = (Long) session.getAttribute("userId");

            Optional<Reservation> reservationOpt = reservationService.trouverParId(reservationId);
            if (!reservationOpt.isPresent()) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "Réservation non trouvée");
                return;
            }

            Reservation reservation = reservationOpt.get();

            // Vérifier que la réservation appartient à l'utilisateur
            if (!reservation.getUtilisateur().getId().equals(userId)) {
                response.sendError(HttpServletResponse.SC_FORBIDDEN);
                return;
            }

            // Vérifier que la réservation est confirmée
            if (reservation.getStatut() != StatutReservation.CONFIRMEE) {
                response.sendError(HttpServletResponse.SC_BAD_REQUEST, "Seules les réservations confirmées peuvent être téléchargées en PDF");
                return;
            }

            // Générer le PDF (simulation)
            // Dans un vrai système, on utiliserait une bibliothèque comme iText ou Apache PDFBox

            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition",
                "attachment; filename=\"billet_" + reservation.getNumeroReservation() + ".pdf\"");

            // Simuler la génération du PDF
            String pdfContent = genererContenuPDF(reservation);
            response.getWriter().write("PDF simulé pour la réservation " + reservation.getNumeroReservation() + "\n\n" + pdfContent);

        } catch (NumberFormatException e) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "ID de réservation invalide");
        } catch (Exception e) {
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Erreur lors de la génération du PDF");
        }
    }

    /**
     * Générer le contenu du PDF (simulation)
     */
    private String genererContenuPDF(Reservation reservation) {
        StringBuilder content = new StringBuilder();
        content.append("=== BILLET ÉLECTRONIQUE TRAINSYSTEM ===\n\n");
        content.append("Numéro de réservation: ").append(reservation.getNumeroReservation()).append("\n");
        content.append("Passager: ").append(reservation.getUtilisateur().getNomComplet()).append("\n");
        content.append("Email: ").append(reservation.getUtilisateur().getEmail()).append("\n\n");

        content.append("=== DÉTAILS DU VOYAGE ===\n");
        content.append("Départ: ").append(reservation.getVoyage().getTrajet().getGareDepart().getVille()).append("\n");
        content.append("Arrivée: ").append(reservation.getVoyage().getTrajet().getGareArrivee().getVille()).append("\n");
        content.append("Date: ").append(reservation.getVoyage().getDateVoyage()).append("\n");
        content.append("Heure départ: ").append(reservation.getVoyage().getTrajet().getHeureDepart()).append("\n");
        content.append("Heure arrivée: ").append(reservation.getVoyage().getTrajet().getHeureArrivee()).append("\n");
        content.append("Type de train: ").append(reservation.getVoyage().getTrajet().getTypeTrain()).append("\n\n");

        content.append("=== DÉTAILS DE LA RÉSERVATION ===\n");
        content.append("Nombre de places: ").append(reservation.getNombrePlaces()).append("\n");
        content.append("Prix total: ").append(reservation.getPrixTotal()).append(" €\n");
        content.append("Statut: ").append(reservation.getStatut()).append("\n");
        content.append("Date de réservation: ").append(reservation.getDateReservation()).append("\n\n");

        content.append("=== INFORMATIONS IMPORTANTES ===\n");
        content.append("- Présentez-vous en gare 30 minutes avant le départ\n");
        content.append("- Munissez-vous d'une pièce d'identité valide\n");
        content.append("- Ce billet est nominatif et non cessible\n");
        content.append("- En cas de problème: <EMAIL>\n\n");

        content.append("Merci de voyager avec TrainSystem !\n");

        return content.toString();
    }
}
