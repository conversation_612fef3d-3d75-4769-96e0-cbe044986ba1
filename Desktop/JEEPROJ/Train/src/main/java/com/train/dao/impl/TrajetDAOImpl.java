package com.train.dao.impl;

import com.train.dao.TrajetDAO;
import com.train.dao.GareDAO;
import com.train.dao.impl.GareDAOImpl;
import com.train.model.Trajet;
import com.train.model.Gare;
import com.train.util.DatabaseConnection;

import java.math.BigDecimal;
import java.sql.*;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Implémentation DAO pour les trajets
 */
public class TrajetDAOImpl implements TrajetDAO {
    
    private final GareDAO gareDAO = new GareDAOImpl();
    
    private static final String INSERT_TRAJET =
        "INSERT INTO trajets (gare_depart_id, gare_arrivee_id, heure_depart, heure_arrivee, prix, nombre_places, type_train, actif) " +
        "VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

    private static final String UPDATE_TRAJET =
        "UPDATE trajets SET gare_depart_id=?, gare_arrivee_id=?, heure_depart=?, heure_arrivee=?, prix=?, nombre_places=?, type_train=?, actif=? WHERE id=?";
    
    private static final String DELETE_TRAJET = "DELETE FROM trajets WHERE id=?";
    
    private static final String FIND_BY_ID = 
        "SELECT t.*, gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM trajets t " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "WHERE t.id=?";
    
    private static final String FIND_ALL = 
        "SELECT t.*, gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM trajets t " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "ORDER BY gd.ville, ga.ville, t.heure_depart";
    
    private static final String FIND_BY_GARE_IDS = 
        "SELECT t.*, gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM trajets t " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "WHERE t.gare_depart_id=? AND t.gare_arrivee_id=? AND t.actif=true " +
        "ORDER BY t.heure_depart";
    
    private static final String FIND_BY_GARE_DEPART = 
        "SELECT t.*, gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM trajets t " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "WHERE t.gare_depart_id=? AND t.actif=true " +
        "ORDER BY t.heure_depart";
    
    private static final String FIND_BY_GARE_ARRIVEE = 
        "SELECT t.*, gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM trajets t " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "WHERE t.gare_arrivee_id=? AND t.actif=true " +
        "ORDER BY t.heure_depart";
    
    private static final String FIND_ACTIVE = 
        "SELECT t.*, gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM trajets t " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "WHERE t.actif=true " +
        "ORDER BY gd.ville, ga.ville, t.heure_depart";
    
    private static final String FIND_BY_GARE = 
        "SELECT t.*, gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM trajets t " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "WHERE (t.gare_depart_id=? OR t.gare_arrivee_id=?) AND t.actif=true " +
        "ORDER BY t.heure_depart";
    
    private static final String SEARCH_TRAJETS = 
        "SELECT t.*, gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM trajets t " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "WHERE gd.ville LIKE ? AND ga.ville LIKE ? AND t.actif=true " +
        "ORDER BY t.heure_depart";
    
    private static final String COUNT_TRAJETS = "SELECT COUNT(*) FROM trajets";
    
    @Override
    public Trajet save(Trajet trajet) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(INSERT_TRAJET, Statement.RETURN_GENERATED_KEYS)) {
            
            stmt.setLong(1, trajet.getGareDepart().getId());
            stmt.setLong(2, trajet.getGareArrivee().getId());
            stmt.setTime(3, Time.valueOf(trajet.getHeureDepart()));
            stmt.setTime(4, Time.valueOf(trajet.getHeureArrivee()));
            stmt.setBigDecimal(5, trajet.getPrix());
            stmt.setInt(6, trajet.getNombrePlaces());
            stmt.setString(7, trajet.getTypeTrain());
            stmt.setBoolean(8, trajet.isActif());
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Échec de la création du trajet");
            }
            
            try (ResultSet generatedKeys = stmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    trajet.setId(generatedKeys.getLong(1));
                }
            }
            
            return trajet;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la sauvegarde du trajet", e);
        }
    }
    
    @Override
    public Trajet update(Trajet trajet) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(UPDATE_TRAJET)) {
            
            stmt.setLong(1, trajet.getGareDepart().getId());
            stmt.setLong(2, trajet.getGareArrivee().getId());
            stmt.setTime(3, Time.valueOf(trajet.getHeureDepart()));
            stmt.setTime(4, Time.valueOf(trajet.getHeureArrivee()));
            stmt.setBigDecimal(5, trajet.getPrix());
            stmt.setInt(6, trajet.getNombrePlaces());
            stmt.setString(7, trajet.getTypeTrain());
            stmt.setBoolean(8, trajet.isActif());
            stmt.setLong(9, trajet.getId());
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Trajet non trouvé pour la mise à jour");
            }
            
            return trajet;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la mise à jour du trajet", e);
        }
    }
    
    @Override
    public boolean deleteById(Long id) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(DELETE_TRAJET)) {
            
            stmt.setLong(1, id);
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la suppression du trajet", e);
        }
    }
    
    @Override
    public Optional<Trajet> findById(Long id) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_BY_ID)) {
            
            stmt.setLong(1, id);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToTrajet(rs));
                }
            }
            return Optional.empty();
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche du trajet", e);
        }
    }
    
    @Override
    public List<Trajet> findAll() {
        List<Trajet> trajets = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_ALL);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                trajets.add(mapResultSetToTrajet(rs));
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la récupération des trajets", e);
        }
        return trajets;
    }
    
    @Override
    public List<Trajet> findByGares(Gare gareDepart, Gare gareArrivee) {
        return findByGareIds(gareDepart.getId(), gareArrivee.getId());
    }
    
    @Override
    public List<Trajet> findByGareIds(Long gareDepartId, Long gareArriveeId) {
        List<Trajet> trajets = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_BY_GARE_IDS)) {
            
            stmt.setLong(1, gareDepartId);
            stmt.setLong(2, gareArriveeId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    trajets.add(mapResultSetToTrajet(rs));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche par gares", e);
        }
        return trajets;
    }
    
    @Override
    public List<Trajet> findByGareDepart(Gare gareDepart) {
        List<Trajet> trajets = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_BY_GARE_DEPART)) {
            
            stmt.setLong(1, gareDepart.getId());
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    trajets.add(mapResultSetToTrajet(rs));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche par gare de départ", e);
        }
        return trajets;
    }
    
    @Override
    public List<Trajet> findByGareArrivee(Gare gareArrivee) {
        List<Trajet> trajets = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_BY_GARE_ARRIVEE)) {
            
            stmt.setLong(1, gareArrivee.getId());
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    trajets.add(mapResultSetToTrajet(rs));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche par gare d'arrivée", e);
        }
        return trajets;
    }
    
    @Override
    public List<Trajet> findActiveTrajects() {
        List<Trajet> trajets = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_ACTIVE);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                trajets.add(mapResultSetToTrajet(rs));
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche des trajets actifs", e);
        }
        return trajets;
    }
    
    @Override
    public List<Trajet> findByGare(Gare gare) {
        List<Trajet> trajets = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_BY_GARE)) {
            
            stmt.setLong(1, gare.getId());
            stmt.setLong(2, gare.getId());
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    trajets.add(mapResultSetToTrajet(rs));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche par gare", e);
        }
        return trajets;
    }
    
    @Override
    public List<Trajet> searchTrajets(String villeDepart, String villeArrivee) {
        List<Trajet> trajets = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(SEARCH_TRAJETS)) {
            
            stmt.setString(1, "%" + villeDepart + "%");
            stmt.setString(2, "%" + villeArrivee + "%");
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    trajets.add(mapResultSetToTrajet(rs));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche de trajets", e);
        }
        return trajets;
    }
    
    @Override
    public boolean existsById(Long id) {
        return findById(id).isPresent();
    }
    
    @Override
    public long count() {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(COUNT_TRAJETS);
             ResultSet rs = stmt.executeQuery()) {

            if (rs.next()) {
                return rs.getLong(1);
            }
            return 0;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors du comptage des trajets", e);
        }
    }

    public List<Trajet> findByActif(boolean actif) {
        List<Trajet> trajets = new ArrayList<>();
        String sql = "SELECT t.*, gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
                    "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
                    "FROM trajets t " +
                    "JOIN gares gd ON t.gare_depart_id = gd.id " +
                    "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
                    "WHERE t.actif=? " +
                    "ORDER BY gd.ville, ga.ville, t.heure_depart";

        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setBoolean(1, actif);

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    trajets.add(mapResultSetToTrajet(rs));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche par statut", e);
        }
        return trajets;
    }
    
    private Trajet mapResultSetToTrajet(ResultSet rs) throws SQLException {
        Trajet trajet = new Trajet();
        trajet.setId(rs.getLong("id"));
        
        // Gare de départ
        Gare gareDepart = new Gare();
        gareDepart.setId(rs.getLong("gare_depart_id"));
        gareDepart.setNom(rs.getString("gare_depart_nom"));
        gareDepart.setVille(rs.getString("gare_depart_ville"));
        gareDepart.setCodeGare(rs.getString("gare_depart_code"));
        trajet.setGareDepart(gareDepart);
        
        // Gare d'arrivée
        Gare gareArrivee = new Gare();
        gareArrivee.setId(rs.getLong("gare_arrivee_id"));
        gareArrivee.setNom(rs.getString("gare_arrivee_nom"));
        gareArrivee.setVille(rs.getString("gare_arrivee_ville"));
        gareArrivee.setCodeGare(rs.getString("gare_arrivee_code"));
        trajet.setGareArrivee(gareArrivee);
        
        trajet.setHeureDepart(rs.getTime("heure_depart").toLocalTime());
        trajet.setHeureArrivee(rs.getTime("heure_arrivee").toLocalTime());
        trajet.setPrix(rs.getBigDecimal("prix"));
        trajet.setNombrePlaces(rs.getInt("nombre_places"));
        trajet.setTypeTrain(rs.getString("type_train"));
        trajet.setActif(rs.getBoolean("actif"));

        return trajet;
    }
}
