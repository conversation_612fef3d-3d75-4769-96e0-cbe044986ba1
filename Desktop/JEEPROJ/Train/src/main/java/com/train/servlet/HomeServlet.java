package com.train.servlet;

import com.train.model.*;
import com.train.service.ReservationService;
import com.train.service.VoyageService;
import com.train.service.impl.DemoReservationServiceImpl;
import com.train.service.impl.DemoVoyageServiceImpl;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Servlet pour la page d'accueil avec dashboard client
 */
@WebServlet(name = "HomeServlet", urlPatterns = {"/home", "/"})
public class HomeServlet extends HttpServlet {

    private ReservationService reservationService = new DemoReservationServiceImpl();
    private VoyageService voyageService = new DemoVoyageServiceImpl();

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        HttpSession session = request.getSession(false);

        // Si l'utilisateur est connecté, afficher le dashboard personnalisé
        if (session != null && session.getAttribute("user") != null) {
            afficherDashboardClient(request, response, session);
        } else {
            // Afficher la page d'accueil publique
            request.getRequestDispatcher("/WEB-INF/views/home.jsp").forward(request, response);
        }
    }

    private void afficherDashboardClient(HttpServletRequest request, HttpServletResponse response, HttpSession session)
            throws ServletException, IOException {

        try {
            Long userId = (Long) session.getAttribute("userId");
            Utilisateur user = (Utilisateur) session.getAttribute("user");

            // Récupérer les réservations de l'utilisateur
            List<Reservation> mesReservations = reservationService.obtenirReservationsUtilisateur(userId);

            // Statistiques personnelles
            long nbReservationsTotal = mesReservations.size();
            long nbReservationsConfirmees = mesReservations.stream()
                    .filter(r -> r.getStatut() == StatutReservation.CONFIRMEE)
                    .count();
            long nbReservationsEnAttente = mesReservations.stream()
                    .filter(r -> r.getStatut() == StatutReservation.EN_ATTENTE)
                    .count();
            long nbReservationsAnnulees = mesReservations.stream()
                    .filter(r -> r.getStatut() == StatutReservation.ANNULEE)
                    .count();

            // Montant total dépensé
            double montantTotal = mesReservations.stream()
                    .filter(r -> r.getStatut() == StatutReservation.CONFIRMEE)
                    .mapToDouble(r -> r.getPrixTotal().doubleValue())
                    .sum();

            // Réservations récentes (5 dernières)
            List<Reservation> reservationsRecentes = mesReservations.stream()
                    .sorted((r1, r2) -> r2.getDateReservation().compareTo(r1.getDateReservation()))
                    .limit(5)
                    .collect(Collectors.toList());

            // Prochains voyages (réservations confirmées avec date future)
            List<Reservation> prochainsVoyages = mesReservations.stream()
                    .filter(r -> r.getStatut() == StatutReservation.CONFIRMEE)
                    .filter(r -> !r.getVoyage().isPasse())
                    .sorted((r1, r2) -> r1.getVoyage().getDateVoyage().compareTo(r2.getVoyage().getDateVoyage()))
                    .limit(3)
                    .collect(Collectors.toList());

            // Voyages disponibles (suggestions)
            List<Voyage> voyagesDisponibles = voyageService.obtenirTousLesVoyages().stream()
                    .filter(v -> !v.isPasse())
                    .filter(v -> v.getPlacesDisponibles() > 0)
                    .sorted((v1, v2) -> v1.getDateVoyage().compareTo(v2.getDateVoyage()))
                    .limit(6)
                    .collect(Collectors.toList());

            // Ajouter les attributs à la requête
            request.setAttribute("user", user);
            request.setAttribute("nbReservationsTotal", nbReservationsTotal);
            request.setAttribute("nbReservationsConfirmees", nbReservationsConfirmees);
            request.setAttribute("nbReservationsEnAttente", nbReservationsEnAttente);
            request.setAttribute("nbReservationsAnnulees", nbReservationsAnnulees);
            request.setAttribute("montantTotal", montantTotal);
            request.setAttribute("reservationsRecentes", reservationsRecentes);
            request.setAttribute("prochainsVoyages", prochainsVoyages);
            request.setAttribute("voyagesDisponibles", voyagesDisponibles);

            // Afficher le dashboard client
            request.getRequestDispatcher("/WEB-INF/views/client/dashboard.jsp").forward(request, response);

        } catch (Exception e) {
            // En cas d'erreur, afficher la page d'accueil standard
            request.setAttribute("errorMessage", "Erreur lors du chargement du dashboard : " + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/home.jsp").forward(request, response);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        doGet(request, response);
    }
}
