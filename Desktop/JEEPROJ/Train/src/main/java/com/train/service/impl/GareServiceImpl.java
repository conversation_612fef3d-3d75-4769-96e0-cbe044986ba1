package com.train.service.impl;

import com.train.dao.GareDAO;
import com.train.dao.impl.GareDAOImpl;
import com.train.model.Gare;
import com.train.service.GareService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Implémentation du service pour la gestion des gares
 */
public class GareServiceImpl implements GareService {
    
    private final GareDAO gareDAO;
    
    public GareServiceImpl() {
        this.gareDAO = new GareDAOImpl();
    }
    
    public GareServiceImpl(GareDAO gareDAO) {
        this.gareDAO = gareDAO;
    }
    
    @Override
    public Gare creerGare(Gare gare) {
        if (gare == null) {
            throw new IllegalArgumentException("La gare ne peut pas être null");
        }
        
        validerGare(gare);
        
        // Vérifier l'unicité du code gare
        if (codeGareExiste(gare.getCodeGare())) {
            throw new IllegalArgumentException("Une gare avec ce code existe déjà : " + gare.getCodeGare());
        }
        
        // Définir les valeurs par défaut
        gare.setDateCreation(LocalDateTime.now());
        if (gare.getCodeGare() != null) {
            gare.setCodeGare(gare.getCodeGare().toUpperCase());
        }
        
        return gareDAO.save(gare);
    }
    
    @Override
    public Gare modifierGare(Gare gare) {
        if (gare == null || gare.getId() == null) {
            throw new IllegalArgumentException("Gare invalide pour la modification");
        }
        
        // Vérifier que la gare existe
        Optional<Gare> existante = gareDAO.findById(gare.getId());
        if (!existante.isPresent()) {
            throw new IllegalArgumentException("Gare non trouvée avec l'ID : " + gare.getId());
        }
        
        validerGare(gare);
        
        // Vérifier l'unicité du code gare (sauf pour la gare actuelle)
        Optional<Gare> gareAvecMemeCode = gareDAO.findByCode(gare.getCodeGare());
        if (gareAvecMemeCode.isPresent() && !gareAvecMemeCode.get().getId().equals(gare.getId())) {
            throw new IllegalArgumentException("Une autre gare avec ce code existe déjà : " + gare.getCodeGare());
        }
        
        // Mettre à jour la date de modification
        gare.setDateModification(LocalDateTime.now());
        if (gare.getCodeGare() != null) {
            gare.setCodeGare(gare.getCodeGare().toUpperCase());
        }
        
        return gareDAO.update(gare);
    }
    
    @Override
    public boolean supprimerGare(Long gareId) {
        if (gareId == null) {
            throw new IllegalArgumentException("L'ID de la gare ne peut pas être null");
        }
        
        // Vérifier que la gare existe
        Optional<Gare> gare = gareDAO.findById(gareId);
        if (!gare.isPresent()) {
            throw new IllegalArgumentException("Gare non trouvée avec l'ID : " + gareId);
        }
        
        // TODO: Vérifier que la gare n'est pas utilisée dans des trajets
        // Cette vérification sera ajoutée quand le service TrajetService sera disponible
        
        return gareDAO.deleteById(gareId);
    }
    
    @Override
    public Gare obtenirGareParId(Long gareId) {
        if (gareId == null) {
            return null;
        }
        
        Optional<Gare> gare = gareDAO.findById(gareId);
        return gare.orElse(null);
    }
    
    @Override
    public Gare obtenirGareParCode(String codeGare) {
        if (codeGare == null || codeGare.trim().isEmpty()) {
            return null;
        }
        
        Optional<Gare> gare = gareDAO.findByCode(codeGare.toUpperCase());
        return gare.orElse(null);
    }
    
    @Override
    public List<Gare> obtenirToutesLesGares() {
        return gareDAO.findAll();
    }
    
    @Override
    public List<Gare> obtenirGaresActives() {
        return gareDAO.findActiveGares();
    }
    
    @Override
    public List<Gare> obtenirGaresParVille(String ville) {
        if (ville == null || ville.trim().isEmpty()) {
            throw new IllegalArgumentException("Le nom de la ville ne peut pas être vide");
        }
        
        return gareDAO.findByVille(ville.trim());
    }
    
    @Override
    public List<Gare> rechercherGaresParNom(String nom) {
        if (nom == null || nom.trim().isEmpty()) {
            return obtenirToutesLesGares();
        }
        
        return gareDAO.searchByName(nom.trim());
    }
    
    @Override
    public List<String> obtenirToutesLesVilles() {
        return gareDAO.findAllCities();
    }
    
    @Override
    public boolean codeGareExiste(String codeGare) {
        if (codeGare == null || codeGare.trim().isEmpty()) {
            return false;
        }
        
        return gareDAO.existsByCode(codeGare.toUpperCase());
    }
    
    @Override
    public boolean changerStatutGare(Long gareId, boolean active) {
        if (gareId == null) {
            return false;
        }
        
        Optional<Gare> gareOpt = gareDAO.findById(gareId);
        if (!gareOpt.isPresent()) {
            return false;
        }
        
        Gare gare = gareOpt.get();
        gare.setActive(active);
        gare.setDateModification(LocalDateTime.now());
        
        Gare gareModifiee = gareDAO.update(gare);
        return gareModifiee != null;
    }
    
    @Override
    public void validerGare(Gare gare) {
        if (gare == null) {
            throw new IllegalArgumentException("La gare ne peut pas être null");
        }
        
        // Validation du nom
        if (gare.getNom() == null || gare.getNom().trim().isEmpty()) {
            throw new IllegalArgumentException("Le nom de la gare est obligatoire");
        }
        
        if (gare.getNom().length() > 100) {
            throw new IllegalArgumentException("Le nom de la gare ne peut pas dépasser 100 caractères");
        }
        
        // Validation de la ville
        if (gare.getVille() == null || gare.getVille().trim().isEmpty()) {
            throw new IllegalArgumentException("La ville est obligatoire");
        }
        
        if (gare.getVille().length() > 100) {
            throw new IllegalArgumentException("Le nom de la ville ne peut pas dépasser 100 caractères");
        }
        
        // Validation du code gare
        if (gare.getCodeGare() == null || gare.getCodeGare().trim().isEmpty()) {
            throw new IllegalArgumentException("Le code de la gare est obligatoire");
        }
        
        if (gare.getCodeGare().length() < 3 || gare.getCodeGare().length() > 10) {
            throw new IllegalArgumentException("Le code de la gare doit contenir entre 3 et 10 caractères");
        }
        
        // Validation du code gare (format alphanumérique)
        if (!gare.getCodeGare().matches("^[A-Z0-9]+$")) {
            throw new IllegalArgumentException("Le code de la gare ne peut contenir que des lettres et des chiffres");
        }
        
        // Validation de l'adresse (optionnelle)
        if (gare.getAdresse() != null && gare.getAdresse().length() > 255) {
            throw new IllegalArgumentException("L'adresse ne peut pas dépasser 255 caractères");
        }
        
        // Validation du code postal (optionnel)
        if (gare.getCodePostal() != null && !gare.getCodePostal().trim().isEmpty()) {
            if (!gare.getCodePostal().matches("^[0-9]{5}$")) {
                throw new IllegalArgumentException("Le code postal doit contenir exactement 5 chiffres");
            }
        }
        
        // Validation des coordonnées GPS (optionnelles)
        if (gare.getLatitude() != null) {
            if (gare.getLatitude() < -90 || gare.getLatitude() > 90) {
                throw new IllegalArgumentException("La latitude doit être comprise entre -90 et 90");
            }
        }
        
        if (gare.getLongitude() != null) {
            if (gare.getLongitude() < -180 || gare.getLongitude() > 180) {
                throw new IllegalArgumentException("La longitude doit être comprise entre -180 et 180");
            }
        }
    }
}
