package com.train.dao.impl;

import com.train.dao.ReservationDAO;
import com.train.model.*;
import com.train.util.DatabaseConnection;

import java.math.BigDecimal;
import java.sql.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Implémentation DAO pour les réservations
 */
public class ReservationDAOImpl implements ReservationDAO {
    
    private static final String INSERT_RESERVATION = 
        "INSERT INTO reservations (numero_reservation, utilisateur_id, voyage_id, nombre_places, prix_total, statut, date_reservation) " +
        "VALUES (?, ?, ?, ?, ?, ?, ?)";
    
    private static final String UPDATE_RESERVATION = 
        "UPDATE reservations SET nombre_places=?, prix_total=?, statut=?, date_annulation=?, motif_annulation=? WHERE id=?";
    
    private static final String DELETE_RESERVATION = "DELETE FROM reservations WHERE id=?";
    
    private static final String FIND_BY_ID = 
        "SELECT r.*, u.nom as user_nom, u.prenom as user_prenom, u.email as user_email, " +
        "v.date_voyage, v.places_disponibles, v.places_reservees, v.statut as voyage_statut, " +
        "t.heure_depart, t.heure_arrivee, t.prix as trajet_prix, t.nombre_places as trajet_places, " +
        "gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM reservations r " +
        "JOIN utilisateurs u ON r.utilisateur_id = u.id " +
        "JOIN voyages v ON r.voyage_id = v.id " +
        "JOIN trajets t ON v.trajet_id = t.id " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "WHERE r.id=?";
    
    private static final String FIND_ALL = 
        "SELECT r.*, u.nom as user_nom, u.prenom as user_prenom, u.email as user_email, " +
        "v.date_voyage, v.places_disponibles, v.places_reservees, v.statut as voyage_statut, " +
        "t.heure_depart, t.heure_arrivee, t.prix as trajet_prix, t.nombre_places as trajet_places, " +
        "gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM reservations r " +
        "JOIN utilisateurs u ON r.utilisateur_id = u.id " +
        "JOIN voyages v ON r.voyage_id = v.id " +
        "JOIN trajets t ON v.trajet_id = t.id " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "ORDER BY r.date_reservation DESC";
    
    private static final String FIND_BY_NUMERO = 
        "SELECT r.*, u.nom as user_nom, u.prenom as user_prenom, u.email as user_email, " +
        "v.date_voyage, v.places_disponibles, v.places_reservees, v.statut as voyage_statut, " +
        "t.heure_depart, t.heure_arrivee, t.prix as trajet_prix, t.nombre_places as trajet_places, " +
        "gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM reservations r " +
        "JOIN utilisateurs u ON r.utilisateur_id = u.id " +
        "JOIN voyages v ON r.voyage_id = v.id " +
        "JOIN trajets t ON v.trajet_id = t.id " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "WHERE r.numero_reservation=?";
    
    private static final String FIND_BY_USER_ID = 
        "SELECT r.*, u.nom as user_nom, u.prenom as user_prenom, u.email as user_email, " +
        "v.date_voyage, v.places_disponibles, v.places_reservees, v.statut as voyage_statut, " +
        "t.heure_depart, t.heure_arrivee, t.prix as trajet_prix, t.nombre_places as trajet_places, " +
        "gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM reservations r " +
        "JOIN utilisateurs u ON r.utilisateur_id = u.id " +
        "JOIN voyages v ON r.voyage_id = v.id " +
        "JOIN trajets t ON v.trajet_id = t.id " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "WHERE r.utilisateur_id=? " +
        "ORDER BY r.date_reservation DESC";
    
    private static final String FIND_BY_STATUT = 
        "SELECT r.*, u.nom as user_nom, u.prenom as user_prenom, u.email as user_email, " +
        "v.date_voyage, v.places_disponibles, v.places_reservees, v.statut as voyage_statut, " +
        "t.heure_depart, t.heure_arrivee, t.prix as trajet_prix, t.nombre_places as trajet_places, " +
        "gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM reservations r " +
        "JOIN utilisateurs u ON r.utilisateur_id = u.id " +
        "JOIN voyages v ON r.voyage_id = v.id " +
        "JOIN trajets t ON v.trajet_id = t.id " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "WHERE r.statut=? " +
        "ORDER BY r.date_reservation DESC";
    
    private static final String FIND_ACTIVE_BY_USER = 
        "SELECT r.*, u.nom as user_nom, u.prenom as user_prenom, u.email as user_email, " +
        "v.date_voyage, v.places_disponibles, v.places_reservees, v.statut as voyage_statut, " +
        "t.heure_depart, t.heure_arrivee, t.prix as trajet_prix, t.nombre_places as trajet_places, " +
        "gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM reservations r " +
        "JOIN utilisateurs u ON r.utilisateur_id = u.id " +
        "JOIN voyages v ON r.voyage_id = v.id " +
        "JOIN trajets t ON v.trajet_id = t.id " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "WHERE r.utilisateur_id=? AND r.statut IN ('EN_ATTENTE', 'CONFIRMEE') " +
        "ORDER BY v.date_voyage, t.heure_depart";
    
    private static final String FIND_BY_DATE_VOYAGE = 
        "SELECT r.*, u.nom as user_nom, u.prenom as user_prenom, u.email as user_email, " +
        "v.date_voyage, v.places_disponibles, v.places_reservees, v.statut as voyage_statut, " +
        "t.heure_depart, t.heure_arrivee, t.prix as trajet_prix, t.nombre_places as trajet_places, " +
        "gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM reservations r " +
        "JOIN utilisateurs u ON r.utilisateur_id = u.id " +
        "JOIN voyages v ON r.voyage_id = v.id " +
        "JOIN trajets t ON v.trajet_id = t.id " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "WHERE v.date_voyage=? " +
        "ORDER BY t.heure_depart";
    
    private static final String FIND_BY_DATE_RANGE = 
        "SELECT r.*, u.nom as user_nom, u.prenom as user_prenom, u.email as user_email, " +
        "v.date_voyage, v.places_disponibles, v.places_reservees, v.statut as voyage_statut, " +
        "t.heure_depart, t.heure_arrivee, t.prix as trajet_prix, t.nombre_places as trajet_places, " +
        "gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
        "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
        "FROM reservations r " +
        "JOIN utilisateurs u ON r.utilisateur_id = u.id " +
        "JOIN voyages v ON r.voyage_id = v.id " +
        "JOIN trajets t ON v.trajet_id = t.id " +
        "JOIN gares gd ON t.gare_depart_id = gd.id " +
        "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
        "WHERE v.date_voyage BETWEEN ? AND ? " +
        "ORDER BY v.date_voyage, t.heure_depart";
    
    private static final String COUNT_BY_VOYAGE = "SELECT COUNT(*) FROM reservations WHERE voyage_id=?";
    
    private static final String SUM_PLACES_BY_VOYAGE = 
        "SELECT COALESCE(SUM(nombre_places), 0) FROM reservations WHERE voyage_id=? AND statut IN ('EN_ATTENTE', 'CONFIRMEE')";
    
    private static final String HAS_RESERVATION = 
        "SELECT COUNT(*) FROM reservations WHERE utilisateur_id=? AND voyage_id=? AND statut IN ('EN_ATTENTE', 'CONFIRMEE')";
    
    private static final String COUNT_RESERVATIONS = "SELECT COUNT(*) FROM reservations";
    
    @Override
    public Reservation save(Reservation reservation) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(INSERT_RESERVATION, Statement.RETURN_GENERATED_KEYS)) {
            
            stmt.setString(1, reservation.getNumeroReservation());
            stmt.setLong(2, reservation.getUtilisateur().getId());
            stmt.setLong(3, reservation.getVoyage().getId());
            stmt.setInt(4, reservation.getNombrePlaces());
            stmt.setBigDecimal(5, reservation.getPrixTotal());
            stmt.setString(6, reservation.getStatut().name());
            stmt.setTimestamp(7, Timestamp.valueOf(reservation.getDateReservation()));
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Échec de la création de la réservation");
            }
            
            try (ResultSet generatedKeys = stmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    reservation.setId(generatedKeys.getLong(1));
                }
            }
            
            return reservation;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la sauvegarde de la réservation", e);
        }
    }
    
    @Override
    public Reservation update(Reservation reservation) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(UPDATE_RESERVATION)) {
            
            stmt.setInt(1, reservation.getNombrePlaces());
            stmt.setBigDecimal(2, reservation.getPrixTotal());
            stmt.setString(3, reservation.getStatut().name());
            
            if (reservation.getDateAnnulation() != null) {
                stmt.setTimestamp(4, Timestamp.valueOf(reservation.getDateAnnulation()));
            } else {
                stmt.setNull(4, Types.TIMESTAMP);
            }
            
            stmt.setString(5, reservation.getMotifAnnulation());
            stmt.setLong(6, reservation.getId());
            
            int affectedRows = stmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Réservation non trouvée pour la mise à jour");
            }
            
            return reservation;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la mise à jour de la réservation", e);
        }
    }
    
    @Override
    public boolean deleteById(Long id) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(DELETE_RESERVATION)) {
            
            stmt.setLong(1, id);
            return stmt.executeUpdate() > 0;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la suppression de la réservation", e);
        }
    }
    
    @Override
    public Optional<Reservation> findById(Long id) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_BY_ID)) {
            
            stmt.setLong(1, id);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToReservation(rs));
                }
            }
            return Optional.empty();
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche de la réservation", e);
        }
    }
    
    @Override
    public List<Reservation> findAll() {
        List<Reservation> reservations = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_ALL);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                reservations.add(mapResultSetToReservation(rs));
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la récupération des réservations", e);
        }
        return reservations;
    }
    
    @Override
    public Optional<Reservation> findByNumero(String numeroReservation) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_BY_NUMERO)) {
            
            stmt.setString(1, numeroReservation);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToReservation(rs));
                }
            }
            return Optional.empty();
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche par numéro", e);
        }
    }
    
    @Override
    public List<Reservation> findByUtilisateur(Utilisateur utilisateur) {
        return findByUtilisateurId(utilisateur.getId());
    }
    
    @Override
    public List<Reservation> findByUtilisateurId(Long utilisateurId) {
        List<Reservation> reservations = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_BY_USER_ID)) {
            
            stmt.setLong(1, utilisateurId);
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    reservations.add(mapResultSetToReservation(rs));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche par utilisateur", e);
        }
        return reservations;
    }
    
    @Override
    public List<Reservation> findByVoyage(Voyage voyage) {
        List<Reservation> reservations = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(
                 "SELECT r.*, u.nom as user_nom, u.prenom as user_prenom, u.email as user_email, " +
                 "v.date_voyage, v.places_disponibles, v.places_reservees, v.statut as voyage_statut, " +
                 "t.heure_depart, t.heure_arrivee, t.prix as trajet_prix, t.nombre_places as trajet_places, " +
                 "gd.nom as gare_depart_nom, gd.ville as gare_depart_ville, gd.code_gare as gare_depart_code, " +
                 "ga.nom as gare_arrivee_nom, ga.ville as gare_arrivee_ville, ga.code_gare as gare_arrivee_code " +
                 "FROM reservations r " +
                 "JOIN utilisateurs u ON r.utilisateur_id = u.id " +
                 "JOIN voyages v ON r.voyage_id = v.id " +
                 "JOIN trajets t ON v.trajet_id = t.id " +
                 "JOIN gares gd ON t.gare_depart_id = gd.id " +
                 "JOIN gares ga ON t.gare_arrivee_id = ga.id " +
                 "WHERE r.voyage_id=? ORDER BY r.date_reservation")) {
            
            stmt.setLong(1, voyage.getId());
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    reservations.add(mapResultSetToReservation(rs));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche par voyage", e);
        }
        return reservations;
    }
    
    @Override
    public List<Reservation> findByStatut(StatutReservation statut) {
        List<Reservation> reservations = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_BY_STATUT)) {
            
            stmt.setString(1, statut.name());
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    reservations.add(mapResultSetToReservation(rs));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche par statut", e);
        }
        return reservations;
    }
    
    @Override
    public List<Reservation> findActiveReservationsByUser(Long utilisateurId) {
        List<Reservation> reservations = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_ACTIVE_BY_USER)) {
            
            stmt.setLong(1, utilisateurId);
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    reservations.add(mapResultSetToReservation(rs));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche des réservations actives", e);
        }
        return reservations;
    }
    
    @Override
    public List<Reservation> findByDateVoyage(LocalDate dateVoyage) {
        List<Reservation> reservations = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_BY_DATE_VOYAGE)) {
            
            stmt.setDate(1, Date.valueOf(dateVoyage));
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    reservations.add(mapResultSetToReservation(rs));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche par date", e);
        }
        return reservations;
    }
    
    @Override
    public List<Reservation> findByDateRange(LocalDate dateDebut, LocalDate dateFin) {
        List<Reservation> reservations = new ArrayList<>();
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(FIND_BY_DATE_RANGE)) {
            
            stmt.setDate(1, Date.valueOf(dateDebut));
            stmt.setDate(2, Date.valueOf(dateFin));
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    reservations.add(mapResultSetToReservation(rs));
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la recherche par période", e);
        }
        return reservations;
    }
    
    @Override
    public long countByVoyage(Long voyageId) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(COUNT_BY_VOYAGE)) {
            
            stmt.setLong(1, voyageId);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getLong(1);
                }
            }
            return 0;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors du comptage par voyage", e);
        }
    }
    
    @Override
    public int getTotalPlacesReservees(Long voyageId) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(SUM_PLACES_BY_VOYAGE)) {
            
            stmt.setLong(1, voyageId);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
            return 0;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors du calcul des places réservées", e);
        }
    }
    
    @Override
    public boolean hasReservationForVoyage(Long utilisateurId, Long voyageId) {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(HAS_RESERVATION)) {
            
            stmt.setLong(1, utilisateurId);
            stmt.setLong(2, voyageId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
            return false;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la vérification de réservation", e);
        }
    }
    
    @Override
    public boolean existsById(Long id) {
        return findById(id).isPresent();
    }
    
    @Override
    public long count() {
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(COUNT_RESERVATIONS);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                return rs.getLong(1);
            }
            return 0;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors du comptage des réservations", e);
        }
    }
    
    private Reservation mapResultSetToReservation(ResultSet rs) throws SQLException {
        Reservation reservation = new Reservation();
        reservation.setId(rs.getLong("id"));
        reservation.setNumeroReservation(rs.getString("numero_reservation"));
        reservation.setNombrePlaces(rs.getInt("nombre_places"));
        reservation.setPrixTotal(rs.getBigDecimal("prix_total"));
        reservation.setStatut(StatutReservation.valueOf(rs.getString("statut")));
        
        Timestamp dateReservation = rs.getTimestamp("date_reservation");
        if (dateReservation != null) {
            reservation.setDateReservation(dateReservation.toLocalDateTime());
        }
        
        Timestamp dateAnnulation = rs.getTimestamp("date_annulation");
        if (dateAnnulation != null) {
            reservation.setDateAnnulation(dateAnnulation.toLocalDateTime());
        }
        
        reservation.setMotifAnnulation(rs.getString("motif_annulation"));
        
        // Créer l'utilisateur
        Utilisateur utilisateur = new Utilisateur();
        utilisateur.setId(rs.getLong("utilisateur_id"));
        utilisateur.setNom(rs.getString("user_nom"));
        utilisateur.setPrenom(rs.getString("user_prenom"));
        utilisateur.setEmail(rs.getString("user_email"));
        reservation.setUtilisateur(utilisateur);
        
        // Créer le voyage avec trajet et gares
        Voyage voyage = new Voyage();
        voyage.setId(rs.getLong("voyage_id"));
        voyage.setDateVoyage(rs.getDate("date_voyage").toLocalDate());
        voyage.setPlacesDisponibles(rs.getInt("places_disponibles"));
        voyage.setPlacesReservees(rs.getInt("places_reservees"));
        voyage.setStatut(StatutVoyage.valueOf(rs.getString("voyage_statut")));
        
        // Créer le trajet
        Trajet trajet = new Trajet();
        trajet.setId(rs.getLong("trajet_id"));
        trajet.setHeureDepart(rs.getTime("heure_depart").toLocalTime());
        trajet.setHeureArrivee(rs.getTime("heure_arrivee").toLocalTime());
        trajet.setPrix(rs.getBigDecimal("trajet_prix"));
        trajet.setNombrePlaces(rs.getInt("trajet_places"));
        
        // Gares
        Gare gareDepart = new Gare();
        gareDepart.setId(rs.getLong("gare_depart_id"));
        gareDepart.setNom(rs.getString("gare_depart_nom"));
        gareDepart.setVille(rs.getString("gare_depart_ville"));
        gareDepart.setCodeGare(rs.getString("gare_depart_code"));
        trajet.setGareDepart(gareDepart);
        
        Gare gareArrivee = new Gare();
        gareArrivee.setId(rs.getLong("gare_arrivee_id"));
        gareArrivee.setNom(rs.getString("gare_arrivee_nom"));
        gareArrivee.setVille(rs.getString("gare_arrivee_ville"));
        gareArrivee.setCodeGare(rs.getString("gare_arrivee_code"));
        trajet.setGareArrivee(gareArrivee);
        
        voyage.setTrajet(trajet);
        reservation.setVoyage(voyage);
        
        return reservation;
    }
}
