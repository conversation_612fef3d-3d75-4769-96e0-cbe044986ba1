package com.train.model;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalTime;

/**
 * Entité représentant un trajet entre deux gares
 */
public class Trajet {
    
    private Long id;
    private Gare gareDepart;
    private Gare gareArrivee;
    private LocalTime heureDepart;
    private LocalTime heureArrivee;
    private Duration duree;
    private BigDecimal prix;
    private int nombrePlaces;
    private String typeTrain; // TGV, INTERCITES, TER, OUIGO
    private boolean actif;
    
    // Constructeurs
    public Trajet() {}
    
    public Trajet(Gare gareDepart, Gare gareArrivee, LocalTime heureDepart, 
                  LocalTime heureArrivee, BigDecimal prix, int nombrePlaces) {
        this.gareDepart = gareDepart;
        this.gareArrivee = gareArrivee;
        this.heureDepart = heureDepart;
        this.heureArrivee = heureArrivee;
        this.prix = prix;
        this.nombrePlaces = nombrePlaces;
        this.actif = true;
        
        // Calculer la durée
        if (heureDepart != null && heureArrivee != null) {
            this.duree = Duration.between(heureDepart, heureArrivee);
            // Si l'heure d'arrivée est le lendemain
            if (heureArrivee.isBefore(heureDepart)) {
                this.duree = this.duree.plusDays(1);
            }
        }
    }
    
    // Getters et Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Gare getGareDepart() {
        return gareDepart;
    }
    
    public void setGareDepart(Gare gareDepart) {
        this.gareDepart = gareDepart;
    }
    
    public Gare getGareArrivee() {
        return gareArrivee;
    }
    
    public void setGareArrivee(Gare gareArrivee) {
        this.gareArrivee = gareArrivee;
    }
    
    public LocalTime getHeureDepart() {
        return heureDepart;
    }
    
    public void setHeureDepart(LocalTime heureDepart) {
        this.heureDepart = heureDepart;
        calculerDuree();
    }
    
    public LocalTime getHeureArrivee() {
        return heureArrivee;
    }
    
    public void setHeureArrivee(LocalTime heureArrivee) {
        this.heureArrivee = heureArrivee;
        calculerDuree();
    }
    
    public Duration getDuree() {
        return duree;
    }
    
    public void setDuree(Duration duree) {
        this.duree = duree;
    }
    
    public BigDecimal getPrix() {
        return prix;
    }
    
    public void setPrix(BigDecimal prix) {
        this.prix = prix;
    }
    
    public int getNombrePlaces() {
        return nombrePlaces;
    }
    
    public void setNombrePlaces(int nombrePlaces) {
        this.nombrePlaces = nombrePlaces;
    }
    
    public boolean isActif() {
        return actif;
    }
    
    public void setActif(boolean actif) {
        this.actif = actif;
    }
    
    // Méthodes utilitaires
    private void calculerDuree() {
        if (heureDepart != null && heureArrivee != null) {
            this.duree = Duration.between(heureDepart, heureArrivee);
            if (heureArrivee.isBefore(heureDepart)) {
                this.duree = this.duree.plusDays(1);
            }
        }
    }
    
    public String getDureeFormatee() {
        if (duree == null) return "";
        long heures = duree.toHours();
        long minutes = duree.toMinutes() % 60;
        return String.format("%dh%02d", heures, minutes);
    }

    public String getTypeTrain() {
        return typeTrain;
    }

    public void setTypeTrain(String typeTrain) {
        this.typeTrain = typeTrain;
    }

    public String getLibelleTrajet() {
        return gareDepart.getVille() + " → " + gareArrivee.getVille();
    }
    
    @Override
    public String toString() {
        return "Trajet{" +
                "id=" + id +
                ", gareDepart=" + gareDepart.getVille() +
                ", gareArrivee=" + gareArrivee.getVille() +
                ", heureDepart=" + heureDepart +
                ", heureArrivee=" + heureArrivee +
                ", prix=" + prix +
                ", actif=" + actif +
                '}';
    }
}
