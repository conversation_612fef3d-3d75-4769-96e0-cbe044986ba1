package com.train.model;

/**
 * Énumération des rôles d'employés dans un voyage
 */
public enum RoleVoyage {
    CONDUCTEUR("Conducteur", "Responsable de la conduite du train", true),
    CONTROLEUR("Contrôleur", "Contrôle des billets et assistance aux voyageurs", false),
    CHEF_DE_BORD("Chef de bord", "Responsable de l'équipe et de la sécurité", true),
    AGENT_COMMERCIAL("Agent commercial", "Vente de billets et information voyageurs", false),
    TECHNICIEN("Technicien", "Maintenance et support technique", false);
    
    private final String libelle;
    private final String description;
    private final boolean principal;
    
    RoleVoyage(String libelle, String description, boolean principal) {
        this.libelle = libelle;
        this.description = description;
        this.principal = principal;
    }
    
    public String getLibelle() {
        return libelle;
    }
    
    public String getDescription() {
        return description;
    }
    
    public boolean isPrincipal() {
        return principal;
    }
    
    /**
     * <PERSON><PERSON>ne les rôles principaux (conducteur, chef de bord)
     */
    public static RoleVoyage[] getRolesPrincipaux() {
        return new RoleVoyage[]{CONDUCTEUR, CHEF_DE_BORD};
    }
    
    /**
     * Retourne les rôles secondaires
     */
    public static RoleVoyage[] getRolesSecondaires() {
        return new RoleVoyage[]{CONTROLEUR, AGENT_COMMERCIAL, TECHNICIEN};
    }
    
    @Override
    public String toString() {
        return libelle;
    }
}
