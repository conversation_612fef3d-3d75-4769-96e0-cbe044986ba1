package com.train.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <PERSON><PERSON><PERSON><PERSON> représentant un paiement
 */
public class Paiement {
    
    private Long id;
    private Long reservationId;
    private Reservation reservation;
    private BigDecimal montant;
    private StatutPaiement statut;
    private TypePaiement typePaiement;
    private String methodePaiement; // CARTE, PAYPAL, VIREMENT, etc.
    private String transactionId;
    private LocalDateTime datePaiement;
    private LocalDateTime dateCreation;
    private LocalDateTime dateModification;
    private String commentaire;
    
    // Constructeurs
    public Paiement() {
        this.dateCreation = LocalDateTime.now();
        this.statut = StatutPaiement.EN_ATTENTE;
    }
    
    public Paiement(Long reservationId, BigDecimal montant, String methodePaiement) {
        this();
        this.reservationId = reservationId;
        this.montant = montant;
        this.methodePaiement = methodePaiement;
        this.typePaiement = TypePaiement.PAIEMENT;
    }
    
    // Getters et Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getReservationId() {
        return reservationId;
    }
    
    public void setReservationId(Long reservationId) {
        this.reservationId = reservationId;
    }
    
    public Reservation getReservation() {
        return reservation;
    }
    
    public void setReservation(Reservation reservation) {
        this.reservation = reservation;
        if (reservation != null) {
            this.reservationId = reservation.getId();
        }
    }
    
    public BigDecimal getMontant() {
        return montant;
    }
    
    public void setMontant(BigDecimal montant) {
        this.montant = montant;
    }
    
    public StatutPaiement getStatut() {
        return statut;
    }
    
    public void setStatut(StatutPaiement statut) {
        this.statut = statut;
        this.dateModification = LocalDateTime.now();
    }
    
    public TypePaiement getTypePaiement() {
        return typePaiement;
    }
    
    public void setTypePaiement(TypePaiement typePaiement) {
        this.typePaiement = typePaiement;
    }
    
    public String getMethodePaiement() {
        return methodePaiement;
    }
    
    public void setMethodePaiement(String methodePaiement) {
        this.methodePaiement = methodePaiement;
    }
    
    public String getTransactionId() {
        return transactionId;
    }
    
    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }
    
    public LocalDateTime getDatePaiement() {
        return datePaiement;
    }
    
    public void setDatePaiement(LocalDateTime datePaiement) {
        this.datePaiement = datePaiement;
    }
    
    public LocalDateTime getDateCreation() {
        return dateCreation;
    }
    
    public void setDateCreation(LocalDateTime dateCreation) {
        this.dateCreation = dateCreation;
    }
    
    public LocalDateTime getDateModification() {
        return dateModification;
    }
    
    public void setDateModification(LocalDateTime dateModification) {
        this.dateModification = dateModification;
    }
    
    public String getCommentaire() {
        return commentaire;
    }
    
    public void setCommentaire(String commentaire) {
        this.commentaire = commentaire;
    }
    
    // Méthodes utilitaires
    public boolean isPaye() {
        return StatutPaiement.CONFIRME.equals(this.statut);
    }
    
    public boolean isEnAttente() {
        return StatutPaiement.EN_ATTENTE.equals(this.statut);
    }
    
    public boolean isEchec() {
        return StatutPaiement.ECHEC.equals(this.statut);
    }
    
    public boolean isRemboursement() {
        return TypePaiement.REMBOURSEMENT.equals(this.typePaiement);
    }
    
    public String getLibelleStatut() {
        if (statut == null) return "Inconnu";
        
        switch (statut) {
            case EN_ATTENTE:
                return "En attente";
            case CONFIRME:
                return "Confirmé";
            case ECHEC:
                return "Échec";
            case ANNULE:
                return "Annulé";
            case REMBOURSE:
                return "Remboursé";
            default:
                return statut.toString();
        }
    }
    
    public String getLibelleType() {
        if (typePaiement == null) return "Inconnu";
        
        switch (typePaiement) {
            case PAIEMENT:
                return "Paiement";
            case REMBOURSEMENT:
                return "Remboursement";
            case FRAIS:
                return "Frais";
            default:
                return typePaiement.toString();
        }
    }
    
    @Override
    public String toString() {
        return "Paiement{" +
                "id=" + id +
                ", reservationId=" + reservationId +
                ", montant=" + montant +
                ", statut=" + statut +
                ", methodePaiement='" + methodePaiement + '\'' +
                ", datePaiement=" + datePaiement +
                '}';
    }
}
