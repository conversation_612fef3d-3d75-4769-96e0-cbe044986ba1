package com.train.service.impl;

import com.train.service.ReservationService;
import com.train.model.*;
import com.train.util.DemoDataManager;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Implémentation du service réservation en mode démonstration
 */
public class DemoReservationServiceImpl implements ReservationService {
    
    private final DemoVoyageServiceImpl voyageService = new DemoVoyageServiceImpl();
    private final DemoUtilisateurServiceImpl utilisateurService = new DemoUtilisateurServiceImpl();
    
    @Override
    public Reservation creerReservation(Long utilisateurId, Long voyageId, int nombrePlaces) {
        if (utilisateurId == null || voyageId == null || nombrePlaces <= 0) {
            throw new IllegalArgumentException("Paramètres de réservation invalides");
        }
        
        Optional<Utilisateur> utilisateurOpt = utilisateurService.trouverParId(utilisateurId);
        if (!utilisateurOpt.isPresent()) {
            throw new IllegalArgumentException("Utilisateur non trouvé");
        }
        
        Optional<Voyage> voyageOpt = voyageService.trouverParId(voyageId);
        if (!voyageOpt.isPresent()) {
            throw new IllegalArgumentException("Voyage non trouvé");
        }
        
        Voyage voyage = voyageOpt.get();
        
        if (voyage.getDateVoyage().isBefore(LocalDate.now())) {
            throw new IllegalStateException("Impossible de réserver pour un voyage passé");
        }
        
        if (!voyageService.aPlacesDisponibles(voyageId, nombrePlaces)) {
            throw new IllegalStateException("Pas assez de places disponibles");
        }
        
        if (aDejaReservation(utilisateurId, voyageId)) {
            throw new IllegalStateException("Vous avez déjà une réservation pour ce voyage");
        }
        
        Reservation reservation = new Reservation();
        reservation.setUtilisateur(utilisateurOpt.get());
        reservation.setVoyage(voyage);
        reservation.setNombrePlaces(nombrePlaces);
        reservation.setPrixTotal(voyage.getTrajet().getPrix().multiply(BigDecimal.valueOf(nombrePlaces)));
        reservation.setStatut(StatutReservation.EN_ATTENTE);
        reservation.setDateReservation(LocalDateTime.now());
        reservation.setNumeroReservation(genererNumeroReservation());
        
        validerReservation(reservation);
        
        if (!voyageService.reserverPlaces(voyageId, nombrePlaces)) {
            throw new IllegalStateException("Échec de la réservation des places");
        }
        
        return DemoDataManager.saveReservation(reservation);
    }
    
    @Override
    public boolean confirmerReservation(Long reservationId) {
        if (reservationId == null) {
            return false;
        }
        
        Optional<Reservation> reservationOpt = DemoDataManager.findReservationById(reservationId);
        if (!reservationOpt.isPresent()) {
            return false;
        }
        
        Reservation reservation = reservationOpt.get();
        
        if (reservation.getStatut() != StatutReservation.EN_ATTENTE) {
            return false;
        }
        
        reservation.setStatut(StatutReservation.CONFIRMEE);
        return DemoDataManager.updateReservation(reservation);
    }
    
    @Override
    public boolean annulerReservation(Long reservationId, String motifAnnulation) {
        if (reservationId == null) {
            return false;
        }
        
        Optional<Reservation> reservationOpt = DemoDataManager.findReservationById(reservationId);
        if (!reservationOpt.isPresent()) {
            return false;
        }
        
        Reservation reservation = reservationOpt.get();
        
        if (!peutEtreAnnulee(reservationId)) {
            return false;
        }
        
        voyageService.libererPlaces(reservation.getVoyage().getId(), reservation.getNombrePlaces());
        
        reservation.setStatut(StatutReservation.ANNULEE);
        reservation.setDateAnnulation(LocalDateTime.now());
        reservation.setMotifAnnulation(motifAnnulation);
        
        return DemoDataManager.updateReservation(reservation);
    }
    
    @Override
    public Optional<Reservation> trouverParId(Long id) {
        if (id == null) {
            return Optional.empty();
        }
        return DemoDataManager.findReservationById(id);
    }
    
    @Override
    public Optional<Reservation> trouverParNumero(String numeroReservation) {
        if (numeroReservation == null || numeroReservation.trim().isEmpty()) {
            return Optional.empty();
        }
        
        return DemoDataManager.getAllReservations().stream()
                .filter(r -> r.getNumeroReservation().equals(numeroReservation.trim()))
                .findFirst();
    }
    
    @Override
    public List<Reservation> obtenirReservationsUtilisateur(Long utilisateurId) {
        if (utilisateurId == null) {
            throw new IllegalArgumentException("L'ID utilisateur ne peut pas être null");
        }
        return DemoDataManager.findReservationsByUserId(utilisateurId);
    }
    
    @Override
    public List<Reservation> obtenirReservationsActives(Long utilisateurId) {
        if (utilisateurId == null) {
            throw new IllegalArgumentException("L'ID utilisateur ne peut pas être null");
        }
        
        return DemoDataManager.findReservationsByUserId(utilisateurId).stream()
                .filter(r -> r.getStatut() == StatutReservation.EN_ATTENTE || r.getStatut() == StatutReservation.CONFIRMEE)
                .collect(java.util.stream.Collectors.toList());
    }
    
    @Override
    public List<Reservation> obtenirReservationsVoyage(Long voyageId) {
        if (voyageId == null) {
            throw new IllegalArgumentException("L'ID voyage ne peut pas être null");
        }
        
        return DemoDataManager.getAllReservations().stream()
                .filter(r -> r.getVoyage().getId().equals(voyageId))
                .collect(java.util.stream.Collectors.toList());
    }
    
    @Override
    public List<Reservation> obtenirReservationsParStatut(StatutReservation statut) {
        if (statut == null) {
            throw new IllegalArgumentException("Le statut ne peut pas être null");
        }
        
        return DemoDataManager.getAllReservations().stream()
                .filter(r -> r.getStatut() == statut)
                .collect(java.util.stream.Collectors.toList());
    }
    
    @Override
    public List<Reservation> obtenirReservationsParDate(LocalDate dateVoyage) {
        if (dateVoyage == null) {
            throw new IllegalArgumentException("La date ne peut pas être null");
        }
        
        return DemoDataManager.getAllReservations().stream()
                .filter(r -> r.getVoyage().getDateVoyage().equals(dateVoyage))
                .collect(java.util.stream.Collectors.toList());
    }
    
    @Override
    public List<Reservation> obtenirReservationsParPeriode(LocalDate dateDebut, LocalDate dateFin) {
        if (dateDebut == null || dateFin == null) {
            throw new IllegalArgumentException("Les dates ne peuvent pas être null");
        }
        if (dateDebut.isAfter(dateFin)) {
            throw new IllegalArgumentException("La date de début doit être antérieure à la date de fin");
        }
        
        return DemoDataManager.getAllReservations().stream()
                .filter(r -> !r.getVoyage().getDateVoyage().isBefore(dateDebut) && 
                            !r.getVoyage().getDateVoyage().isAfter(dateFin))
                .collect(java.util.stream.Collectors.toList());
    }
    
    @Override
    public boolean peutEtreAnnulee(Long reservationId) {
        if (reservationId == null) {
            return false;
        }
        
        Optional<Reservation> reservationOpt = DemoDataManager.findReservationById(reservationId);
        if (!reservationOpt.isPresent()) {
            return false;
        }
        
        Reservation reservation = reservationOpt.get();
        
        if (reservation.getStatut() != StatutReservation.CONFIRMEE && 
            reservation.getStatut() != StatutReservation.EN_ATTENTE) {
            return false;
        }
        
        return !reservation.getVoyage().getDateVoyage().isBefore(LocalDate.now());
    }
    
    @Override
    public double calculerMontantTotalUtilisateur(Long utilisateurId) {
        List<Reservation> reservations = obtenirReservationsUtilisateur(utilisateurId);
        return reservations.stream()
                .filter(r -> r.getStatut() == StatutReservation.CONFIRMEE)
                .mapToDouble(r -> r.getPrixTotal().doubleValue())
                .sum();
    }
    
    @Override
    public StatistiquesReservation calculerStatistiques(LocalDate dateDebut, LocalDate dateFin) {
        List<Reservation> reservations = obtenirReservationsParPeriode(dateDebut, dateFin);
        
        long nombreTotal = reservations.size();
        long nombreConfirmees = reservations.stream()
                .mapToLong(r -> r.getStatut() == StatutReservation.CONFIRMEE ? 1 : 0)
                .sum();
        long nombreAnnulees = reservations.stream()
                .mapToLong(r -> r.getStatut() == StatutReservation.ANNULEE ? 1 : 0)
                .sum();
        double chiffreAffaires = reservations.stream()
                .filter(r -> r.getStatut() == StatutReservation.CONFIRMEE)
                .mapToDouble(r -> r.getPrixTotal().doubleValue())
                .sum();
        
        return new StatistiquesReservation(nombreTotal, nombreConfirmees, nombreAnnulees, chiffreAffaires);
    }
    
    @Override
    public boolean aDejaReservation(Long utilisateurId, Long voyageId) {
        if (utilisateurId == null || voyageId == null) {
            return false;
        }
        return DemoDataManager.hasReservationForVoyage(utilisateurId, voyageId);
    }
    
    @Override
    public String genererNumeroReservation() {
        return "RES" + System.currentTimeMillis() + String.format("%03d", (int)(Math.random() * 1000));
    }
    
    @Override
    public void validerReservation(Reservation reservation) {
        if (reservation == null) {
            throw new IllegalArgumentException("La réservation ne peut pas être null");
        }
        
        if (reservation.getUtilisateur() == null) {
            throw new IllegalArgumentException("L'utilisateur est requis");
        }
        
        if (reservation.getVoyage() == null) {
            throw new IllegalArgumentException("Le voyage est requis");
        }
        
        if (reservation.getNombrePlaces() <= 0) {
            throw new IllegalArgumentException("Le nombre de places doit être positif");
        }
        
        if (reservation.getNombrePlaces() > 10) {
            throw new IllegalArgumentException("Maximum 10 places par réservation");
        }
        
        if (reservation.getPrixTotal() == null || reservation.getPrixTotal().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Le prix total doit être positif");
        }
        
        if (reservation.getNumeroReservation() == null || reservation.getNumeroReservation().trim().isEmpty()) {
            throw new IllegalArgumentException("Le numéro de réservation est requis");
        }
    }

    @Override
    public List<Reservation> obtenirToutesLesReservations() {
        return DemoDataManager.getAllReservations();
    }
}
