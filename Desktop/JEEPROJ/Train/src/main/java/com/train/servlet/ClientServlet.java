package com.train.servlet;

import com.train.model.*;
import com.train.service.ReservationService;
import com.train.service.VoyageService;
import com.train.service.impl.DemoReservationServiceImpl;
import com.train.service.impl.DemoVoyageServiceImpl;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Servlet pour la gestion de l'espace client
 */
@WebServlet(name = "ClientServlet", urlPatterns = {"/client/*"})
public class ClientServlet extends HttpServlet {

    private ReservationService reservationService = new DemoReservationServiceImpl();
    private VoyageService voyageService = new DemoVoyageServiceImpl();

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // Vérifier l'authentification
        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        Utilisateur user = (Utilisateur) session.getAttribute("user");
        
        // Vérifier que l'utilisateur est un client
        if (user.getTypeUtilisateur() != TypeUtilisateur.CLIENT) {
            response.sendError(HttpServletResponse.SC_FORBIDDEN, "Accès réservé aux clients");
            return;
        }
        
        String pathInfo = request.getPathInfo();
        if (pathInfo == null) {
            pathInfo = "/";
        }
        
        try {
            switch (pathInfo) {
                case "/":
                case "/dashboard":
                    afficherDashboard(request, response, session);
                    break;
                case "/profile":
                    afficherProfil(request, response, user);
                    break;
                case "/reservations":
                    afficherReservations(request, response, user);
                    break;
                default:
                    response.sendError(HttpServletResponse.SC_NOT_FOUND);
            }
        } catch (Exception e) {
            e.printStackTrace();
            request.setAttribute("errorMessage", "Une erreur est survenue : " + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/error.jsp").forward(request, response);
        }
    }
    
    private void afficherDashboard(HttpServletRequest request, HttpServletResponse response, HttpSession session)
            throws ServletException, IOException {

        try {
            System.out.println("=== Début afficherDashboard Client ===");
            Long userId = (Long) session.getAttribute("userId");
            Utilisateur user = (Utilisateur) session.getAttribute("user");

            System.out.println("UserId: " + userId);
            System.out.println("User: " + (user != null ? user.getPrenom() : "NULL"));

            // Récupérer les réservations de l'utilisateur
            List<Reservation> mesReservations = reservationService.obtenirReservationsUtilisateur(userId);
            System.out.println("Nombre de réservations: " + (mesReservations != null ? mesReservations.size() : "NULL"));

            // Calculer les statistiques
            int totalReservations = mesReservations != null ? mesReservations.size() : 0;
            int reservationsConfirmees = 0;
            int reservationsEnAttente = 0;
            double montantTotal = 0.0;

            if (mesReservations != null) {
                for (Reservation reservation : mesReservations) {
                    if (reservation.getStatut() == StatutReservation.CONFIRMEE) {
                        reservationsConfirmees++;
                    } else if (reservation.getStatut() == StatutReservation.EN_ATTENTE) {
                        reservationsEnAttente++;
                    }
                    montantTotal += reservation.getPrixTotal().doubleValue();
                }
            }

            // Récupérer les voyages disponibles (pour suggestions)
            List<Voyage> voyagesDisponibles = voyageService.obtenirTousLesVoyages();
            System.out.println("Nombre de voyages disponibles: " + (voyagesDisponibles != null ? voyagesDisponibles.size() : "NULL"));

            // Filtrer les voyages futurs
            List<Voyage> voyagesFuturs = null;
            if (voyagesDisponibles != null) {
                voyagesFuturs = voyagesDisponibles.stream()
                    .filter(v -> v.getDateVoyage().isAfter(LocalDate.now()) || v.getDateVoyage().isEqual(LocalDate.now()))
                    .limit(5)
                    .collect(Collectors.toList());
            }

            // Préparer les données pour la JSP
            request.setAttribute("user", user);
            request.setAttribute("mesReservations", mesReservations);
            request.setAttribute("voyagesSuggeres", voyagesFuturs);
            
            // Statistiques
            request.setAttribute("totalReservations", totalReservations);
            request.setAttribute("reservationsConfirmees", reservationsConfirmees);
            request.setAttribute("reservationsEnAttente", reservationsEnAttente);
            request.setAttribute("montantTotal", montantTotal);

            System.out.println("=== Statistiques calculées ===");
            System.out.println("Total: " + totalReservations);
            System.out.println("Confirmées: " + reservationsConfirmees);
            System.out.println("En attente: " + reservationsEnAttente);
            System.out.println("Montant: " + montantTotal);

            // Afficher le dashboard client
            System.out.println("=== Redirection vers dashboard client ===");
            request.getRequestDispatcher("/WEB-INF/views/client/dashboard.jsp").forward(request, response);

        } catch (Exception e) {
            // En cas d'erreur, afficher la page d'erreur
            System.out.println("=== ERREUR dans afficherDashboard ===");
            System.out.println("Erreur: " + e.getMessage());
            e.printStackTrace();
            request.setAttribute("errorMessage", "Erreur lors du chargement du dashboard : " + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/error.jsp").forward(request, response);
        }
    }
    
    private void afficherProfil(HttpServletRequest request, HttpServletResponse response, Utilisateur user)
            throws ServletException, IOException {
        
        request.setAttribute("user", user);
        request.getRequestDispatcher("/WEB-INF/views/client/profile.jsp").forward(request, response);
    }
    
    private void afficherReservations(HttpServletRequest request, HttpServletResponse response, Utilisateur user)
            throws ServletException, IOException {
        
        try {
            List<Reservation> reservations = reservationService.obtenirReservationsUtilisateur(user.getId());
            request.setAttribute("reservations", reservations);
            request.getRequestDispatcher("/WEB-INF/views/client/reservations.jsp").forward(request, response);
        } catch (Exception e) {
            request.setAttribute("errorMessage", "Erreur lors du chargement des réservations : " + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/error.jsp").forward(request, response);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        doGet(request, response);
    }
}
