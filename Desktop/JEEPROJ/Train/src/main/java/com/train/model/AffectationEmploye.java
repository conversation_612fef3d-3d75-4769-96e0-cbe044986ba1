package com.train.model;

import java.time.LocalDateTime;

/**
 * Modèle pour les affectations des employés aux voyages
 */
public class AffectationEmploye {
    
    private Long id;
    private Long employeId;
    private Long voyageId;
    private RoleVoyage roleVoyage;
    private LocalDateTime dateAffectation;
    private StatutAffectation statut;
    private String notes;
    
    // Relations
    private Utilisateur employe;
    private Voyage voyage;
    
    public enum RoleVoyage {
        CONDUCTEUR("Conducteur"),
        CONTROLEUR("Contrôleur"),
        CHEF_BORD("Chef de bord"),
        MAINTENANCE("Maintenance");
        
        private final String libelle;
        
        RoleVoyage(String libelle) {
            this.libelle = libelle;
        }
        
        public String getLibelle() {
            return libelle;
        }
    }
    
    public enum StatutAffectation {
        AFFECTE("Affecté"),
        CONFIRME("Confirmé"),
        TERMINE("Terminé"),
        ANNULE("Annulé");
        
        private final String libelle;
        
        StatutAffectation(String libelle) {
            this.libelle = libelle;
        }
        
        public String getLibelle() {
            return libelle;
        }
    }
    
    // Constructeurs
    public AffectationEmploye() {}
    
    public AffectationEmploye(Long employeId, Long voyageId, RoleVoyage roleVoyage) {
        this.employeId = employeId;
        this.voyageId = voyageId;
        this.roleVoyage = roleVoyage;
        this.dateAffectation = LocalDateTime.now();
        this.statut = StatutAffectation.AFFECTE;
    }
    
    // Getters et Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getEmployeId() {
        return employeId;
    }
    
    public void setEmployeId(Long employeId) {
        this.employeId = employeId;
    }
    
    public Long getVoyageId() {
        return voyageId;
    }
    
    public void setVoyageId(Long voyageId) {
        this.voyageId = voyageId;
    }
    
    public RoleVoyage getRoleVoyage() {
        return roleVoyage;
    }
    
    public void setRoleVoyage(RoleVoyage roleVoyage) {
        this.roleVoyage = roleVoyage;
    }
    
    public LocalDateTime getDateAffectation() {
        return dateAffectation;
    }
    
    public void setDateAffectation(LocalDateTime dateAffectation) {
        this.dateAffectation = dateAffectation;
    }
    
    public StatutAffectation getStatut() {
        return statut;
    }
    
    public void setStatut(StatutAffectation statut) {
        this.statut = statut;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    public Utilisateur getEmploye() {
        return employe;
    }
    
    public void setEmploye(Utilisateur employe) {
        this.employe = employe;
    }
    
    public Voyage getVoyage() {
        return voyage;
    }
    
    public void setVoyage(Voyage voyage) {
        this.voyage = voyage;
    }
    
    // Méthodes utilitaires
    
    /**
     * Confirme l'affectation
     */
    public void confirmer() {
        this.statut = StatutAffectation.CONFIRME;
    }
    
    /**
     * Termine l'affectation
     */
    public void terminer() {
        this.statut = StatutAffectation.TERMINE;
    }
    
    /**
     * Annule l'affectation
     */
    public void annuler() {
        this.statut = StatutAffectation.ANNULE;
    }
    
    /**
     * Vérifie si l'affectation est active
     */
    public boolean isActive() {
        return statut == StatutAffectation.AFFECTE || statut == StatutAffectation.CONFIRME;
    }
    
    /**
     * Obtient le nom complet de l'employé
     */
    public String getNomCompletEmploye() {
        if (employe != null) {
            return employe.getPrenom() + " " + employe.getNom();
        }
        return "Employé inconnu";
    }
    
    /**
     * Obtient les informations du voyage
     */
    public String getInfoVoyage() {
        if (voyage != null && voyage.getTrajet() != null) {
            return voyage.getTrajet().getGareDepart().getNom() + " → " + 
                   voyage.getTrajet().getGareArrivee().getNom() + 
                   " (" + voyage.getDateVoyage() + ")";
        }
        return "Voyage inconnu";
    }
    
    @Override
    public String toString() {
        return "AffectationEmploye{" +
                "id=" + id +
                ", employeId=" + employeId +
                ", voyageId=" + voyageId +
                ", roleVoyage=" + roleVoyage +
                ", statut=" + statut +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        AffectationEmploye that = (AffectationEmploye) o;
        
        if (!employeId.equals(that.employeId)) return false;
        return voyageId.equals(that.voyageId);
    }
    
    @Override
    public int hashCode() {
        int result = employeId.hashCode();
        result = 31 * result + voyageId.hashCode();
        return result;
    }
}
