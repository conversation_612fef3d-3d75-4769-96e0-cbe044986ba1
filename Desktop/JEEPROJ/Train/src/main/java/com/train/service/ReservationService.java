package com.train.service;

import com.train.model.Reservation;
import com.train.model.Utilisateur;
import com.train.model.Voyage;
import com.train.model.StatutReservation;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Interface de service pour la gestion des réservations
 */
public interface ReservationService {
    
    /**
     * Crée une nouvelle réservation
     * @param utilisateurId l'ID de l'utilisateur
     * @param voyageId l'ID du voyage
     * @param nombrePlaces nombre de places à réserver
     * @return la réservation créée
     * @throws IllegalStateException si pas assez de places disponibles
     */
    Reservation creerReservation(Long utilisateurId, Long voyageId, int nombrePlaces);
    
    /**
     * Confirme une réservation
     * @param reservationId l'ID de la réservation
     * @return true si la confirmation a réussi
     */
    boolean confirmerReservation(Long reservationId);
    
    /**
     * Annule une réservation
     * @param reservationId l'ID de la réservation
     * @param motifAnnulation motif de l'annulation
     * @return true si l'annulation a réussi
     */
    boolean annulerReservation(Long reservationId, String motifAnnulation);
    
    /**
     * Trouve une réservation par son ID
     * @param id l'ID de la réservation
     * @return Optional contenant la réservation si trouvée
     */
    Optional<Reservation> trouverParId(Long id);
    
    /**
     * Trouve une réservation par son numéro
     * @param numeroReservation le numéro de réservation
     * @return Optional contenant la réservation si trouvée
     */
    Optional<Reservation> trouverParNumero(String numeroReservation);
    
    /**
     * Obtient toutes les réservations d'un utilisateur
     * @param utilisateurId l'ID de l'utilisateur
     * @return liste des réservations de l'utilisateur
     */
    List<Reservation> obtenirReservationsUtilisateur(Long utilisateurId);
    
    /**
     * Obtient les réservations actives d'un utilisateur
     * @param utilisateurId l'ID de l'utilisateur
     * @return liste des réservations actives
     */
    List<Reservation> obtenirReservationsActives(Long utilisateurId);
    
    /**
     * Obtient les réservations pour un voyage
     * @param voyageId l'ID du voyage
     * @return liste des réservations pour ce voyage
     */
    List<Reservation> obtenirReservationsVoyage(Long voyageId);
    
    /**
     * Obtient les réservations par statut
     * @param statut le statut de réservation
     * @return liste des réservations avec ce statut
     */
    List<Reservation> obtenirReservationsParStatut(StatutReservation statut);
    
    /**
     * Obtient les réservations pour une date de voyage
     * @param dateVoyage la date du voyage
     * @return liste des réservations pour cette date
     */
    List<Reservation> obtenirReservationsParDate(LocalDate dateVoyage);
    
    /**
     * Obtient les réservations dans une période
     * @param dateDebut date de début
     * @param dateFin date de fin
     * @return liste des réservations dans la période
     */
    List<Reservation> obtenirReservationsParPeriode(LocalDate dateDebut, LocalDate dateFin);
    
    /**
     * Vérifie si une réservation peut être annulée
     * @param reservationId l'ID de la réservation
     * @return true si la réservation peut être annulée
     */
    boolean peutEtreAnnulee(Long reservationId);
    
    /**
     * Calcule le montant total des réservations d'un utilisateur
     * @param utilisateurId l'ID de l'utilisateur
     * @return montant total
     */
    double calculerMontantTotalUtilisateur(Long utilisateurId);
    
    /**
     * Calcule les statistiques de réservation pour une période
     * @param dateDebut date de début
     * @param dateFin date de fin
     * @return statistiques de réservation
     */
    StatistiquesReservation calculerStatistiques(LocalDate dateDebut, LocalDate dateFin);
    
    /**
     * Vérifie si un utilisateur a déjà une réservation pour un voyage
     * @param utilisateurId l'ID de l'utilisateur
     * @param voyageId l'ID du voyage
     * @return true si une réservation existe
     */
    boolean aDejaReservation(Long utilisateurId, Long voyageId);
    
    /**
     * Génère un numéro de réservation unique
     * @return numéro de réservation
     */
    String genererNumeroReservation();
    
    /**
     * Valide les données d'une réservation
     * @param reservation la réservation à valider
     * @throws IllegalArgumentException si les données sont invalides
     */
    void validerReservation(Reservation reservation);

    /**
     * Obtient toutes les réservations
     * @return liste de toutes les réservations
     */
    List<Reservation> obtenirToutesLesReservations();
    
    /**
     * Classe pour les statistiques de réservation
     */
    class StatistiquesReservation {
        private long nombreReservations;
        private long nombreReservationsConfirmees;
        private long nombreReservationsAnnulees;
        private double chiffreAffaires;
        private double tauxAnnulation;
        
        // Constructeurs, getters et setters
        public StatistiquesReservation() {}
        
        public StatistiquesReservation(long nombreReservations, long nombreReservationsConfirmees, 
                                     long nombreReservationsAnnulees, double chiffreAffaires) {
            this.nombreReservations = nombreReservations;
            this.nombreReservationsConfirmees = nombreReservationsConfirmees;
            this.nombreReservationsAnnulees = nombreReservationsAnnulees;
            this.chiffreAffaires = chiffreAffaires;
            this.tauxAnnulation = nombreReservations > 0 ? 
                (double) nombreReservationsAnnulees / nombreReservations * 100 : 0;
        }
        
        // Getters et setters
        public long getNombreReservations() { return nombreReservations; }
        public void setNombreReservations(long nombreReservations) { this.nombreReservations = nombreReservations; }
        
        public long getNombreReservationsConfirmees() { return nombreReservationsConfirmees; }
        public void setNombreReservationsConfirmees(long nombreReservationsConfirmees) { 
            this.nombreReservationsConfirmees = nombreReservationsConfirmees; 
        }
        
        public long getNombreReservationsAnnulees() { return nombreReservationsAnnulees; }
        public void setNombreReservationsAnnulees(long nombreReservationsAnnulees) { 
            this.nombreReservationsAnnulees = nombreReservationsAnnulees; 
        }
        
        public double getChiffreAffaires() { return chiffreAffaires; }
        public void setChiffreAffaires(double chiffreAffaires) { this.chiffreAffaires = chiffreAffaires; }
        
        public double getTauxAnnulation() { return tauxAnnulation; }
        public void setTauxAnnulation(double tauxAnnulation) { this.tauxAnnulation = tauxAnnulation; }
    }
}
