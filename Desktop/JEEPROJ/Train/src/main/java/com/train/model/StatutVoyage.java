package com.train.model;

/**
 * Énumération des statuts possibles d'un voyage
 */
public enum StatutVoyage {
    PROGRAMME("Programmé"),
    EN_COURS("En cours"),
    TERMINE("<PERSON>rmin<PERSON>"),
    ANNULE("<PERSON><PERSON>é"),
    RETARDE("Retardé");
    
    private final String libelle;
    
    StatutVoyage(String libelle) {
        this.libelle = libelle;
    }
    
    public String getLibelle() {
        return libelle;
    }
    
    @Override
    public String toString() {
        return libelle;
    }
}
