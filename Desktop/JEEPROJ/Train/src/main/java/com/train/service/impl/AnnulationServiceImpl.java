package com.train.service.impl;

import com.train.model.*;
import com.train.service.AnnulationService;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * Implémentation du service de gestion des annulations avec données de démonstration
 */
public class AnnulationServiceImpl implements AnnulationService {
    
    private final Map<Long, DemandeAnnulation> demandes = new HashMap<>();
    private final AtomicLong idGenerator = new AtomicLong(1);
    
    public AnnulationServiceImpl() {
        initialiserDonneesDemo();
    }
    
    private void initialiserDonneesDemo() {
        // Demandes en attente
        creerDemandeDemo(1L, 1L, "Changement de programme personnel", StatutAnnulation.EN_ATTENTE, 
                        new BigDecimal("89.50"), new BigDecimal("15.00"));
        creerDemandeDemo(2L, 2L, "Problème de santé", StatutAnnulation.EN_ATTENTE, 
                        new BigDecimal("65.00"), new BigDecimal("10.00"));
        creerDemandeDemo(3L, 3L, "Annulation du voyage professionnel", StatutAnnulation.EN_ATTENTE, 
                        new BigDecimal("120.00"), new BigDecimal("20.00"));
        
        // Demandes approuvées
        creerDemandeDemo(4L, 4L, "Urgence familiale", StatutAnnulation.APPROUVEE, 
                        new BigDecimal("75.50"), new BigDecimal("12.00"));
        creerDemandeDemo(5L, 5L, "Grève des transports", StatutAnnulation.APPROUVEE, 
                        new BigDecimal("95.00"), new BigDecimal("0.00")); // Pas de frais pour grève
        
        // Demandes rejetées
        creerDemandeDemo(6L, 6L, "Changement d'avis", StatutAnnulation.REJETEE, 
                        new BigDecimal("110.00"), new BigDecimal("25.00"));
        
        // Demandes traitées
        creerDemandeDemo(7L, 7L, "Maladie avec certificat médical", StatutAnnulation.TRAITEE, 
                        new BigDecimal("85.00"), new BigDecimal("5.00"));
    }
    
    private void creerDemandeDemo(Long reservationId, Long utilisateurId, String motif, 
                                  StatutAnnulation statut, BigDecimal montantPaye, BigDecimal fraisAnnulation) {
        DemandeAnnulation demande = new DemandeAnnulation();
        demande.setId(idGenerator.getAndIncrement());
        demande.setReservationId(reservationId);
        demande.setUtilisateurId(utilisateurId);
        demande.setMotif(motif);
        demande.setStatut(statut);
        demande.setMontantPaye(montantPaye);
        demande.setFraisAnnulation(fraisAnnulation);
        demande.calculerMontantRemboursement();
        demande.setDateDemande(LocalDateTime.now().minusDays(new Random().nextInt(15)));
        
        if (!StatutAnnulation.EN_ATTENTE.equals(statut)) {
            demande.setDateTraitement(demande.getDateDemande().plusHours(new Random().nextInt(48)));
            demande.setAdminId(1L); // Admin par défaut
            
            if (StatutAnnulation.REJETEE.equals(statut)) {
                demande.setCommentaireAdmin("Demande hors délai d'annulation gratuite");
            } else if (StatutAnnulation.APPROUVEE.equals(statut)) {
                demande.setCommentaireAdmin("Demande approuvée selon nos conditions");
            } else if (StatutAnnulation.TRAITEE.equals(statut)) {
                demande.setCommentaireAdmin("Remboursement effectué");
            }
        }
        
        demandes.put(demande.getId(), demande);
    }
    
    @Override
    public DemandeAnnulation creerDemandeAnnulation(DemandeAnnulation demande) {
        if (demande.getId() == null) {
            demande.setId(idGenerator.getAndIncrement());
        }
        demande.setDateDemande(LocalDateTime.now());
        demande.setStatut(StatutAnnulation.EN_ATTENTE);
        demandes.put(demande.getId(), demande);
        return demande;
    }
    
    @Override
    public Optional<DemandeAnnulation> obtenirDemandeParId(Long id) {
        return Optional.ofNullable(demandes.get(id));
    }
    
    @Override
    public List<DemandeAnnulation> obtenirToutesLesDemandes() {
        return new ArrayList<>(demandes.values());
    }
    
    @Override
    public List<DemandeAnnulation> obtenirDemandesParUtilisateur(Long utilisateurId) {
        return demandes.values().stream()
                .filter(d -> Objects.equals(d.getUtilisateurId(), utilisateurId))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<DemandeAnnulation> obtenirDemandesParStatut(StatutAnnulation statut) {
        return demandes.values().stream()
                .filter(d -> Objects.equals(d.getStatut(), statut))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<DemandeAnnulation> obtenirDemandesParPeriode(LocalDateTime dateDebut, LocalDateTime dateFin) {
        return demandes.values().stream()
                .filter(d -> d.getDateDemande().isAfter(dateDebut) && d.getDateDemande().isBefore(dateFin))
                .collect(Collectors.toList());
    }
    
    @Override
    public DemandeAnnulation mettreAJourDemande(DemandeAnnulation demande) {
        if (demande.getId() != null && demandes.containsKey(demande.getId())) {
            demandes.put(demande.getId(), demande);
            return demande;
        }
        throw new IllegalArgumentException("Demande d'annulation non trouvée");
    }
    
    @Override
    public boolean approuverDemande(Long demandeId, Long adminId, String commentaire) {
        Optional<DemandeAnnulation> optDemande = obtenirDemandeParId(demandeId);
        if (optDemande.isPresent()) {
            DemandeAnnulation demande = optDemande.get();
            demande.setStatut(StatutAnnulation.APPROUVEE);
            demande.setAdminId(adminId);
            demande.setCommentaireAdmin(commentaire);
            demande.setDateTraitement(LocalDateTime.now());
            mettreAJourDemande(demande);
            return true;
        }
        return false;
    }
    
    @Override
    public boolean rejeterDemande(Long demandeId, Long adminId, String motif) {
        Optional<DemandeAnnulation> optDemande = obtenirDemandeParId(demandeId);
        if (optDemande.isPresent()) {
            DemandeAnnulation demande = optDemande.get();
            demande.setStatut(StatutAnnulation.REJETEE);
            demande.setAdminId(adminId);
            demande.setCommentaireAdmin(motif);
            demande.setDateTraitement(LocalDateTime.now());
            mettreAJourDemande(demande);
            return true;
        }
        return false;
    }
    
    @Override
    public boolean marquerTraitee(Long demandeId, Long adminId) {
        Optional<DemandeAnnulation> optDemande = obtenirDemandeParId(demandeId);
        if (optDemande.isPresent()) {
            DemandeAnnulation demande = optDemande.get();
            if (StatutAnnulation.APPROUVEE.equals(demande.getStatut())) {
                demande.setStatut(StatutAnnulation.TRAITEE);
                demande.setAdminId(adminId);
                demande.setDateTraitement(LocalDateTime.now());
                mettreAJourDemande(demande);
                return true;
            }
        }
        return false;
    }
    
    @Override
    public BigDecimal calculerFraisAnnulation(Long reservationId) {
        // Logique simplifiée : 15% du montant avec minimum 5€ et maximum 25€
        BigDecimal montantReservation = new BigDecimal("75.00"); // Simulé
        BigDecimal frais = montantReservation.multiply(new BigDecimal("0.15"));
        
        if (frais.compareTo(new BigDecimal("5.00")) < 0) {
            frais = new BigDecimal("5.00");
        } else if (frais.compareTo(new BigDecimal("25.00")) > 0) {
            frais = new BigDecimal("25.00");
        }
        
        return frais;
    }
    
    @Override
    public BigDecimal calculerMontantRemboursement(Long reservationId, BigDecimal fraisAnnulation) {
        BigDecimal montantPaye = new BigDecimal("75.00"); // Simulé
        BigDecimal remboursement = montantPaye.subtract(fraisAnnulation);
        return remboursement.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : remboursement;
    }
    
    @Override
    public boolean peutEtreAnnulee(Long reservationId) {
        // Logique simplifiée : peut être annulée si le voyage est dans plus de 24h
        return true; // Simulé
    }
    
    @Override
    public long compterDemandesParStatut(StatutAnnulation statut) {
        return demandes.values().stream()
                .filter(d -> Objects.equals(d.getStatut(), statut))
                .count();
    }
    
    @Override
    public StatistiquesAnnulation obtenirStatistiques() {
        StatistiquesAnnulation stats = new StatistiquesAnnulation();
        
        stats.setDemandesEnAttente(compterDemandesParStatut(StatutAnnulation.EN_ATTENTE));
        stats.setDemandesApprouvees(compterDemandesParStatut(StatutAnnulation.APPROUVEE));
        stats.setDemandesRejetees(compterDemandesParStatut(StatutAnnulation.REJETEE));
        stats.setDemandesTraitees(compterDemandesParStatut(StatutAnnulation.TRAITEE));
        
        // Montant total à rembourser (demandes approuvées non traitées)
        BigDecimal montantARembouser = demandes.values().stream()
                .filter(d -> StatutAnnulation.APPROUVEE.equals(d.getStatut()))
                .map(DemandeAnnulation::getMontantRemboursement)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        stats.setMontantTotalARembouser(montantARembouser);
        
        // Montant total remboursé (demandes traitées)
        BigDecimal montantRembourse = demandes.values().stream()
                .filter(d -> StatutAnnulation.TRAITEE.equals(d.getStatut()))
                .map(DemandeAnnulation::getMontantRemboursement)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        stats.setMontantTotalRembourse(montantRembourse);
        
        // Frais total d'annulation
        BigDecimal fraisTotal = demandes.values().stream()
                .filter(d -> !StatutAnnulation.REJETEE.equals(d.getStatut()))
                .map(DemandeAnnulation::getFraisAnnulation)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        stats.setFraisTotalAnnulation(fraisTotal);
        
        return stats;
    }
}
