package com.train.dao;

import com.train.model.Paiement;
import com.train.model.StatutPaiement;
import com.train.model.TypePaiement;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Interface DAO pour la gestion des paiements
 */
public interface PaiementDAO {
    
    /**
     * Créer un nouveau paiement
     */
    Paiement creer(Paiement paiement) throws SQLException;
    
    /**
     * Obtenir un paiement par son ID
     */
    Optional<Paiement> obtenirParId(Long id) throws SQLException;
    
    /**
     * Obtenir tous les paiements
     */
    List<Paiement> obtenirTous() throws SQLException;
    
    /**
     * Obtenir les paiements par réservation
     */
    List<Paiement> obtenirParReservation(Long reservationId) throws SQLException;
    
    /**
     * Obtenir les paiements par statut
     */
    List<Paiement> obtenirParStatut(StatutPaiement statut) throws SQLException;
    
    /**
     * Obtenir les paiements par type
     */
    List<Paiement> obtenirParType(TypePaiement type) throws SQLException;
    
    /**
     * Obtenir les paiements par période
     */
    List<Paiement> obtenirParPeriode(LocalDateTime dateDebut, LocalDateTime dateFin) throws SQLException;
    
    /**
     * Mettre à jour un paiement
     */
    Paiement mettreAJour(Paiement paiement) throws SQLException;
    
    /**
     * Supprimer un paiement
     */
    boolean supprimer(Long id) throws SQLException;
    
    /**
     * Calculer le total des revenus
     */
    BigDecimal calculerRevenuTotal() throws SQLException;
    
    /**
     * Calculer les revenus par période
     */
    BigDecimal calculerRevenuParPeriode(LocalDateTime dateDebut, LocalDateTime dateFin) throws SQLException;
    
    /**
     * Compter les paiements par statut
     */
    long compterParStatut(StatutPaiement statut) throws SQLException;
    
    /**
     * Compter les paiements par type
     */
    long compterParType(TypePaiement type) throws SQLException;
    
    /**
     * Obtenir les statistiques de paiement
     */
    StatistiquesPaiement obtenirStatistiques() throws SQLException;
    
    /**
     * Classe pour les statistiques de paiement
     */
    class StatistiquesPaiement {
        private BigDecimal revenuTotal;
        private BigDecimal revenuMois;
        private BigDecimal paiementsEnAttente;
        private BigDecimal remboursementsATraiter;
        private BigDecimal remboursementsEffectues;
        private long nombrePaiements;
        private long nombreRemboursements;
        
        // Constructeurs
        public StatistiquesPaiement() {}
        
        // Getters et Setters
        public BigDecimal getRevenuTotal() { return revenuTotal; }
        public void setRevenuTotal(BigDecimal revenuTotal) { this.revenuTotal = revenuTotal; }
        
        public BigDecimal getRevenuMois() { return revenuMois; }
        public void setRevenuMois(BigDecimal revenuMois) { this.revenuMois = revenuMois; }
        
        public BigDecimal getPaiementsEnAttente() { return paiementsEnAttente; }
        public void setPaiementsEnAttente(BigDecimal paiementsEnAttente) { this.paiementsEnAttente = paiementsEnAttente; }
        
        public BigDecimal getRemboursementsATraiter() { return remboursementsATraiter; }
        public void setRemboursementsATraiter(BigDecimal remboursementsATraiter) { this.remboursementsATraiter = remboursementsATraiter; }
        
        public BigDecimal getRemboursementsEffectues() { return remboursementsEffectues; }
        public void setRemboursementsEffectues(BigDecimal remboursementsEffectues) { this.remboursementsEffectues = remboursementsEffectues; }
        
        public long getNombrePaiements() { return nombrePaiements; }
        public void setNombrePaiements(long nombrePaiements) { this.nombrePaiements = nombrePaiements; }
        
        public long getNombreRemboursements() { return nombreRemboursements; }
        public void setNombreRemboursements(long nombreRemboursements) { this.nombreRemboursements = nombreRemboursements; }
    }
}
