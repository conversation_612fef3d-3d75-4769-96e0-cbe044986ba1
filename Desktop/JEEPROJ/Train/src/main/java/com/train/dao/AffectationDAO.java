package com.train.dao;

import com.train.model.Affectation;
import com.train.model.RoleVoyage;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Interface DAO pour la gestion des affectations d'employés
 */
public interface AffectationDAO {
    
    /**
     * Créer une nouvelle affectation
     */
    Affectation creer(Affectation affectation) throws SQLException;
    
    /**
     * Obtenir une affectation par son ID
     */
    Optional<Affectation> obtenirParId(Long id) throws SQLException;
    
    /**
     * Obtenir toutes les affectations
     */
    List<Affectation> obtenirTous() throws SQLException;
    
    /**
     * Obtenir les affectations par voyage
     */
    List<Affectation> obtenirParVoyage(Long voyageId) throws SQLException;
    
    /**
     * Obtenir les affectations par employé
     */
    List<Affectation> obtenirParEmploye(Long employeId) throws SQLException;
    
    /**
     * Obtenir les affectations actives par employé
     */
    List<Affectation> obtenirActivesParEmploye(Long employeId) throws SQLException;
    
    /**
     * Obtenir les affectations par rôle
     */
    List<Affectation> obtenirParRole(RoleVoyage role) throws SQLException;
    
    /**
     * Obtenir les affectations par période
     */
    List<Affectation> obtenirParPeriode(LocalDate dateDebut, LocalDate dateFin) throws SQLException;
    
    /**
     * Mettre à jour une affectation
     */
    Affectation mettreAJour(Affectation affectation) throws SQLException;
    
    /**
     * Supprimer une affectation
     */
    boolean supprimer(Long id) throws SQLException;
    
    /**
     * Désactiver une affectation
     */
    boolean desactiver(Long id) throws SQLException;
    
    /**
     * Vérifier si un employé est déjà affecté à un voyage avec un rôle
     */
    boolean existeAffectation(Long voyageId, Long employeId, RoleVoyage role) throws SQLException;
    
    /**
     * Compter les affectations par statut
     */
    long compterParStatut(boolean actif) throws SQLException;
    
    /**
     * Compter les affectations par rôle
     */
    long compterParRole(RoleVoyage role) throws SQLException;
    
    /**
     * Obtenir les employés affectés à un voyage
     */
    List<Long> obtenirEmployesAffectes(Long voyageId) throws SQLException;
    
    /**
     * Obtenir les voyages assignés à un employé pour une période
     */
    List<Long> obtenirVoyagesAssignes(Long employeId, LocalDate dateDebut, LocalDate dateFin) throws SQLException;
}
