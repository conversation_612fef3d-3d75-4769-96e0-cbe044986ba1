package com.train.service.impl;

import com.train.model.*;
import com.train.service.AffectationService;
import com.train.service.UtilisateurService;
import com.train.service.VoyageService;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * Implémentation du service de gestion des affectations avec données de démonstration
 */
public class AffectationServiceImpl implements AffectationService {
    
    private final Map<Long, Affectation> affectations = new HashMap<>();
    private final AtomicLong idGenerator = new AtomicLong(1);
    private final UtilisateurService utilisateurService;
    private final VoyageService voyageService;
    
    public AffectationServiceImpl(UtilisateurService utilisateurService, VoyageService voyageService) {
        this.utilisateurService = utilisateurService;
        this.voyageService = voyageService;
        initialiserDonneesDemo();
    }
    
    private void initialiserDonneesDemo() {
        // Créer quelques affectations de démonstration
        creerAffectationDemo(1L, 2L, RoleVoyage.CONDUCTEUR); // Employé 2 comme conducteur voyage 1
        creerAffectationDemo(1L, 3L, RoleVoyage.CONTROLEUR); // Employé 3 comme contrôleur voyage 1
        creerAffectationDemo(2L, 2L, RoleVoyage.CONDUCTEUR); // Employé 2 comme conducteur voyage 2
        creerAffectationDemo(2L, 4L, RoleVoyage.CHEF_DE_BORD); // Employé 4 comme chef de bord voyage 2
        creerAffectationDemo(3L, 3L, RoleVoyage.CONTROLEUR); // Employé 3 comme contrôleur voyage 3
        creerAffectationDemo(3L, 5L, RoleVoyage.TECHNICIEN); // Employé 5 comme technicien voyage 3
    }
    
    private void creerAffectationDemo(Long voyageId, Long employeId, RoleVoyage role) {
        Affectation affectation = new Affectation();
        affectation.setId(idGenerator.getAndIncrement());
        affectation.setVoyageId(voyageId);
        affectation.setEmployeId(employeId);
        affectation.setRole(role);
        affectation.setDateAffectation(LocalDateTime.now().minusDays(new Random().nextInt(7)));
        affectation.setActif(true);
        affectations.put(affectation.getId(), affectation);
    }
    
    @Override
    public Affectation creerAffectation(Affectation affectation) {
        if (affectation.getId() == null) {
            affectation.setId(idGenerator.getAndIncrement());
        }
        affectation.setDateAffectation(LocalDateTime.now());
        affectations.put(affectation.getId(), affectation);
        return affectation;
    }
    
    @Override
    public Optional<Affectation> obtenirAffectationParId(Long id) {
        return Optional.ofNullable(affectations.get(id));
    }
    
    @Override
    public List<Affectation> obtenirToutesLesAffectations() {
        return new ArrayList<>(affectations.values());
    }
    
    @Override
    public List<Affectation> obtenirAffectationsParVoyage(Long voyageId) {
        return affectations.values().stream()
                .filter(a -> Objects.equals(a.getVoyageId(), voyageId))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Affectation> obtenirAffectationsParEmploye(Long employeId) {
        return affectations.values().stream()
                .filter(a -> Objects.equals(a.getEmployeId(), employeId))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Affectation> obtenirAffectationsParRole(RoleVoyage role) {
        return affectations.values().stream()
                .filter(a -> Objects.equals(a.getRole(), role))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Affectation> obtenirAffectationsActivesParEmploye(Long employeId) {
        return affectations.values().stream()
                .filter(a -> Objects.equals(a.getEmployeId(), employeId))
                .filter(Affectation::isActif)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Voyage> obtenirVoyagesAssignes(Long employeId, LocalDate dateDebut, LocalDate dateFin) {
        List<Affectation> affectationsEmploye = obtenirAffectationsActivesParEmploye(employeId);
        List<Voyage> voyages = new ArrayList<>();
        
        for (Affectation affectation : affectationsEmploye) {
            Optional<Voyage> voyage = voyageService.trouverParId(affectation.getVoyageId());
            if (voyage.isPresent()) {
                Voyage v = voyage.get();
                LocalDate dateVoyage = v.getDateVoyage();
                if (dateVoyage != null && 
                    !dateVoyage.isBefore(dateDebut) && 
                    !dateVoyage.isAfter(dateFin)) {
                    voyages.add(v);
                }
            }
        }
        
        return voyages;
    }
    
    @Override
    public Affectation mettreAJourAffectation(Affectation affectation) {
        if (affectation.getId() != null && affectations.containsKey(affectation.getId())) {
            affectation.setDateModification(LocalDateTime.now());
            affectations.put(affectation.getId(), affectation);
            return affectation;
        }
        throw new IllegalArgumentException("Affectation non trouvée");
    }
    
    @Override
    public boolean supprimerAffectation(Long affectationId) {
        return affectations.remove(affectationId) != null;
    }
    
    @Override
    public boolean desactiverAffectation(Long affectationId) {
        Optional<Affectation> optAffectation = obtenirAffectationParId(affectationId);
        if (optAffectation.isPresent()) {
            Affectation affectation = optAffectation.get();
            affectation.setActif(false);
            mettreAJourAffectation(affectation);
            return true;
        }
        return false;
    }
    
    @Override
    public Affectation affecterEmploye(Long voyageId, Long employeId, RoleVoyage role, String commentaire) {
        // Vérifier si l'employé est déjà affecté à ce voyage avec ce rôle
        boolean dejaAffecte = affectations.values().stream()
                .anyMatch(a -> Objects.equals(a.getVoyageId(), voyageId) && 
                              Objects.equals(a.getEmployeId(), employeId) && 
                              Objects.equals(a.getRole(), role) && 
                              a.isActif());
        
        if (dejaAffecte) {
            throw new IllegalStateException("L'employé est déjà affecté à ce voyage avec ce rôle");
        }
        
        Affectation affectation = new Affectation(voyageId, employeId, role);
        affectation.setCommentaire(commentaire);
        return creerAffectation(affectation);
    }
    
    @Override
    public boolean desaffecterEmploye(Long voyageId, Long employeId) {
        List<Affectation> affectationsADesactiver = affectations.values().stream()
                .filter(a -> Objects.equals(a.getVoyageId(), voyageId) && 
                            Objects.equals(a.getEmployeId(), employeId) && 
                            a.isActif())
                .collect(Collectors.toList());
        
        for (Affectation affectation : affectationsADesactiver) {
            affectation.setActif(false);
            mettreAJourAffectation(affectation);
        }
        
        return !affectationsADesactiver.isEmpty();
    }
    
    @Override
    public boolean isEmployeDisponible(Long employeId, Long voyageId) {
        // Logique simplifiée : vérifier qu'il n'est pas déjà affecté à un voyage à la même heure
        return true; // Pour la démonstration
    }
    
    @Override
    public List<Utilisateur> obtenirEmployesDisponibles(Long voyageId) {
        // Retourner tous les employés (non clients) pour la démonstration
        return utilisateurService.obtenirTousLesUtilisateurs().stream()
                .filter(u -> u.getTypeUtilisateur() == TypeUtilisateur.EMPLOYE)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Utilisateur> obtenirEmployesParType(RoleVoyage role) {
        // Pour la démonstration, retourner tous les employés
        return obtenirEmployesDisponibles(null);
    }
    
    @Override
    public boolean isVoyageCompletementAffecte(Long voyageId) {
        List<Affectation> affectationsVoyage = obtenirAffectationsParVoyage(voyageId);
        Set<RoleVoyage> rolesAffecter = affectationsVoyage.stream()
                .filter(Affectation::isActif)
                .map(Affectation::getRole)
                .collect(Collectors.toSet());
        
        // Un voyage est complètement affecté s'il a au moins un conducteur
        return rolesAffecter.contains(RoleVoyage.CONDUCTEUR);
    }
    
    @Override
    public List<RoleVoyage> obtenirRolesManquants(Long voyageId) {
        List<Affectation> affectationsVoyage = obtenirAffectationsParVoyage(voyageId);
        Set<RoleVoyage> rolesAffecter = affectationsVoyage.stream()
                .filter(Affectation::isActif)
                .map(Affectation::getRole)
                .collect(Collectors.toSet());
        
        List<RoleVoyage> rolesManquants = new ArrayList<>();
        
        // Rôles obligatoires
        if (!rolesAffecter.contains(RoleVoyage.CONDUCTEUR)) {
            rolesManquants.add(RoleVoyage.CONDUCTEUR);
        }
        
        return rolesManquants;
    }
    
    @Override
    public StatistiquesAffectation obtenirStatistiques() {
        StatistiquesAffectation stats = new StatistiquesAffectation();
        
        stats.setTotalAffectations(affectations.size());
        stats.setAffectationsActives(affectations.values().stream()
                .filter(Affectation::isActif)
                .count());
        
        // Compter les voyages complètement affectés
        Set<Long> voyagesIds = affectations.values().stream()
                .map(Affectation::getVoyageId)
                .collect(Collectors.toSet());
        
        long voyagesComplets = voyagesIds.stream()
                .filter(this::isVoyageCompletementAffecte)
                .count();
        
        stats.setVoyagesCompletementAffecter(voyagesComplets);
        stats.setVoyagesPartiellementAffecter(voyagesIds.size() - voyagesComplets);
        
        // Compter les employés affectés
        stats.setEmployesAffecter(affectations.values().stream()
                .filter(Affectation::isActif)
                .map(Affectation::getEmployeId)
                .collect(Collectors.toSet())
                .size());
        
        // Compter par rôle
        stats.setConducteurs(obtenirAffectationsParRole(RoleVoyage.CONDUCTEUR).stream()
                .filter(Affectation::isActif)
                .count());
        
        stats.setControleurs(obtenirAffectationsParRole(RoleVoyage.CONTROLEUR).stream()
                .filter(Affectation::isActif)
                .count());
        
        stats.setChefsDebord(obtenirAffectationsParRole(RoleVoyage.CHEF_DE_BORD).stream()
                .filter(Affectation::isActif)
                .count());
        
        return stats;
    }
}
