package com.train.servlet;

import com.train.model.*;
import com.train.service.VoyageService;
import com.train.service.impl.DemoVoyageServiceImpl;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Servlet pour la gestion de l'espace employé
 */
@WebServlet("/employee/*")
public class EmployeeServlet extends HttpServlet {

    private VoyageService voyageService = new DemoVoyageServiceImpl();
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Vérifier l'authentification
        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }

        Utilisateur user = (Utilisateur) session.getAttribute("user");

        // Vérifier que l'utilisateur est un employé
        if (user.getTypeUtilisateur() != TypeUtilisateur.EMPLOYE) {
            response.sendError(HttpServletResponse.SC_FORBIDDEN, "Accès réservé aux employés");
            return;
        }

        String pathInfo = request.getPathInfo();
        if (pathInfo == null) {
            pathInfo = "/";
        }

        try {
            switch (pathInfo) {
                case "/":
                case "/dashboard":
                    afficherDashboard(request, response, user);
                    break;
                case "/voyages":
                    afficherMesVoyages(request, response, user);
                    break;
                case "/incidents":
                    afficherIncidents(request, response, user);
                    break;
                case "/rapport-incident":
                    afficherFormulaireIncident(request, response, user);
                    break;
                default:
                    response.sendError(HttpServletResponse.SC_NOT_FOUND);
            }
        } catch (Exception e) {
            e.printStackTrace();
            request.setAttribute("errorMessage", "Une erreur est survenue : " + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/error.jsp").forward(request, response);
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        doGet(request, response);
    }

    /**
     * Afficher le dashboard employé
     */
    private void afficherDashboard(HttpServletRequest request, HttpServletResponse response, Utilisateur employe)
            throws ServletException, IOException {

        try {
            System.out.println("=== Dashboard Employé ===");
            System.out.println("Employé: " + employe.getPrenom() + " " + employe.getNom());

            // Récupérer tous les voyages
            List<Voyage> tousLesVoyages = voyageService.obtenirTousLesVoyages();

            // Simuler les affectations de l'employé (pour la démo)
            List<AffectationEmploye> mesAffectations = simulerAffectationsEmploye(employe.getId(), tousLesVoyages);

            // Filtrer les voyages d'aujourd'hui et futurs
            List<AffectationEmploye> voyagesAujourdhui = mesAffectations.stream()
                .filter(a -> a.getVoyage().getDateVoyage().equals(LocalDate.now()))
                .collect(Collectors.toList());

            List<AffectationEmploye> prochainsVoyages = mesAffectations.stream()
                .filter(a -> a.getVoyage().getDateVoyage().isAfter(LocalDate.now()))
                .limit(5)
                .collect(Collectors.toList());

            // Simuler quelques incidents récents
            List<RapportIncident> incidentsRecents = simulerIncidentsRecents(employe.getId());

            // Calculer les statistiques
            int totalAffectations = mesAffectations.size();
            int voyagesTermines = (int) mesAffectations.stream()
                .filter(a -> a.getStatut() == AffectationEmploye.StatutAffectation.TERMINE)
                .count();
            int incidentsOuverts = (int) incidentsRecents.stream()
                .filter(RapportIncident::isOuvert)
                .count();

            // Préparer les données pour la JSP
            request.setAttribute("employe", employe);
            request.setAttribute("mesAffectations", mesAffectations);
            request.setAttribute("voyagesAujourdhui", voyagesAujourdhui);
            request.setAttribute("prochainsVoyages", prochainsVoyages);
            request.setAttribute("incidentsRecents", incidentsRecents);

            // Statistiques
            request.setAttribute("totalAffectations", totalAffectations);
            request.setAttribute("voyagesTermines", voyagesTermines);
            request.setAttribute("incidentsOuverts", incidentsOuverts);

            System.out.println("Affectations: " + totalAffectations);
            System.out.println("Voyages aujourd'hui: " + voyagesAujourdhui.size());
            System.out.println("Prochains voyages: " + prochainsVoyages.size());

            // Afficher le dashboard employé
            request.getRequestDispatcher("/WEB-INF/views/employee/dashboard.jsp").forward(request, response);

        } catch (Exception e) {
            System.out.println("=== ERREUR Dashboard Employé ===");
            e.printStackTrace();
            request.setAttribute("errorMessage", "Erreur lors du chargement du dashboard : " + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/error.jsp").forward(request, response);
        }
    }
    
    private void afficherMesVoyages(HttpServletRequest request, HttpServletResponse response, Utilisateur employe)
            throws ServletException, IOException {

        List<Voyage> tousLesVoyages = voyageService.obtenirTousLesVoyages();
        List<AffectationEmploye> mesAffectations = simulerAffectationsEmploye(employe.getId(), tousLesVoyages);

        request.setAttribute("employe", employe);
        request.setAttribute("mesAffectations", mesAffectations);
        request.getRequestDispatcher("/WEB-INF/views/employee/voyages.jsp").forward(request, response);
    }

    private void afficherIncidents(HttpServletRequest request, HttpServletResponse response, Utilisateur employe)
            throws ServletException, IOException {

        List<RapportIncident> mesIncidents = simulerIncidentsRecents(employe.getId());

        request.setAttribute("employe", employe);
        request.setAttribute("mesIncidents", mesIncidents);
        request.getRequestDispatcher("/WEB-INF/views/employee/incidents.jsp").forward(request, response);
    }

    private void afficherFormulaireIncident(HttpServletRequest request, HttpServletResponse response, Utilisateur employe)
            throws ServletException, IOException {

        List<Voyage> tousLesVoyages = voyageService.obtenirTousLesVoyages();
        List<AffectationEmploye> mesAffectations = simulerAffectationsEmploye(employe.getId(), tousLesVoyages);

        request.setAttribute("employe", employe);
        request.setAttribute("mesVoyages", mesAffectations);
        request.getRequestDispatcher("/WEB-INF/views/employee/rapport-incident.jsp").forward(request, response);
    }
    
    // Méthodes de simulation pour la démo
    private List<AffectationEmploye> simulerAffectationsEmploye(Long employeId, List<Voyage> voyages) {
        List<AffectationEmploye> affectations = new ArrayList<>();

        // Simuler quelques affectations
        for (int i = 0; i < Math.min(voyages.size(), 8); i++) {
            Voyage voyage = voyages.get(i);
            AffectationEmploye affectation = new AffectationEmploye();
            affectation.setId((long) (i + 1));
            affectation.setEmployeId(employeId);
            affectation.setVoyageId(voyage.getId());
            affectation.setVoyage(voyage);
            affectation.setDateAffectation(LocalDateTime.now().minusDays(i));

            // Alterner les rôles
            AffectationEmploye.RoleVoyage[] roles = AffectationEmploye.RoleVoyage.values();
            affectation.setRoleVoyage(roles[i % roles.length]);

            // Définir le statut selon la date
            if (voyage.getDateVoyage().isBefore(LocalDate.now())) {
                affectation.setStatut(AffectationEmploye.StatutAffectation.TERMINE);
            } else if (voyage.getDateVoyage().equals(LocalDate.now())) {
                affectation.setStatut(AffectationEmploye.StatutAffectation.CONFIRME);
            } else {
                affectation.setStatut(AffectationEmploye.StatutAffectation.AFFECTE);
            }

            affectations.add(affectation);
        }

        return affectations;
    }

    private List<RapportIncident> simulerIncidentsRecents(Long employeId) {
        List<RapportIncident> incidents = new ArrayList<>();

        // Simuler quelques incidents
        RapportIncident incident1 = new RapportIncident();
        incident1.setId(1L);
        incident1.setEmployeId(employeId);
        incident1.setTypeIncident(RapportIncident.TypeIncident.RETARD);
        incident1.setTitre("Retard de 15 minutes sur Paris-Lyon");
        incident1.setDescription("Problème technique sur la voie, retard de 15 minutes");
        incident1.setGravite(RapportIncident.GraviteIncident.MOYENNE);
        incident1.setStatut(RapportIncident.StatutIncident.RESOLU);
        incident1.setDateIncident(LocalDateTime.now().minusDays(2));
        incident1.setDateRapport(LocalDateTime.now().minusDays(2));
        incidents.add(incident1);

        RapportIncident incident2 = new RapportIncident();
        incident2.setId(2L);
        incident2.setEmployeId(employeId);
        incident2.setTypeIncident(RapportIncident.TypeIncident.SECURITE);
        incident2.setTitre("Passager sans billet");
        incident2.setDescription("Contrôle de routine, passager sans titre de transport");
        incident2.setGravite(RapportIncident.GraviteIncident.FAIBLE);
        incident2.setStatut(RapportIncident.StatutIncident.FERME);
        incident2.setDateIncident(LocalDateTime.now().minusDays(5));
        incident2.setDateRapport(LocalDateTime.now().minusDays(5));
        incidents.add(incident2);

        RapportIncident incident3 = new RapportIncident();
        incident3.setId(3L);
        incident3.setEmployeId(employeId);
        incident3.setTypeIncident(RapportIncident.TypeIncident.PANNE);
        incident3.setTitre("Problème de climatisation");
        incident3.setDescription("Climatisation défaillante en voiture 3");
        incident3.setGravite(RapportIncident.GraviteIncident.MOYENNE);
        incident3.setStatut(RapportIncident.StatutIncident.EN_COURS);
        incident3.setDateIncident(LocalDateTime.now().minusHours(6));
        incident3.setDateRapport(LocalDateTime.now().minusHours(6));
        incidents.add(incident3);

        return incidents;
    }
}
