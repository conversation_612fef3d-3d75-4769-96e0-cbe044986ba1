package com.train.servlet;

import com.train.model.*;
import com.train.service.*;
import com.train.service.impl.*;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

/**
 * Servlet pour la gestion de l'espace employé
 */
@WebServlet("/employee/*")
public class EmployeeServlet extends HttpServlet {
    
    private AffectationService affectationService;
    private VoyageService voyageService;
    private UtilisateurService utilisateurService;
    
    @Override
    public void init() throws ServletException {
        super.init();
        // Initialiser les services
        utilisateurService = new DemoUtilisateurServiceImpl();
        voyageService = new VoyageServiceImpl();
        affectationService = new AffectationServiceDBImpl(utilisateurService, voyageService);
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // Vérifier l'authentification
        HttpSession session = request.getSession(false);
        if (session == null || session.getAttribute("user") == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }
        
        Utilisateur user = (Utilisateur) session.getAttribute("user");
        
        // Vérifier que l'utilisateur est un employé
        if (user.getTypeUtilisateur() != TypeUtilisateur.EMPLOYE) {
            response.sendError(HttpServletResponse.SC_FORBIDDEN, "Accès réservé aux employés");
            return;
        }
        
        String pathInfo = request.getPathInfo();
        if (pathInfo == null) {
            pathInfo = "/";
        }
        
        try {
            if (pathInfo.equals("/") || pathInfo.equals("")) {
                afficherDashboard(request, response, user);
            } else if (pathInfo.startsWith("/voyages")) {
                gererVoyages(request, response, pathInfo, user);
            } else if (pathInfo.startsWith("/planning")) {
                afficherPlanning(request, response, user);
            } else if (pathInfo.startsWith("/profile")) {
                afficherProfil(request, response, user);
            } else {
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
            }
        } catch (Exception e) {
            e.printStackTrace();
            request.setAttribute("errorMessage", "Une erreur est survenue : " + e.getMessage());
            request.getRequestDispatcher("/WEB-INF/views/error.jsp").forward(request, response);
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        doGet(request, response);
    }
    
    /**
     * Afficher le dashboard employé
     */
    private void afficherDashboard(HttpServletRequest request, HttpServletResponse response, Utilisateur user) 
            throws ServletException, IOException {
        
        // Obtenir les affectations de l'employé
        List<Affectation> affectations = affectationService.obtenirAffectationsActivesParEmploye(user.getId());
        
        // Obtenir les voyages assignés pour les 30 prochains jours
        LocalDate aujourdhui = LocalDate.now();
        LocalDate dans30Jours = aujourdhui.plusDays(30);
        List<Voyage> voyagesAssignes = affectationService.obtenirVoyagesAssignes(
            user.getId(), aujourdhui, dans30Jours);
        
        // Calculer les statistiques
        StatistiquesEmploye stats = calculerStatistiques(user.getId(), voyagesAssignes);
        
        // Obtenir les prochains voyages (5 maximum)
        List<Voyage> prochainsVoyages = voyagesAssignes.stream()
                .filter(v -> v.getDateVoyage().isAfter(aujourdhui.minusDays(1)))
                .sorted((v1, v2) -> v1.getDateVoyage().compareTo(v2.getDateVoyage()))
                .limit(5)
                .collect(java.util.stream.Collectors.toList());
        
        // Voyage d'aujourd'hui
        Voyage voyageAujourdhui = voyagesAssignes.stream()
                .filter(v -> v.getDateVoyage().equals(aujourdhui))
                .findFirst()
                .orElse(null);
        
        request.setAttribute("statistiques", stats);
        request.setAttribute("prochainsVoyages", prochainsVoyages);
        request.setAttribute("voyageAujourdhui", voyageAujourdhui);
        
        request.getRequestDispatcher("/WEB-INF/views/employee/dashboard.jsp")
               .forward(request, response);
    }
    
    /**
     * Gérer les voyages de l'employé
     */
    private void gererVoyages(HttpServletRequest request, HttpServletResponse response, 
                             String pathInfo, Utilisateur user) 
            throws ServletException, IOException {
        
        if (pathInfo.equals("/voyages") || pathInfo.equals("/voyages/")) {
            // Afficher tous les voyages de l'employé
            LocalDate debut = LocalDate.now().minusMonths(1);
            LocalDate fin = LocalDate.now().plusMonths(3);
            
            List<Voyage> voyages = affectationService.obtenirVoyagesAssignes(user.getId(), debut, fin);
            
            request.setAttribute("voyages", voyages);
            request.getRequestDispatcher("/WEB-INF/views/employee/voyages.jsp")
                   .forward(request, response);
        } else {
            // Détails d'un voyage spécifique
            String[] parts = pathInfo.split("/");
            if (parts.length >= 3) {
                try {
                    Long voyageId = Long.parseLong(parts[2]);
                    // Vérifier que l'employé est assigné à ce voyage
                    List<Affectation> affectations = affectationService.obtenirAffectationsParVoyage(voyageId);
                    boolean estAssigne = affectations.stream()
                            .anyMatch(a -> a.getEmployeId().equals(user.getId()) && a.isActif());
                    
                    if (estAssigne) {
                        Voyage voyage = voyageService.trouverParId(voyageId).orElse(null);
                        if (voyage != null) {
                            request.setAttribute("voyage", voyage);
                            request.getRequestDispatcher("/WEB-INF/views/employee/voyage-details.jsp")
                                   .forward(request, response);
                            return;
                        }
                    }
                } catch (NumberFormatException e) {
                    // ID invalide
                }
            }
            response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    /**
     * Afficher le planning de l'employé
     */
    private void afficherPlanning(HttpServletRequest request, HttpServletResponse response, Utilisateur user) 
            throws ServletException, IOException {
        
        // Obtenir le planning pour les 4 prochaines semaines
        LocalDate debut = LocalDate.now();
        LocalDate fin = debut.plusWeeks(4);
        
        List<Voyage> voyages = affectationService.obtenirVoyagesAssignes(user.getId(), debut, fin);
        
        request.setAttribute("voyages", voyages);
        request.setAttribute("dateDebut", debut);
        request.setAttribute("dateFin", fin);
        
        request.getRequestDispatcher("/WEB-INF/views/employee/planning.jsp")
               .forward(request, response);
    }
    
    /**
     * Afficher le profil de l'employé
     */
    private void afficherProfil(HttpServletRequest request, HttpServletResponse response, Utilisateur user) 
            throws ServletException, IOException {
        
        request.setAttribute("utilisateur", user);
        request.getRequestDispatcher("/WEB-INF/views/employee/profile.jsp")
               .forward(request, response);
    }
    
    /**
     * Calculer les statistiques pour un employé
     */
    private StatistiquesEmploye calculerStatistiques(Long employeId, List<Voyage> voyages) {
        StatistiquesEmploye stats = new StatistiquesEmploye();
        
        LocalDate aujourdhui = LocalDate.now();
        
        stats.setVoyagesTotal(voyages.size());
        
        stats.setVoyagesAujourdhui((int) voyages.stream()
                .filter(v -> v.getDateVoyage().equals(aujourdhui))
                .count());
        
        stats.setVoyagesProchains((int) voyages.stream()
                .filter(v -> v.getDateVoyage().isAfter(aujourdhui) && 
                            v.getDateVoyage().isBefore(aujourdhui.plusDays(8)))
                .count());
        
        stats.setVoyagesTermines((int) voyages.stream()
                .filter(v -> v.getDateVoyage().isBefore(aujourdhui) && 
                            v.getDateVoyage().isAfter(aujourdhui.minusMonths(1)))
                .count());
        
        return stats;
    }
    
    /**
     * Classe pour les statistiques employé
     */
    public static class StatistiquesEmploye {
        private int voyagesTotal;
        private int voyagesAujourdhui;
        private int voyagesProchains;
        private int voyagesTermines;
        
        // Getters et Setters
        public int getVoyagesTotal() { return voyagesTotal; }
        public void setVoyagesTotal(int voyagesTotal) { this.voyagesTotal = voyagesTotal; }
        
        public int getVoyagesAujourdhui() { return voyagesAujourdhui; }
        public void setVoyagesAujourdhui(int voyagesAujourdhui) { this.voyagesAujourdhui = voyagesAujourdhui; }
        
        public int getVoyagesProchains() { return voyagesProchains; }
        public void setVoyagesProchains(int voyagesProchains) { this.voyagesProchains = voyagesProchains; }
        
        public int getVoyagesTermines() { return voyagesTermines; }
        public void setVoyagesTermines(int voyagesTermines) { this.voyagesTermines = voyagesTermines; }
    }
}
