package com.train.service.impl;

import com.train.dao.AffectationDAO;
import com.train.dao.impl.AffectationDAOImpl;
import com.train.model.*;
import com.train.service.AffectationService;
import com.train.service.UtilisateurService;
import com.train.service.VoyageService;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Implémentation du service de gestion des affectations utilisant la base de données
 */
public class AffectationServiceDBImpl implements AffectationService {
    
    private final AffectationDAO affectationDAO;
    private final UtilisateurService utilisateurService;
    private final VoyageService voyageService;
    
    public AffectationServiceDBImpl(UtilisateurService utilisateurService, VoyageService voyageService) {
        this.affectationDAO = new AffectationDAOImpl();
        this.utilisateurService = utilisateurService;
        this.voyageService = voyageService;
    }
    
    @Override
    public Affectation creerAffectation(Affectation affectation) {
        try {
            if (affectation.getDateAffectation() == null) {
                affectation.setDateAffectation(LocalDateTime.now());
            }
            return affectationDAO.creer(affectation);
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la création de l'affectation", e);
        }
    }
    
    @Override
    public Optional<Affectation> obtenirAffectationParId(Long id) {
        try {
            return affectationDAO.obtenirParId(id);
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la récupération de l'affectation", e);
        }
    }
    
    @Override
    public List<Affectation> obtenirToutesLesAffectations() {
        try {
            List<Affectation> affectations = affectationDAO.obtenirTous();
            // Enrichir avec les données des employés et voyages
            enrichirAffectations(affectations);
            return affectations;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la récupération des affectations", e);
        }
    }
    
    @Override
    public List<Affectation> obtenirAffectationsParVoyage(Long voyageId) {
        try {
            List<Affectation> affectations = affectationDAO.obtenirParVoyage(voyageId);
            enrichirAffectations(affectations);
            return affectations;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la récupération des affectations par voyage", e);
        }
    }
    
    @Override
    public List<Affectation> obtenirAffectationsParEmploye(Long employeId) {
        try {
            List<Affectation> affectations = affectationDAO.obtenirParEmploye(employeId);
            enrichirAffectations(affectations);
            return affectations;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la récupération des affectations par employé", e);
        }
    }
    
    @Override
    public List<Affectation> obtenirAffectationsParRole(RoleVoyage role) {
        try {
            List<Affectation> affectations = affectationDAO.obtenirParRole(role);
            enrichirAffectations(affectations);
            return affectations;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la récupération des affectations par rôle", e);
        }
    }
    
    @Override
    public List<Affectation> obtenirAffectationsActivesParEmploye(Long employeId) {
        try {
            List<Affectation> affectations = affectationDAO.obtenirActivesParEmploye(employeId);
            enrichirAffectations(affectations);
            return affectations;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la récupération des affectations actives", e);
        }
    }
    
    @Override
    public List<Voyage> obtenirVoyagesAssignes(Long employeId, LocalDate dateDebut, LocalDate dateFin) {
        try {
            List<Long> voyageIds = affectationDAO.obtenirVoyagesAssignes(employeId, dateDebut, dateFin);
            List<Voyage> voyages = new ArrayList<>();
            
            for (Long voyageId : voyageIds) {
                Optional<Voyage> voyage = voyageService.trouverParId(voyageId);
                voyage.ifPresent(voyages::add);
            }
            
            return voyages;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la récupération des voyages assignés", e);
        }
    }
    
    @Override
    public Affectation mettreAJourAffectation(Affectation affectation) {
        try {
            return affectationDAO.mettreAJour(affectation);
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la mise à jour de l'affectation", e);
        }
    }
    
    @Override
    public boolean supprimerAffectation(Long affectationId) {
        try {
            return affectationDAO.supprimer(affectationId);
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la suppression de l'affectation", e);
        }
    }
    
    @Override
    public boolean desactiverAffectation(Long affectationId) {
        try {
            return affectationDAO.desactiver(affectationId);
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la désactivation de l'affectation", e);
        }
    }
    
    @Override
    public Affectation affecterEmploye(Long voyageId, Long employeId, RoleVoyage role, String commentaire) {
        try {
            // Vérifier si l'affectation existe déjà
            if (affectationDAO.existeAffectation(voyageId, employeId, role)) {
                throw new IllegalStateException("L'employé est déjà affecté à ce voyage avec ce rôle");
            }
            
            Affectation affectation = new Affectation(voyageId, employeId, role);
            affectation.setCommentaire(commentaire);
            return creerAffectation(affectation);
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de l'affectation de l'employé", e);
        }
    }
    
    @Override
    public boolean desaffecterEmploye(Long voyageId, Long employeId) {
        try {
            List<Affectation> affectations = affectationDAO.obtenirParVoyage(voyageId);
            boolean success = false;
            
            for (Affectation affectation : affectations) {
                if (affectation.getEmployeId().equals(employeId) && affectation.isActif()) {
                    affectation.setActif(false);
                    mettreAJourAffectation(affectation);
                    success = true;
                }
            }
            
            return success;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la désaffectation de l'employé", e);
        }
    }
    
    @Override
    public boolean isEmployeDisponible(Long employeId, Long voyageId) {
        // Logique simplifiée pour la démonstration
        // Dans une vraie application, on vérifierait les conflits d'horaires
        return true;
    }
    
    @Override
    public List<Utilisateur> obtenirEmployesDisponibles(Long voyageId) {
        // Retourner tous les employés
        return utilisateurService.obtenirTousLesUtilisateurs().stream()
                .filter(u -> u.getTypeUtilisateur() == TypeUtilisateur.EMPLOYE)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Utilisateur> obtenirEmployesParType(RoleVoyage role) {
        // Pour la démonstration, retourner tous les employés
        return obtenirEmployesDisponibles(null);
    }
    
    @Override
    public boolean isVoyageCompletementAffecte(Long voyageId) {
        try {
            List<Affectation> affectations = affectationDAO.obtenirParVoyage(voyageId);
            Set<RoleVoyage> rolesAffecter = affectations.stream()
                    .filter(Affectation::isActif)
                    .map(Affectation::getRole)
                    .collect(Collectors.toSet());
            
            // Un voyage est complètement affecté s'il a au moins un conducteur
            return rolesAffecter.contains(RoleVoyage.CONDUCTEUR);
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la vérification de l'affectation du voyage", e);
        }
    }
    
    @Override
    public List<RoleVoyage> obtenirRolesManquants(Long voyageId) {
        try {
            List<Affectation> affectations = affectationDAO.obtenirParVoyage(voyageId);
            Set<RoleVoyage> rolesAffecter = affectations.stream()
                    .filter(Affectation::isActif)
                    .map(Affectation::getRole)
                    .collect(Collectors.toSet());
            
            List<RoleVoyage> rolesManquants = new ArrayList<>();
            
            // Rôles obligatoires
            if (!rolesAffecter.contains(RoleVoyage.CONDUCTEUR)) {
                rolesManquants.add(RoleVoyage.CONDUCTEUR);
            }
            
            return rolesManquants;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors de la récupération des rôles manquants", e);
        }
    }
    

    
    @Override
    public StatistiquesAffectation obtenirStatistiques() {
        try {
            StatistiquesAffectation stats = new StatistiquesAffectation();
            
            // Compter les affectations
            stats.setTotalAffectations(affectationDAO.obtenirTous().size());
            stats.setAffectationsActives(affectationDAO.compterParStatut(true));
            
            // Compter par rôle
            stats.setConducteurs(affectationDAO.compterParRole(RoleVoyage.CONDUCTEUR));
            stats.setControleurs(affectationDAO.compterParRole(RoleVoyage.CONTROLEUR));
            stats.setChefsDebord(affectationDAO.compterParRole(RoleVoyage.CHEF_DE_BORD));
            
            // Compter les voyages
            List<Voyage> tousLesVoyages = voyageService.obtenirTousLesVoyages();
            long voyagesComplets = tousLesVoyages.stream()
                    .filter(v -> isVoyageCompletementAffecte(v.getId()))
                    .count();
            
            stats.setVoyagesCompletementAffecter(voyagesComplets);
            stats.setVoyagesPartiellementAffecter(tousLesVoyages.size() - voyagesComplets);
            
            // Compter les employés affectés
            Set<Long> employesAffecter = affectationDAO.obtenirTous().stream()
                    .filter(Affectation::isActif)
                    .map(Affectation::getEmployeId)
                    .collect(Collectors.toSet());
            stats.setEmployesAffecter(employesAffecter.size());
            
            return stats;
        } catch (SQLException e) {
            throw new RuntimeException("Erreur lors du calcul des statistiques", e);
        }
    }
    
    /**
     * Enrichir les affectations avec les données des employés et voyages
     */
    private void enrichirAffectations(List<Affectation> affectations) {
        for (Affectation affectation : affectations) {
            // Charger l'employé
            Optional<Utilisateur> employe = utilisateurService.trouverParId(affectation.getEmployeId());
            employe.ifPresent(affectation::setEmploye);
            
            // Charger le voyage
            Optional<Voyage> voyage = voyageService.trouverParId(affectation.getVoyageId());
            voyage.ifPresent(affectation::setVoyage);
        }
    }
}
