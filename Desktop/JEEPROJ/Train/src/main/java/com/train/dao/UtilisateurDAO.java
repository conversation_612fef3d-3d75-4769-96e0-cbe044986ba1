package com.train.dao;

import com.train.model.Utilisateur;
import com.train.model.TypeUtilisateur;
import java.util.List;
import java.util.Optional;

/**
 * Interface DAO pour les opérations sur les utilisateurs
 */
public interface UtilisateurDAO extends BaseDAO<Utilisateur, Long> {
    
    /**
     * Trouve un utilisateur par son email
     * @param email l'email de l'utilisateur
     * @return Optional contenant l'utilisateur si trouvé
     */
    Optional<Utilisateur> findByEmail(String email);
    
    /**
     * Vérifie si un email existe déjà
     * @param email l'email à vérifier
     * @return true si l'email existe, false sinon
     */
    boolean existsByEmail(String email);
    
    /**
     * Trouve les utilisateurs par type
     * @param type le type d'utilisateur
     * @return liste des utilisateurs du type spécifié
     */
    List<Utilisateur> findByType(TypeUtilisateur type);
    
    /**
     * Trouve les utilisateurs actifs
     * @return liste des utilisateurs actifs
     */
    List<Utilisateur> findActiveUsers();
    
    /**
     * Authentifie un utilisateur
     * @param email l'email de l'utilisateur
     * @param motDePasse le mot de passe
     * @return Optional contenant l'utilisateur si l'authentification réussit
     */
    Optional<Utilisateur> authenticate(String email, String motDePasse);
    
    /**
     * Met à jour la dernière connexion d'un utilisateur
     * @param userId l'ID de l'utilisateur
     */
    void updateLastLogin(Long userId);
    
    /**
     * Change le mot de passe d'un utilisateur
     * @param userId l'ID de l'utilisateur
     * @param nouveauMotDePasse le nouveau mot de passe (déjà hashé)
     * @return true si le changement a réussi
     */
    boolean changePassword(Long userId, String nouveauMotDePasse);
}
