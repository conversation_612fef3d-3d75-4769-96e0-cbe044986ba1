package com.train.test;

import com.train.util.DatabaseConnection;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * Test simple de connexion à la base de données
 */
public class DatabaseConnectionTest {
    
    public static void main(String[] args) {
        System.out.println("=== Test de Connexion à la Base de Données ===");
        
        try {
            System.out.println("Tentative de connexion...");
            Connection conn = DatabaseConnection.getConnection();
            
            if (conn != null && !conn.isClosed()) {
                System.out.println("✅ Connexion réussie !");
                System.out.println("URL: " + conn.getMetaData().getURL());
                System.out.println("Driver: " + conn.getMetaData().getDriverName());
                System.out.println("Version: " + conn.getMetaData().getDriverVersion());
                
                DatabaseConnection.closeConnection(conn);
                System.out.println("✅ Connexion fermée proprement");
            } else {
                System.out.println("❌ Échec de la connexion");
            }
            
        } catch (SQLException e) {
            System.out.println("❌ Erreur de connexion: " + e.getMessage());
            System.out.println("Vérifiez que:");
            System.out.println("1. MySQL est démarré");
            System.out.println("2. La base 'train' existe");
            System.out.println("3. Les paramètres dans database.properties sont corrects");
        } catch (Exception e) {
            System.out.println("❌ Erreur inattendue: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
