# 🧪 Test du Formulaire de Gare - Enregistrement en Base de Données

## ⚠️ Problème identifié
Le formulaire ne sauvegarde pas en base de données car **la table `gares` n'existe pas encore**.

## 🔧 Solution : Créer la table gares

### Étape 1 : Exécuter le script SQL
1. **Ouvrez phpMyAdmin** : `http://localhost/phpmyadmin`
2. **Sélectionnez la base `train`** (ou créez-la si elle n'existe pas)
3. **Cliquez sur l'onglet "SQL"**
4. **Copiez-collez ce script** :

```sql
-- Utiliser la base de données train
USE train;

-- Créer la table gares
CREATE TABLE IF NOT EXISTS gares (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    ville VARCHAR(100) NOT NULL,
    code_gare VARCHAR(10) NOT NULL UNIQUE,
    adresse TEXT,
    code_postal VARCHAR(10),
    latitude DECIMAL(10, 6),
    longitude DECIMAL(10, 6),
    active BOOLEAN DEFAULT TRUE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_code_gare (code_gare),
    INDEX idx_ville (ville),
    INDEX idx_active (active)
);

-- Insérer quelques données de test
INSERT INTO gares (nom, ville, code_gare, adresse, code_postal, latitude, longitude, active) VALUES
('Gare de Paris-Nord', 'Paris', 'PARN', '18 Rue de Dunkerque', '75010', 48.8809, 2.3553, TRUE),
('Gare de Lyon-Part-Dieu', 'Lyon', 'LYONPD', 'Place Charles Béraudier', '69003', 45.7606, 4.8598, TRUE),
('Gare de Marseille-Saint-Charles', 'Marseille', 'MARSC', 'Square Narvik', '13001', 43.3030, 5.3808, TRUE);

-- Vérifier les données
SELECT * FROM gares;
```

5. **Cliquez sur "Exécuter"**

### Étape 2 : Tester le formulaire
1. **Accédez à l'application** : `http://localhost:8080/Train`
2. **Connectez-vous** : `<EMAIL>` / `password`
3. **Allez dans Gares → Nouvelle Gare**
4. **Remplissez le formulaire** :
   - **Nom** : `Gare de Test`
   - **Ville** : `TestVille`
   - **Code gare** : `TEST01`
   - **Adresse** : `123 Rue de la Gare`
   - **Code postal** : `12345`
   - **Latitude** : `48.8566`
   - **Longitude** : `2.3522`
5. **Cliquez sur "Créer"**

### Étape 3 : Vérifier l'enregistrement
1. **Retournez dans phpMyAdmin**
2. **Actualisez la table `gares`**
3. **Votre nouvelle gare devrait apparaître !**

## 🎯 Résultat attendu

Après avoir créé la table :
- ✅ **3 gares de test** visibles dans la liste
- ✅ **Formulaire fonctionnel** pour créer de nouvelles gares
- ✅ **Données sauvegardées** en base de données MySQL
- ✅ **Messages de succès** affichés

## 🔍 Diagnostic des erreurs

### Si le formulaire ne fonctionne toujours pas :

1. **Vérifiez les logs du serveur** dans le terminal
2. **Vérifiez la connexion à la base** :
   - Base de données : `train`
   - Utilisateur : `root`
   - Mot de passe : (vide)
   - Port : `3306`

3. **Vérifiez que MySQL est démarré** :
   - XAMPP : Démarrez MySQL
   - WAMP : Démarrez MySQL
   - MAMP : Démarrez MySQL

### Messages d'erreur courants :

- **"Table 'train.gares' doesn't exist"** → Exécutez le script SQL
- **"Access denied"** → Vérifiez les identifiants MySQL
- **"Connection refused"** → Démarrez MySQL

## 📊 Structure de la table créée

| Colonne | Type | Description |
|---------|------|-------------|
| `id` | BIGINT AUTO_INCREMENT | Identifiant unique |
| `nom` | VARCHAR(100) | Nom de la gare |
| `ville` | VARCHAR(100) | Ville de la gare |
| `code_gare` | VARCHAR(10) UNIQUE | Code unique de la gare |
| `adresse` | TEXT | Adresse complète |
| `code_postal` | VARCHAR(10) | Code postal |
| `latitude` | DECIMAL(10,6) | Coordonnée GPS latitude |
| `longitude` | DECIMAL(10,6) | Coordonnée GPS longitude |
| `active` | BOOLEAN | Statut actif/inactif |
| `date_creation` | TIMESTAMP | Date de création |
| `date_modification` | TIMESTAMP | Date de modification |

## 🎉 Une fois la table créée

Le formulaire sera **entièrement fonctionnel** :
- ✅ Création de nouvelles gares
- ✅ Modification de gares existantes
- ✅ Validation des données
- ✅ Sauvegarde en base MySQL
- ✅ Messages de succès/erreur

---

**Exécutez le script SQL et testez le formulaire ! 🚀**
