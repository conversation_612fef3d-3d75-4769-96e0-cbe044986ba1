# Système de Gestion des Trains

## Description
Application web JEE pour la gestion des réservations de billets de train. Le système permet aux clients de rechercher des trajets, effectuer des réservations, et aux administrateurs de gérer les trains, gares, et voyages.

## Fonctionnalités

### Espace Client
- ✅ Recherche de trajets par ville de départ/arrivée et date
- ✅ Consultation des horaires et prix
- ✅ Réservation de billets
- ✅ Gestion du profil utilisateur
- ✅ Historique des réservations
- ✅ Annulation de réservations

### Espace Administrateur
- ✅ Gestion des trajets (ajout, suppression, modification)
- ✅ Gestion des gares
- ✅ Gestion des voyages (programmation)
- ✅ Gestion des places disponibles
- ✅ Gestion des paiements
- ✅ Traitement des demandes d'annulation et remboursements

### Fonctionnalités Optionnelles
- ✅ Gestion avancée des trajets
- ✅ Système de promotions
- ✅ Sélection des sièges
- ✅ Réductions pour voyages en famille
- ✅ Gestion des employés
- ✅ Notifications et alertes
- ✅ Statistiques et rapports

## Technologies Utilisées

### Backend
- **Java 11**
- **JEE (Servlet/JSP)**
- **JSTL/EL**
- **MySQL 8.0**
- **Maven**

### Frontend
- **JSP**
- **Bootstrap 5**
- **Font Awesome**
- **JavaScript**

### Sécurité
- **BCrypt** pour le hachage des mots de passe
- **Filtres de sécurité**
- **Validation des données**

## Structure du Projet

```
src/
├── main/
│   ├── java/
│   │   └── com/train/
│   │       ├── model/          # Entités (Utilisateur, Gare, Trajet, etc.)
│   │       ├── dao/            # Data Access Objects
│   │       ├── service/        # Logique métier
│   │       ├── servlet/        # Contrôleurs (Servlets)
│   │       ├── filter/         # Filtres (authentification, encodage)
│   │       └── util/           # Classes utilitaires
│   ├── resources/
│   │   ├── database.properties # Configuration BDD
│   │   └── database/
│   │       └── init.sql        # Script d'initialisation
│   └── webapp/
│       ├── WEB-INF/
│       │   ├── views/          # Pages JSP
│       │   └── web.xml         # Configuration web
│       └── static/             # Ressources statiques (CSS, JS, images)
```

## Installation et Configuration

### Prérequis
- Java 11 ou supérieur
- Maven 3.6+
- MySQL 8.0+
- Serveur d'application (Tomcat 9+)

### Configuration de la Base de Données

1. **Créer la base de données :**
   ```sql
   CREATE DATABASE train CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. **Exécuter le script d'initialisation :**
   ```bash
   mysql -u root -p train < src/main/resources/database/init.sql
   ```

3. **Configurer la connexion :**
   Modifier le fichier `src/main/resources/database.properties` :
   ```properties
   db.url=*****************************************************************
   db.username=root
   db.password=votre_mot_de_passe
   ```

### Compilation et Déploiement

1. **Compiler le projet :**
   ```bash
   mvn clean compile
   ```

2. **Générer le WAR :**
   ```bash
   mvn clean package
   ```

3. **Déployer sur Tomcat :**
   Copier le fichier `target/Train.war` dans le dossier `webapps` de Tomcat

### Accès à l'Application

- **URL :** http://localhost:8080/Train
- **Compte administrateur par défaut :**
  - Email : <EMAIL>
  - Mot de passe : password

## Utilisation

### Pour les Clients
1. Créer un compte ou se connecter
2. Rechercher un trajet en saisissant ville de départ, d'arrivée et date
3. Sélectionner un trajet et nombre de places
4. Confirmer la réservation
5. Gérer ses réservations depuis son profil

### Pour les Administrateurs
1. Se connecter avec un compte administrateur
2. Accéder au panneau d'administration
3. Gérer les gares, trajets, et voyages
4. Consulter les statistiques et rapports
5. Traiter les demandes d'annulation

## Tests

### Exécuter les tests unitaires
```bash
mvn test
```

### Tests d'intégration
```bash
mvn verify
```

## Données de Test

Le script d'initialisation inclut :
- 8 gares dans différentes villes françaises
- Plusieurs trajets entre ces gares
- Un compte administrateur par défaut
- Des voyages programmés pour les prochains jours

## Contribution

1. Fork le projet
2. Créer une branche pour votre fonctionnalité
3. Commiter vos changements
4. Pousser vers la branche
5. Ouvrir une Pull Request

## Licence

Ce projet est sous licence MIT. Voir le fichier LICENSE pour plus de détails.

## Support

Pour toute question ou problème, veuillez ouvrir une issue sur le repository GitHub.
