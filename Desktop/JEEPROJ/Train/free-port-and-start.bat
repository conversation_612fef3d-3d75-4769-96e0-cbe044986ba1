@echo off
echo ========================================
echo    LIBERATION DU PORT ET DEMARRAGE
echo ========================================
echo.

cd /d "%~dp0"

echo 1. Recherche des processus utilisant le port 8080...
netstat -ano | findstr :8080

echo.
echo 2. Arrêt de tous les processus Java...
taskkill /F /IM java.exe 2>nul
taskkill /F /IM javaw.exe 2>nul

echo.
echo 3. Arrêt des processus utilisant le port 8080...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8080') do (
    echo Arrêt du processus %%a
    taskkill /F /PID %%a 2>nul
)

echo.
echo 4. Attente de libération du port...
timeout /t 5 /nobreak >nul

echo 5. Vérification que le port est libre...
netstat -ano | findstr :8080
if %ERRORLEVEL%==0 (
    echo ⚠️ Le port 8080 est encore utilisé, utilisation du port 8081...
    set PORT=8081
) else (
    echo ✅ Port 8080 libéré !
    set PORT=8080
)

echo.
echo 6. Compilation...
call mvn clean compile -q
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur de compilation
    pause
    exit /b 1
)
echo ✅ Compilation réussie

echo.
echo 7. Démarrage sur le port %PORT%...
echo.
echo 🚀 Lancement de TrainSystem...
echo 📍 URL: http://localhost:%PORT%/Train
echo 🔑 Compte: <EMAIL> / password
echo.

if "%PORT%"=="8081" (
    start "TrainSystem Server" cmd /k "echo ========================================= && echo    SERVEUR TRAINSYSTEM PORT 8081 && echo ========================================= && echo. && echo ✅ Application: http://localhost:8081/Train && echo 🔑 Compte: <EMAIL> / password && echo. && echo ⚠️ Pour arreter: Ctrl+C && echo. && mvn jetty:run -Djetty.port=8081"
    
    echo ⏳ Attente du démarrage (20 secondes)...
    timeout /t 20 /nobreak >nul
    
    echo 🌐 Ouverture sur le port 8081...
    start http://localhost:8081/Train
    
    echo.
    echo ✅ Application accessible sur : http://localhost:8081/Train
) else (
    start "TrainSystem Server" cmd /k "echo ========================================= && echo    SERVEUR TRAINSYSTEM PORT 8080 && echo ========================================= && echo. && echo ✅ Application: http://localhost:8080/Train && echo 🔑 Compte: <EMAIL> / password && echo. && echo ⚠️ Pour arreter: Ctrl+C && echo. && mvn jetty:run"
    
    echo ⏳ Attente du démarrage (20 secondes)...
    timeout /t 20 /nobreak >nul
    
    echo 🌐 Ouverture sur le port 8080...
    start http://localhost:8080/Train
    
    echo.
    echo ✅ Application accessible sur : http://localhost:8080/Train
)

echo.
echo ========================================
echo    APPLICATION LANCEE !
echo ========================================
echo.
echo 🎯 Fonctionnalités disponibles :
echo    ✅ Page d'accueil moderne
echo    ✅ Inscription utilisateur
echo    ✅ Connexion sécurisée
echo    ✅ Recherche de voyages
echo    ✅ Réservation de billets
echo    ✅ Gestion des réservations
echo.
echo 👤 Compte de test :
echo    📧 Email: <EMAIL>
echo    🔑 Password: password
echo.
echo 🗄️ Données de test :
echo    🏢 8 gares (Paris, Lyon, Marseille...)
echo    🚄 Trajets avec horaires réalistes
echo    📅 Voyages pour 7 jours
echo.

pause
