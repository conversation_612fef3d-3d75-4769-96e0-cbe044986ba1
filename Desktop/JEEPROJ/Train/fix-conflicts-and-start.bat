@echo off
echo ========================================
echo    CORRECTION DES CONFLITS ET DEMARRAGE
echo ========================================
echo.

cd /d "%~dp0"

echo 1. Arrêt de tous les processus Java...
taskkill /F /IM java.exe 2>nul
taskkill /F /IM javaw.exe 2>nul

echo.
echo 2. Libération du port 8081...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8081') do (
    echo Arrêt du processus %%a sur port 8081
    taskkill /F /PID %%a 2>nul
)

echo.
echo 3. Nettoyage complet du projet...
call mvn clean -q
if exist "target" rmdir /s /q "target" 2>nul

echo.
echo 4. Suppression des servlets en conflit...
if exist "src\main\java\com\train\servlet\DemoServlet.java" (
    del "src\main\java\com\train\servlet\DemoServlet.java"
    echo ✅ DemoServlet supprimé
)

echo.
echo 5. Compilation propre...
call mvn compile -q
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur de compilation
    pause
    exit /b 1
)
echo ✅ Compilation réussie

echo.
echo 6. Vérification des servlets...
echo Servlets présents :
dir /b src\main\java\com\train\servlet\*.java

echo.
echo 7. Démarrage sur le port 8081...
echo.
echo 🚀 Lancement de TrainSystem (sans conflits)...
echo 📍 URL: http://localhost:8081/Train
echo 🔑 Compte: <EMAIL> / password
echo.

start "TrainSystem Server Clean" cmd /k "echo ========================================= && echo    SERVEUR TRAINSYSTEM PROPRE && echo ========================================= && echo. && echo ✅ Application: http://localhost:8081/Train && echo 🔑 Compte: <EMAIL> / password && echo 📋 Servlets: AuthServlet, HomeServlet, SearchServlet, ReservationServlet && echo. && echo ⚠️ Pour arreter: Ctrl+C && echo. && mvn jetty:run -Djetty.port=8081"

echo ⏳ Attente du démarrage (25 secondes)...
timeout /t 25 /nobreak >nul

echo 🌐 Ouverture de l'application...
start http://localhost:8081/Train

echo.
echo ========================================
echo    APPLICATION CORRIGEE ET LANCEE !
echo ========================================
echo.
echo ✅ TrainSystem est accessible sur :
echo    👉 http://localhost:8081/Train
echo.
echo 🔧 Conflits résolus :
echo    ❌ DemoServlet supprimé
echo    ✅ AuthServlet (/login, /logout, /register)
echo    ✅ HomeServlet (/home, /)
echo    ✅ SearchServlet (/search)
echo    ✅ ReservationServlet (/reservation/*)
echo    ✅ TestServlet (/test-db, /test)
echo.
echo 🎯 Fonctionnalités disponibles :
echo    ✅ Page d'accueil
echo    ✅ Inscription/Connexion
echo    ✅ Recherche de voyages
echo    ✅ Réservation de billets
echo    ✅ Gestion des réservations
echo.
echo 👤 Compte de test :
echo    📧 Email: <EMAIL>
echo    🔑 Password: password
echo.

pause
