-- Script pour créer automatiquement des voyages basés sur vos trajets existants
USE train;

-- 1. Vérifier les trajets existants
SELECT 
    t.id,
    CONCAT(gd.ville, ' → ', ga.ville) as trajet,
    t.heure_depart,
    t.heure_arrivee,
    t.prix,
    t.nombre_places,
    t.type_train,
    t.actif
FROM trajets t
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
WHERE t.actif = TRUE
ORDER BY gd.ville, t.heure_depart;

-- 2. Supprimer les anciens voyages de test (optionnel)
-- DELETE FROM voyages WHERE date_voyage < CURDATE();

-- 3. Créer des voyages pour les 14 prochains jours sur tous les trajets actifs
INSERT INTO voyages (trajet_id, date_voyage, places_disponibles, places_reservees, statut, retard_minutes)
SELECT 
    t.id as trajet_id,
    DATE_ADD(CURDATE(), INTERVAL day_num DAY) as date_voyage,
    t.nombre_places as places_disponibles,
    CASE 
        -- Simuler des réservations réalistes
        WHEN day_num <= 2 THEN FLOOR(t.nombre_places * (0.6 + RAND() * 0.3))  -- 60-90% pour les 2 prochains jours
        WHEN day_num <= 7 THEN FLOOR(t.nombre_places * (0.3 + RAND() * 0.4))  -- 30-70% pour la semaine
        ELSE FLOOR(t.nombre_places * (0.1 + RAND() * 0.3))                    -- 10-40% pour plus tard
    END as places_reservees,
    CASE 
        -- Simuler différents statuts
        WHEN day_num = 0 THEN 'EN_COURS'
        WHEN day_num <= 7 AND RAND() < 0.05 THEN 'RETARDE'
        WHEN day_num <= 14 AND RAND() < 0.02 THEN 'ANNULE'
        ELSE 'PROGRAMME'
    END as statut,
    CASE 
        -- Ajouter des retards aléatoires pour certains voyages
        WHEN RAND() < 0.1 THEN FLOOR(5 + RAND() * 25)  -- 5-30 minutes de retard pour 10% des voyages
        ELSE 0
    END as retard_minutes
FROM trajets t
CROSS JOIN (
    SELECT 0 as day_num UNION ALL   -- Aujourd'hui
    SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 UNION ALL 
    SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL 
    SELECT 9 UNION ALL SELECT 10 UNION ALL SELECT 11 UNION ALL SELECT 12 UNION ALL 
    SELECT 13 UNION ALL SELECT 14   -- 14 jours au total
) days
WHERE t.actif = TRUE
AND NOT EXISTS (
    -- Éviter les doublons
    SELECT 1 FROM voyages v 
    WHERE v.trajet_id = t.id 
    AND v.date_voyage = DATE_ADD(CURDATE(), INTERVAL day_num DAY)
)
ORDER BY t.id, day_num;

-- 4. Mettre à jour les places disponibles en fonction des réservations
UPDATE voyages 
SET places_disponibles = (
    SELECT t.nombre_places - v.places_reservees
    FROM trajets t 
    WHERE t.id = voyages.trajet_id
)
WHERE places_disponibles > (
    SELECT t.nombre_places 
    FROM trajets t 
    WHERE t.id = voyages.trajet_id
);

-- 5. Ajouter quelques voyages spéciaux pour le week-end (plus de fréquence)
INSERT INTO voyages (trajet_id, date_voyage, places_disponibles, places_reservees, statut)
SELECT 
    t.id,
    DATE_ADD(CURDATE(), INTERVAL (WEEKDAY(CURDATE()) + 6 - WEEKDAY(CURDATE())) DAY) as samedi,
    t.nombre_places,
    FLOOR(t.nombre_places * 0.8), -- 80% de réservations le samedi
    'PROGRAMME'
FROM trajets t
WHERE t.actif = TRUE
AND t.type_train IN ('TGV', 'INTERCITES') -- Seulement pour les trains longue distance
AND NOT EXISTS (
    SELECT 1 FROM voyages v 
    WHERE v.trajet_id = t.id 
    AND v.date_voyage = DATE_ADD(CURDATE(), INTERVAL (WEEKDAY(CURDATE()) + 6 - WEEKDAY(CURDATE())) DAY)
);

-- 6. Vérifier les résultats
SELECT 
    'Résumé des voyages créés' as info,
    COUNT(*) as total_voyages,
    COUNT(CASE WHEN statut = 'PROGRAMME' THEN 1 END) as programmes,
    COUNT(CASE WHEN statut = 'EN_COURS' THEN 1 END) as en_cours,
    COUNT(CASE WHEN statut = 'RETARDE' THEN 1 END) as retardes,
    COUNT(CASE WHEN statut = 'ANNULE' THEN 1 END) as annules,
    MIN(date_voyage) as premiere_date,
    MAX(date_voyage) as derniere_date
FROM voyages;

-- 7. Afficher un échantillon des voyages créés
SELECT 
    v.id,
    CONCAT(gd.ville, ' → ', ga.ville) as trajet,
    v.date_voyage,
    CONCAT(v.places_reservees, '/', (v.places_disponibles + v.places_reservees)) as occupation,
    ROUND((v.places_reservees / (v.places_disponibles + v.places_reservees)) * 100, 1) as taux_occupation_pct,
    v.statut,
    CASE WHEN v.retard_minutes > 0 THEN CONCAT(v.retard_minutes, ' min') ELSE 'À l\'heure' END as retard,
    t.heure_depart,
    t.heure_arrivee,
    t.prix,
    t.type_train
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
WHERE v.date_voyage >= CURDATE()
ORDER BY v.date_voyage, gd.ville, t.heure_depart
LIMIT 20;

-- 8. Statistiques par jour
SELECT 
    v.date_voyage,
    COUNT(*) as nb_voyages,
    SUM(v.places_reservees) as total_places_reservees,
    SUM(v.places_disponibles + v.places_reservees) as total_places,
    ROUND(AVG((v.places_reservees / (v.places_disponibles + v.places_reservees)) * 100), 1) as taux_occupation_moyen
FROM voyages v
WHERE v.date_voyage >= CURDATE()
GROUP BY v.date_voyage
ORDER BY v.date_voyage
LIMIT 10;

COMMIT;
