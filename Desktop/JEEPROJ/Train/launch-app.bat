@echo off
echo ========================================
echo    LANCEMENT TRAINSYSTEM
echo ========================================
echo.

cd /d "%~dp0"

REM Nettoyer les anciens processus
taskkill /F /IM java.exe 2>nul
taskkill /F /IM python.exe 2>nul
timeout /t 2 /nobreak >nul

REM Supprimer les fichiers de test
if exist "target\webapp" rmdir /s /q "target\webapp" 2>nul

echo 1. Compilation de l'application...
call mvn clean compile -q
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur de compilation
    pause
    exit /b 1
)
echo ✅ Compilation réussie

echo 2. Génération du WAR...
call mvn package -DskipTests -q
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur de packaging
    pause
    exit /b 1
)
echo ✅ WAR généré

echo 3. Recherche d'un serveur d'application...

REM Essayer de trouver Tomcat
set TOMCAT_FOUND=0
if exist "C:\Program Files\Apache Software Foundation\Tomcat*" (
    for /d %%i in ("C:\Program Files\Apache Software Foundation\Tomcat*") do (
        set "CATALINA_HOME=%%i"
        set TOMCAT_FOUND=1
        goto :tomcat_found
    )
)

if exist "C:\apache-tomcat*" (
    for /d %%i in ("C:\apache-tomcat*") do (
        set "CATALINA_HOME=%%i"
        set TOMCAT_FOUND=1
        goto :tomcat_found
    )
)

if defined CATALINA_HOME (
    if exist "%CATALINA_HOME%" (
        set TOMCAT_FOUND=1
        goto :tomcat_found
    )
)

:tomcat_found
if %TOMCAT_FOUND%==1 (
    echo ✅ Tomcat trouvé : %CATALINA_HOME%
    
    echo 4. Déploiement sur Tomcat...
    
    REM Arrêter Tomcat
    if exist "%CATALINA_HOME%\bin\shutdown.bat" (
        call "%CATALINA_HOME%\bin\shutdown.bat" >nul 2>&1
        timeout /t 3 /nobreak >nul
    )
    
    REM Nettoyer l'ancien déploiement
    if exist "%CATALINA_HOME%\webapps\Train" rmdir /s /q "%CATALINA_HOME%\webapps\Train"
    if exist "%CATALINA_HOME%\webapps\Train.war" del "%CATALINA_HOME%\webapps\Train.war"
    
    REM Copier le nouveau WAR
    copy "target\Train.war" "%CATALINA_HOME%\webapps\" >nul
    echo ✅ WAR déployé
    
    REM Démarrer Tomcat
    echo 5. Démarrage de Tomcat...
    start "Tomcat Server" "%CATALINA_HOME%\bin\startup.bat"
    
    echo ⏳ Attente du démarrage (20 secondes)...
    timeout /t 20 /nobreak >nul
    
    REM Vérifier si Tomcat répond
    powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:8080/Train' -TimeoutSec 10 | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
    if %ERRORLEVEL%==0 (
        echo ✅ Application démarrée avec succès !
        echo 🌐 Ouverture de l'application...
        start http://localhost:8080/Train
        goto :success
    ) else (
        echo ⚠️ Tomcat démarre encore, patientez...
        echo 🌐 Essayez : http://localhost:8080/Train
        start http://localhost:8080/Train
        goto :success
    )
) else (
    echo ⚠️ Tomcat non trouvé, utilisation d'un serveur alternatif...
    goto :alternative
)

:alternative
echo 4. Démarrage avec serveur intégré Maven...

REM Utiliser Maven avec serveur intégré
start "TrainSystem Maven Server" cmd /c "cd /d \"%~dp0\" && mvn tomcat7:run -Dmaven.tomcat.port=8080 -Dmaven.tomcat.path=/Train"

echo ⏳ Attente du démarrage Maven (15 secondes)...
timeout /t 15 /nobreak >nul

echo 🌐 Tentative d'ouverture de l'application...
start http://localhost:8080/Train

:success
echo.
echo ========================================
echo    TRAINSYSTEM LANCE !
echo ========================================
echo.
echo 🎉 L'application TrainSystem est maintenant accessible !
echo.
echo 📍 URL : http://localhost:8080/Train
echo.
echo 👤 Compte de test :
echo    Email    : <EMAIL>
echo    Password : password
echo.
echo 🚀 Fonctionnalités disponibles :
echo    • Inscription et connexion
echo    • Recherche de voyages
echo    • Réservation de billets
echo    • Gestion des réservations
echo.
echo 🔧 Mode démonstration avec données en mémoire
echo.
echo Pour arrêter l'application :
echo - Fermez la fenêtre du serveur
echo - Ou utilisez Ctrl+C dans la console du serveur
echo.

pause
