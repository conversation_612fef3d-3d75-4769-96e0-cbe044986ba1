# 🗄️ Guide de Création de la Table Gares

## 🎯 Objectif
Créer la table `gares` dans votre base de données MySQL pour permettre l'enregistrement des gares via le formulaire.

## 📋 Étapes à suivre

### 1. Ouvrir phpMyAdmin
- Accédez à : `http://localhost/phpmyadmin`
- Connectez-vous avec vos identifiants MySQL

### 2. Sélectionner la base de données
- Cliquez sur la base de données **`train`** dans le panneau de gauche
- Si elle n'existe pas, créez-la d'abord :
  ```sql
  CREATE DATABASE train CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
  ```

### 3. Exécuter le script SQL
- Cliquez sur l'onglet **"SQL"** en haut
- Copiez et collez le contenu du fichier `CREATE_TABLE_GARES.sql`
- Cliquez sur **"Exécuter"**

### 4. Vérifier la création
Après exécution, vous devriez voir :
- ✅ Table `gares` créée avec succès
- ✅ 10 gares de test insérées
- ✅ Statistiques affichées

## 📊 Structure de la table

| Colonne | Type | Description |
|---------|------|-------------|
| `id` | BIGINT AUTO_INCREMENT | Identifiant unique |
| `nom` | VARCHAR(100) | Nom de la gare |
| `ville` | VARCHAR(100) | Ville de la gare |
| `code_gare` | VARCHAR(10) UNIQUE | Code unique de la gare |
| `adresse` | TEXT | Adresse complète |
| `code_postal` | VARCHAR(10) | Code postal |
| `latitude` | DECIMAL(10,6) | Coordonnée GPS latitude |
| `longitude` | DECIMAL(10,6) | Coordonnée GPS longitude |
| `active` | BOOLEAN | Statut actif/inactif |
| `date_creation` | TIMESTAMP | Date de création |
| `date_modification` | TIMESTAMP | Date de modification |

## 🧪 Test après création

### 1. Vérifier dans phpMyAdmin
- Allez dans la table `gares`
- Cliquez sur "Afficher" pour voir les données
- Vous devriez voir 10 gares de test

### 2. Tester le formulaire
- Accédez à : `http://localhost:8080/Train`
- Connectez-vous : `<EMAIL>` / `password`
- Allez dans : Gares → Nouvelle Gare
- Remplissez le formulaire et soumettez

### 3. Vérifier l'enregistrement
- Retournez dans phpMyAdmin
- Actualisez la table `gares`
- Votre nouvelle gare devrait apparaître

## 🔧 Script SQL complet

```sql
-- Utiliser la base de données train
USE train;

-- Créer la table gares
CREATE TABLE IF NOT EXISTS gares (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    ville VARCHAR(100) NOT NULL,
    code_gare VARCHAR(10) NOT NULL UNIQUE,
    adresse TEXT,
    code_postal VARCHAR(10),
    latitude DECIMAL(10, 6),
    longitude DECIMAL(10, 6),
    active BOOLEAN DEFAULT TRUE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_code_gare (code_gare),
    INDEX idx_ville (ville),
    INDEX idx_active (active)
);

-- Insérer des données de test
INSERT INTO gares (nom, ville, code_gare, adresse, code_postal, latitude, longitude, active) VALUES
('Gare de Paris-Nord', 'Paris', 'PARN', '18 Rue de Dunkerque', '75010', 48.8809, 2.3553, TRUE),
('Gare de Lyon-Part-Dieu', 'Lyon', 'LYONPD', 'Place Charles Béraudier', '69003', 45.7606, 4.8598, TRUE),
('Gare de Marseille-Saint-Charles', 'Marseille', 'MARSC', 'Square Narvik', '13001', 43.3030, 5.3808, TRUE);
```

## ⚠️ Problèmes courants

### Erreur "Table doesn't exist"
- Vérifiez que vous êtes dans la bonne base de données
- Exécutez d'abord le script de création

### Erreur "Duplicate entry"
- La table existe déjà
- Utilisez `DROP TABLE IF EXISTS gares;` avant de recréer

### Erreur de connexion
- Vérifiez les paramètres dans `database.properties`
- Assurez-vous que MySQL est démarré

## 🎉 Résultat attendu

Après avoir exécuté ce script :
1. ✅ Table `gares` créée dans la base `train`
2. ✅ 10 gares de test disponibles
3. ✅ Formulaire fonctionnel pour ajouter de nouvelles gares
4. ✅ Données sauvegardées en base de données

---

**Exécutez ce script et testez le formulaire ! 🚀**
