-- ========================================
-- MIGRATION COMPLETE BASE DE DONNEES TRAIN
-- Version: 1.0
-- Date: 2025-01-20
-- Description: Migration complète des tables pour TrainSystem
-- ========================================

-- Sélectionner la base de données
USE train;

-- Configuration pour la migration
SET FOREIGN_KEY_CHECKS = 0;
SET AUTOCOMMIT = 0;
START TRANSACTION;

-- ========================================
-- SUPPRESSION DES TABLES EXISTANTES
-- ========================================
DROP TABLE IF EXISTS reservations;
DROP TABLE IF EXISTS voyages;
DROP TABLE IF EXISTS trajets;
DROP TABLE IF EXISTS utilisateurs;
DROP TABLE IF EXISTS gares;

-- Supprimer les vues si elles existent
DROP VIEW IF EXISTS v_voyages_details;
DROP VIEW IF EXISTS v_reservations_details;
DROP VIEW IF EXISTS v_statistiques_generales;

-- Supprimer les procédures si elles existent
DROP PROCEDURE IF EXISTS sp_rechercher_voyages;
DROP PROCEDURE IF EXISTS sp_creer_reservation;
DROP PROCEDURE IF EXISTS sp_statistiques;
DROP PROCEDURE IF EXISTS sp_confirmer_reservation;

-- ========================================
-- CREATION TABLE: gares
-- ========================================
CREATE TABLE gares (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'Identifiant unique de la gare',
    nom VARCHAR(100) NOT NULL COMMENT 'Nom complet de la gare',
    ville VARCHAR(100) NOT NULL COMMENT 'Ville où se trouve la gare',
    code_gare VARCHAR(10) UNIQUE NOT NULL COMMENT 'Code unique de la gare (ex: PAR01)',
    adresse VARCHAR(255) COMMENT 'Adresse complète de la gare',
    code_postal VARCHAR(10) COMMENT 'Code postal',
    latitude DECIMAL(10, 8) COMMENT 'Coordonnée latitude GPS',
    longitude DECIMAL(11, 8) COMMENT 'Coordonnée longitude GPS',
    active BOOLEAN DEFAULT TRUE COMMENT 'Gare active (TRUE) ou fermée (FALSE)',
    capacite_max INT DEFAULT 1000 COMMENT 'Capacité maximale de voyageurs',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Date de création de l\'enregistrement',
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Date de dernière modification',
    
    -- Index pour optimiser les recherches
    INDEX idx_gares_ville (ville),
    INDEX idx_gares_code (code_gare),
    INDEX idx_gares_active (active),
    INDEX idx_gares_ville_active (ville, active),
    
    -- Contraintes
    CONSTRAINT chk_gares_latitude CHECK (latitude BETWEEN -90 AND 90),
    CONSTRAINT chk_gares_longitude CHECK (longitude BETWEEN -180 AND 180),
    CONSTRAINT chk_gares_capacite CHECK (capacite_max > 0)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Table des gares - Stocke les informations des gares ferroviaires';

-- ========================================
-- CREATION TABLE: utilisateurs
-- ========================================
CREATE TABLE utilisateurs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'Identifiant unique de l\'utilisateur',
    nom VARCHAR(100) NOT NULL COMMENT 'Nom de famille',
    prenom VARCHAR(100) NOT NULL COMMENT 'Prénom',
    email VARCHAR(255) UNIQUE NOT NULL COMMENT 'Adresse email unique',
    mot_de_passe VARCHAR(255) NOT NULL COMMENT 'Mot de passe haché (BCrypt)',
    telephone VARCHAR(20) COMMENT 'Numéro de téléphone',
    date_naissance DATE COMMENT 'Date de naissance',
    adresse VARCHAR(255) COMMENT 'Adresse postale complète',
    ville VARCHAR(100) COMMENT 'Ville de résidence',
    code_postal VARCHAR(10) COMMENT 'Code postal',
    pays VARCHAR(50) DEFAULT 'France' COMMENT 'Pays de résidence',
    type_utilisateur ENUM('CLIENT', 'EMPLOYE', 'ADMINISTRATEUR') DEFAULT 'CLIENT' COMMENT 'Type d\'utilisateur',
    actif BOOLEAN DEFAULT TRUE COMMENT 'Compte actif (TRUE) ou suspendu (FALSE)',
    email_verifie BOOLEAN DEFAULT FALSE COMMENT 'Email vérifié ou non',
    tentatives_connexion INT DEFAULT 0 COMMENT 'Nombre de tentatives de connexion échouées',
    compte_bloque_jusqu TIMESTAMP NULL COMMENT 'Date jusqu\'à laquelle le compte est bloqué',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Date de création du compte',
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Date de dernière modification',
    derniere_connexion TIMESTAMP NULL COMMENT 'Date et heure de la dernière connexion',
    
    -- Index pour optimiser les recherches
    INDEX idx_utilisateurs_email (email),
    INDEX idx_utilisateurs_type (type_utilisateur),
    INDEX idx_utilisateurs_actif (actif),
    INDEX idx_utilisateurs_nom_prenom (nom, prenom),
    INDEX idx_utilisateurs_derniere_connexion (derniere_connexion),
    INDEX idx_utilisateurs_type_actif (type_utilisateur, actif),
    
    -- Contraintes
    CONSTRAINT chk_utilisateurs_email_format CHECK (email REGEXP '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT chk_utilisateurs_tentatives CHECK (tentatives_connexion >= 0),
    CONSTRAINT chk_utilisateurs_age CHECK (date_naissance IS NULL OR date_naissance <= CURDATE())
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Table des utilisateurs - Stocke les comptes utilisateurs du système';

-- ========================================
-- CREATION TABLE: trajets
-- ========================================
CREATE TABLE trajets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'Identifiant unique du trajet',
    gare_depart_id BIGINT NOT NULL COMMENT 'ID de la gare de départ',
    gare_arrivee_id BIGINT NOT NULL COMMENT 'ID de la gare d\'arrivée',
    heure_depart TIME NOT NULL COMMENT 'Heure de départ programmée',
    heure_arrivee TIME NOT NULL COMMENT 'Heure d\'arrivée programmée',
    duree_minutes INT GENERATED ALWAYS AS (
        CASE 
            WHEN heure_arrivee >= heure_depart THEN 
                TIME_TO_SEC(heure_arrivee) - TIME_TO_SEC(heure_depart)
            ELSE 
                (24*3600) - TIME_TO_SEC(heure_depart) + TIME_TO_SEC(heure_arrivee)
        END / 60
    ) STORED COMMENT 'Durée du trajet en minutes (calculée automatiquement)',
    prix DECIMAL(10, 2) NOT NULL COMMENT 'Prix du billet en euros',
    nombre_places INT NOT NULL DEFAULT 200 COMMENT 'Nombre total de places disponibles',
    type_train ENUM('TGV', 'INTERCITES', 'TER', 'OUIGO', 'AVE', 'EUROSTAR') DEFAULT 'TER' COMMENT 'Type de train',
    numero_train VARCHAR(20) COMMENT 'Numéro du train (ex: TGV 6651)',
    jours_circulation SET('LUNDI','MARDI','MERCREDI','JEUDI','VENDREDI','SAMEDI','DIMANCHE') 
        DEFAULT 'LUNDI,MARDI,MERCREDI,JEUDI,VENDREDI,SAMEDI,DIMANCHE' COMMENT 'Jours de circulation',
    actif BOOLEAN DEFAULT TRUE COMMENT 'Trajet actif ou suspendu',
    date_debut_validite DATE DEFAULT (CURDATE()) COMMENT 'Date de début de validité du trajet',
    date_fin_validite DATE COMMENT 'Date de fin de validité du trajet',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Date de création',
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Date de modification',
    
    -- Clés étrangères
    FOREIGN KEY (gare_depart_id) REFERENCES gares(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (gare_arrivee_id) REFERENCES gares(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    
    -- Index pour optimiser les recherches
    INDEX idx_trajets_gare_depart (gare_depart_id),
    INDEX idx_trajets_gare_arrivee (gare_arrivee_id),
    INDEX idx_trajets_gares (gare_depart_id, gare_arrivee_id),
    INDEX idx_trajets_heure_depart (heure_depart),
    INDEX idx_trajets_prix (prix),
    INDEX idx_trajets_type_train (type_train),
    INDEX idx_trajets_actif (actif),
    INDEX idx_trajets_validite (date_debut_validite, date_fin_validite),
    INDEX idx_trajets_recherche (gare_depart_id, gare_arrivee_id, actif),
    
    -- Contraintes
    CONSTRAINT chk_trajets_gares_differentes CHECK (gare_depart_id != gare_arrivee_id),
    CONSTRAINT chk_trajets_prix_positif CHECK (prix > 0),
    CONSTRAINT chk_trajets_places_positives CHECK (nombre_places > 0),
    CONSTRAINT chk_trajets_validite CHECK (date_fin_validite IS NULL OR date_fin_validite >= date_debut_validite)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Table des trajets - Définit les liaisons entre gares avec horaires et tarifs';

-- ========================================
-- CREATION TABLE: voyages
-- ========================================
CREATE TABLE voyages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'Identifiant unique du voyage',
    trajet_id BIGINT NOT NULL COMMENT 'ID du trajet associé',
    date_voyage DATE NOT NULL COMMENT 'Date du voyage',
    places_disponibles INT NOT NULL COMMENT 'Nombre de places encore disponibles',
    places_reservees INT DEFAULT 0 COMMENT 'Nombre de places déjà réservées',
    places_occupees INT DEFAULT 0 COMMENT 'Nombre de places effectivement occupées',
    statut ENUM('PROGRAMME', 'EN_COURS', 'TERMINE', 'ANNULE', 'RETARDE', 'SUSPENDU') 
        DEFAULT 'PROGRAMME' COMMENT 'Statut actuel du voyage',
    retard_minutes INT DEFAULT 0 COMMENT 'Retard en minutes par rapport à l\'horaire prévu',
    motif_retard VARCHAR(255) COMMENT 'Motif du retard si applicable',
    prix_dynamique DECIMAL(10, 2) COMMENT 'Prix ajusté dynamiquement (si différent du prix de base)',
    taux_occupation DECIMAL(5, 2) GENERATED ALWAYS AS (
        CASE 
            WHEN (places_disponibles + places_reservees) > 0 THEN 
                (places_reservees * 100.0) / (places_disponibles + places_reservees)
            ELSE 0 
        END
    ) STORED COMMENT 'Taux d\'occupation en pourcentage (calculé)',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Date de création',
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Date de modification',
    
    -- Clé étrangère
    FOREIGN KEY (trajet_id) REFERENCES trajets(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    
    -- Index pour optimiser les recherches
    UNIQUE KEY unique_trajet_date (trajet_id, date_voyage),
    INDEX idx_voyages_date (date_voyage),
    INDEX idx_voyages_statut (statut),
    INDEX idx_voyages_places_disponibles (places_disponibles),
    INDEX idx_voyages_trajet_date (trajet_id, date_voyage),
    INDEX idx_voyages_recherche (date_voyage, statut, places_disponibles),
    INDEX idx_voyages_taux_occupation (taux_occupation),
    
    -- Contraintes
    CONSTRAINT chk_voyages_places_coherentes CHECK (places_disponibles >= 0),
    CONSTRAINT chk_voyages_places_reservees_positives CHECK (places_reservees >= 0),
    CONSTRAINT chk_voyages_places_occupees_positives CHECK (places_occupees >= 0),
    CONSTRAINT chk_voyages_retard_positif CHECK (retard_minutes >= 0),
    CONSTRAINT chk_voyages_prix_dynamique_positif CHECK (prix_dynamique IS NULL OR prix_dynamique > 0),
    CONSTRAINT chk_voyages_places_logiques CHECK (places_occupees <= places_reservees)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='Table des voyages - Instances spécifiques des trajets pour des dates données';

-- ========================================
-- CREATION TABLE: reservations
-- ========================================
CREATE TABLE reservations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'Identifiant unique de la réservation',
    numero_reservation VARCHAR(20) UNIQUE NOT NULL COMMENT 'Numéro unique de réservation (ex: RES123456)',
    utilisateur_id BIGINT NOT NULL COMMENT 'ID de l\'utilisateur qui a fait la réservation',
    voyage_id BIGINT NOT NULL COMMENT 'ID du voyage réservé',
    nombre_places INT NOT NULL DEFAULT 1 COMMENT 'Nombre de places réservées',
    prix_unitaire DECIMAL(10, 2) NOT NULL COMMENT 'Prix par place au moment de la réservation',
    prix_total DECIMAL(10, 2) NOT NULL COMMENT 'Prix total de la réservation',
    statut ENUM('EN_ATTENTE', 'CONFIRMEE', 'PAYEE', 'ANNULEE', 'REMBOURSEE', 'EXPIREE')
        DEFAULT 'EN_ATTENTE' COMMENT 'Statut de la réservation',

    -- Dates importantes
    date_reservation TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Date et heure de création de la réservation',
    date_confirmation TIMESTAMP NULL COMMENT 'Date de confirmation de la réservation',
    date_paiement TIMESTAMP NULL COMMENT 'Date du paiement',
    date_annulation TIMESTAMP NULL COMMENT 'Date d\'annulation si applicable',
    date_expiration TIMESTAMP GENERATED ALWAYS AS (
        DATE_ADD(date_reservation, INTERVAL 30 MINUTE)
    ) STORED COMMENT 'Date d\'expiration automatique (30 min après création)',

    -- Informations de paiement
    mode_paiement ENUM('CARTE_BANCAIRE', 'PAYPAL', 'VIREMENT', 'ESPECES', 'CHEQUE', 'MOBILE')
        DEFAULT 'CARTE_BANCAIRE' COMMENT 'Mode de paiement utilisé',
    reference_paiement VARCHAR(100) COMMENT 'Référence du paiement (transaction ID)',
    montant_paye DECIMAL(10, 2) COMMENT 'Montant effectivement payé',

    -- Informations d'annulation
    motif_annulation TEXT COMMENT 'Motif de l\'annulation',
    frais_annulation DECIMAL(10, 2) DEFAULT 0 COMMENT 'Frais d\'annulation appliqués',
    montant_rembourse DECIMAL(10, 2) COMMENT 'Montant remboursé en cas d\'annulation',

    -- Informations supplémentaires
    commentaire_client TEXT COMMENT 'Commentaire ou demande spéciale du client',
    code_reduction VARCHAR(20) COMMENT 'Code de réduction appliqué',
    reduction_appliquee DECIMAL(10, 2) DEFAULT 0 COMMENT 'Montant de la réduction',

    -- Métadonnées
    ip_creation VARCHAR(45) COMMENT 'Adresse IP lors de la création',
    user_agent TEXT COMMENT 'User agent du navigateur',
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Date de dernière modification',

    -- Clés étrangères
    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (voyage_id) REFERENCES voyages(id) ON DELETE RESTRICT ON UPDATE CASCADE,

    -- Index pour optimiser les recherches
    INDEX idx_reservations_numero (numero_reservation),
    INDEX idx_reservations_utilisateur (utilisateur_id),
    INDEX idx_reservations_voyage (voyage_id),
    INDEX idx_reservations_statut (statut),
    INDEX idx_reservations_date_reservation (date_reservation),
    INDEX idx_reservations_date_voyage (voyage_id, date_reservation),
    INDEX idx_reservations_utilisateur_statut (utilisateur_id, statut),
    INDEX idx_reservations_paiement (mode_paiement, reference_paiement),
    INDEX idx_reservations_expiration (date_expiration, statut),

    -- Contraintes
    CONSTRAINT chk_reservations_nombre_places_positif CHECK (nombre_places > 0),
    CONSTRAINT chk_reservations_prix_unitaire_positif CHECK (prix_unitaire > 0),
    CONSTRAINT chk_reservations_prix_total_positif CHECK (prix_total > 0),
    CONSTRAINT chk_reservations_prix_coherent CHECK (prix_total = (prix_unitaire * nombre_places) - reduction_appliquee),
    CONSTRAINT chk_reservations_frais_annulation_positifs CHECK (frais_annulation >= 0),
    CONSTRAINT chk_reservations_reduction_positive CHECK (reduction_appliquee >= 0),
    CONSTRAINT chk_reservations_montant_paye_positif CHECK (montant_paye IS NULL OR montant_paye >= 0),
    CONSTRAINT chk_reservations_montant_rembourse_positif CHECK (montant_rembourse IS NULL OR montant_rembourse >= 0)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Table des réservations - Stocke toutes les réservations de billets';

-- ========================================
-- CREATION DES TRIGGERS
-- ========================================

DELIMITER //

-- Trigger pour générer automatiquement le numéro de réservation
CREATE TRIGGER tr_reservations_before_insert
BEFORE INSERT ON reservations
FOR EACH ROW
BEGIN
    IF NEW.numero_reservation IS NULL OR NEW.numero_reservation = '' THEN
        SET NEW.numero_reservation = CONCAT('RES', YEAR(NOW()), LPAD(FLOOR(RAND() * 999999), 6, '0'));
    END IF;
END//

-- Trigger pour mettre à jour les places lors d'une nouvelle réservation
CREATE TRIGGER tr_reservations_after_insert
AFTER INSERT ON reservations
FOR EACH ROW
BEGIN
    IF NEW.statut IN ('CONFIRMEE', 'EN_ATTENTE', 'PAYEE') THEN
        UPDATE voyages
        SET places_disponibles = places_disponibles - NEW.nombre_places,
            places_reservees = places_reservees + NEW.nombre_places
        WHERE id = NEW.voyage_id;
    END IF;
END//

-- Trigger pour gérer les changements de statut des réservations
CREATE TRIGGER tr_reservations_after_update
AFTER UPDATE ON reservations
FOR EACH ROW
BEGIN
    -- Si le statut change vers confirmé depuis en attente
    IF OLD.statut = 'EN_ATTENTE' AND NEW.statut = 'CONFIRMEE' THEN
        UPDATE reservations
        SET date_confirmation = CURRENT_TIMESTAMP
        WHERE id = NEW.id;
    END IF;

    -- Si le statut change vers payé
    IF OLD.statut != 'PAYEE' AND NEW.statut = 'PAYEE' THEN
        UPDATE reservations
        SET date_paiement = CURRENT_TIMESTAMP,
            montant_paye = NEW.prix_total
        WHERE id = NEW.id;
    END IF;

    -- Si le statut change vers annulé
    IF OLD.statut NOT IN ('ANNULEE', 'REMBOURSEE') AND NEW.statut = 'ANNULEE' THEN
        UPDATE reservations
        SET date_annulation = CURRENT_TIMESTAMP
        WHERE id = NEW.id;

        -- Libérer les places
        UPDATE voyages
        SET places_disponibles = places_disponibles + OLD.nombre_places,
            places_reservees = places_reservees - OLD.nombre_places
        WHERE id = OLD.voyage_id;
    END IF;

    -- Si le statut change vers remboursé
    IF OLD.statut != 'REMBOURSEE' AND NEW.statut = 'REMBOURSEE' THEN
        UPDATE reservations
        SET montant_rembourse = NEW.prix_total - NEW.frais_annulation
        WHERE id = NEW.id;
    END IF;
END//

-- Trigger pour nettoyer les réservations expirées
CREATE EVENT IF NOT EXISTS ev_nettoyer_reservations_expirees
ON SCHEDULE EVERY 1 HOUR
DO
BEGIN
    -- Marquer comme expirées les réservations en attente depuis plus de 30 minutes
    UPDATE reservations
    SET statut = 'EXPIREE'
    WHERE statut = 'EN_ATTENTE'
    AND date_reservation < DATE_SUB(NOW(), INTERVAL 30 MINUTE);

    -- Libérer les places des réservations expirées
    UPDATE voyages v
    JOIN reservations r ON v.id = r.voyage_id
    SET v.places_disponibles = v.places_disponibles + r.nombre_places,
        v.places_reservees = v.places_reservees - r.nombre_places
    WHERE r.statut = 'EXPIREE'
    AND r.date_modification >= DATE_SUB(NOW(), INTERVAL 1 HOUR);
END//

DELIMITER ;

-- ========================================
-- CREATION DES VUES
-- ========================================

-- Vue complète des voyages avec détails des gares
CREATE VIEW v_voyages_details AS
SELECT
    v.id as voyage_id,
    v.date_voyage,
    v.places_disponibles,
    v.places_reservees,
    v.taux_occupation,
    v.statut as statut_voyage,
    v.retard_minutes,

    t.id as trajet_id,
    t.heure_depart,
    t.heure_arrivee,
    t.duree_minutes,
    COALESCE(v.prix_dynamique, t.prix) as prix_actuel,
    t.prix as prix_base,
    t.type_train,
    t.numero_train,

    gd.id as gare_depart_id,
    gd.nom as gare_depart_nom,
    gd.ville as ville_depart,
    gd.code_gare as code_depart,

    ga.id as gare_arrivee_id,
    ga.nom as gare_arrivee_nom,
    ga.ville as ville_arrivee,
    ga.code_gare as code_arrivee,

    -- Calculs utiles
    CASE
        WHEN v.places_disponibles = 0 THEN 'COMPLET'
        WHEN v.taux_occupation >= 90 THEN 'PRESQUE_COMPLET'
        WHEN v.taux_occupation >= 70 THEN 'BIEN_REMPLI'
        ELSE 'DISPONIBLE'
    END as niveau_disponibilite

FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
WHERE v.statut = 'PROGRAMME'
AND t.actif = TRUE
AND gd.active = TRUE
AND ga.active = TRUE;

-- Vue détaillée des réservations
CREATE VIEW v_reservations_details AS
SELECT
    r.id as reservation_id,
    r.numero_reservation,
    r.nombre_places,
    r.prix_unitaire,
    r.prix_total,
    r.reduction_appliquee,
    r.statut as statut_reservation,
    r.date_reservation,
    r.date_confirmation,
    r.date_paiement,
    r.date_expiration,
    r.mode_paiement,
    r.reference_paiement,

    -- Informations utilisateur
    CONCAT(u.prenom, ' ', u.nom) as nom_complet,
    u.email,
    u.telephone,

    -- Informations voyage
    v.date_voyage,
    v.statut as statut_voyage,

    -- Informations trajet
    CONCAT(gd.ville, ' → ', ga.ville) as trajet,
    t.heure_depart,
    t.heure_arrivee,
    t.duree_minutes,
    t.type_train,
    t.numero_train,

    -- Calculs utiles
    CASE
        WHEN r.statut = 'EN_ATTENTE' AND NOW() > r.date_expiration THEN 'EXPIRE'
        WHEN r.statut = 'CONFIRMEE' AND v.date_voyage < CURDATE() THEN 'VOYAGE_PASSE'
        ELSE r.statut
    END as statut_effectif

FROM reservations r
JOIN utilisateurs u ON r.utilisateur_id = u.id
JOIN voyages v ON r.voyage_id = v.id
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id;

-- Vue des statistiques générales
CREATE VIEW v_statistiques_generales AS
SELECT
    'Utilisateurs actifs' as metric,
    COUNT(*) as valeur,
    'nombre' as unite
FROM utilisateurs WHERE actif = TRUE

UNION ALL

SELECT
    'Gares actives',
    COUNT(*),
    'nombre'
FROM gares WHERE active = TRUE

UNION ALL

SELECT
    'Trajets actifs',
    COUNT(*),
    'nombre'
FROM trajets WHERE actif = TRUE

UNION ALL

SELECT
    'Voyages programmés',
    COUNT(*),
    'nombre'
FROM voyages WHERE statut = 'PROGRAMME' AND date_voyage >= CURDATE()

UNION ALL

SELECT
    'Réservations confirmées',
    COUNT(*),
    'nombre'
FROM reservations WHERE statut IN ('CONFIRMEE', 'PAYEE')

UNION ALL

SELECT
    'Chiffre d\'affaires',
    ROUND(SUM(prix_total), 2),
    'euros'
FROM reservations WHERE statut IN ('CONFIRMEE', 'PAYEE')

UNION ALL

SELECT
    'Taux occupation moyen',
    ROUND(AVG(taux_occupation), 2),
    'pourcentage'
FROM voyages WHERE date_voyage >= CURDATE() AND statut = 'PROGRAMME';

-- ========================================
-- CREATION DES PROCEDURES STOCKEES
-- ========================================

DELIMITER //

-- Procédure de recherche de voyages optimisée
CREATE PROCEDURE sp_rechercher_voyages(
    IN p_ville_depart VARCHAR(100),
    IN p_ville_arrivee VARCHAR(100),
    IN p_date_voyage DATE,
    IN p_limite INT DEFAULT 50
)
BEGIN
    SELECT
        v.voyage_id,
        v.date_voyage,
        v.places_disponibles,
        v.niveau_disponibilite,
        v.trajet_id,
        v.heure_depart,
        v.heure_arrivee,
        v.duree_minutes,
        v.prix_actuel,
        v.type_train,
        v.numero_train,
        v.gare_depart_nom,
        v.ville_depart,
        v.gare_arrivee_nom,
        v.ville_arrivee,
        v.taux_occupation
    FROM v_voyages_details v
    WHERE v.ville_depart LIKE CONCAT('%', p_ville_depart, '%')
    AND v.ville_arrivee LIKE CONCAT('%', p_ville_arrivee, '%')
    AND v.date_voyage = p_date_voyage
    AND v.places_disponibles > 0
    ORDER BY v.heure_depart
    LIMIT p_limite;
END//

-- Procédure de création de réservation
CREATE PROCEDURE sp_creer_reservation(
    IN p_utilisateur_id BIGINT,
    IN p_voyage_id BIGINT,
    IN p_nombre_places INT,
    IN p_code_reduction VARCHAR(20),
    OUT p_numero_reservation VARCHAR(20),
    OUT p_prix_total DECIMAL(10,2),
    OUT p_message VARCHAR(255)
)
BEGIN
    DECLARE v_prix_unitaire DECIMAL(10,2);
    DECLARE v_places_disponibles INT;
    DECLARE v_reduction DECIMAL(10,2) DEFAULT 0;
    DECLARE v_prix_total DECIMAL(10,2);
    DECLARE v_numero VARCHAR(20);
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_message = 'Erreur lors de la création de la réservation';
        SET p_numero_reservation = NULL;
        SET p_prix_total = 0;
    END;

    START TRANSACTION;

    -- Vérifier les places disponibles et récupérer le prix
    SELECT v.places_disponibles, COALESCE(v.prix_dynamique, t.prix)
    INTO v_places_disponibles, v_prix_unitaire
    FROM voyages v
    JOIN trajets t ON v.trajet_id = t.id
    WHERE v.id = p_voyage_id
    AND v.statut = 'PROGRAMME'
    FOR UPDATE;

    -- Vérifier si assez de places
    IF v_places_disponibles < p_nombre_places THEN
        SET p_message = CONCAT('Seulement ', v_places_disponibles, ' place(s) disponible(s)');
        ROLLBACK;
    ELSE
        -- Appliquer la réduction si code fourni
        IF p_code_reduction IS NOT NULL AND p_code_reduction != '' THEN
            CASE p_code_reduction
                WHEN 'ETUDIANT' THEN SET v_reduction = v_prix_unitaire * 0.25;
                WHEN 'SENIOR' THEN SET v_reduction = v_prix_unitaire * 0.20;
                WHEN 'FAMILLE' THEN SET v_reduction = v_prix_unitaire * 0.15;
                ELSE SET v_reduction = 0;
            END CASE;
        END IF;

        -- Calculer le prix total
        SET v_prix_total = (v_prix_unitaire * p_nombre_places) - (v_reduction * p_nombre_places);

        -- Générer un numéro de réservation unique
        SET v_numero = CONCAT('RES', YEAR(NOW()), LPAD(FLOOR(RAND() * 999999), 6, '0'));

        -- Insérer la réservation
        INSERT INTO reservations (
            numero_reservation,
            utilisateur_id,
            voyage_id,
            nombre_places,
            prix_unitaire,
            prix_total,
            code_reduction,
            reduction_appliquee,
            statut
        ) VALUES (
            v_numero,
            p_utilisateur_id,
            p_voyage_id,
            p_nombre_places,
            v_prix_unitaire,
            v_prix_total,
            p_code_reduction,
            v_reduction * p_nombre_places,
            'EN_ATTENTE'
        );

        SET p_numero_reservation = v_numero;
        SET p_prix_total = v_prix_total;
        SET p_message = 'Réservation créée avec succès';

        COMMIT;
    END IF;
END//

-- Procédure de confirmation de réservation
CREATE PROCEDURE sp_confirmer_reservation(
    IN p_numero_reservation VARCHAR(20),
    IN p_reference_paiement VARCHAR(100),
    OUT p_success BOOLEAN,
    OUT p_message VARCHAR(255)
)
BEGIN
    DECLARE v_count INT DEFAULT 0;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_success = FALSE;
        SET p_message = 'Erreur lors de la confirmation';
    END;

    START TRANSACTION;

    -- Vérifier que la réservation existe et est en attente
    SELECT COUNT(*) INTO v_count
    FROM reservations
    WHERE numero_reservation = p_numero_reservation
    AND statut = 'EN_ATTENTE'
    AND date_reservation > DATE_SUB(NOW(), INTERVAL 30 MINUTE);

    IF v_count = 0 THEN
        SET p_success = FALSE;
        SET p_message = 'Réservation non trouvée ou expirée';
        ROLLBACK;
    ELSE
        -- Confirmer la réservation
        UPDATE reservations
        SET statut = 'CONFIRMEE',
            date_confirmation = NOW(),
            reference_paiement = p_reference_paiement
        WHERE numero_reservation = p_numero_reservation;

        SET p_success = TRUE;
        SET p_message = 'Réservation confirmée avec succès';

        COMMIT;
    END IF;
END//

-- Procédure de statistiques détaillées
CREATE PROCEDURE sp_statistiques_detaillees()
BEGIN
    -- Statistiques générales
    SELECT * FROM v_statistiques_generales;

    -- Top 5 des trajets les plus réservés
    SELECT
        'TOP TRAJETS' as section,
        CONCAT(gd.ville, ' → ', ga.ville) as trajet,
        COUNT(r.id) as nb_reservations,
        SUM(r.prix_total) as ca_total
    FROM reservations r
    JOIN voyages v ON r.voyage_id = v.id
    JOIN trajets t ON v.trajet_id = t.id
    JOIN gares gd ON t.gare_depart_id = gd.id
    JOIN gares ga ON t.gare_arrivee_id = ga.id
    WHERE r.statut IN ('CONFIRMEE', 'PAYEE')
    GROUP BY t.id, gd.ville, ga.ville
    ORDER BY nb_reservations DESC
    LIMIT 5;

    -- Répartition par type de train
    SELECT
        'REPARTITION TRAINS' as section,
        t.type_train,
        COUNT(r.id) as nb_reservations,
        ROUND(AVG(r.prix_total), 2) as prix_moyen
    FROM reservations r
    JOIN voyages v ON r.voyage_id = v.id
    JOIN trajets t ON v.trajet_id = t.id
    WHERE r.statut IN ('CONFIRMEE', 'PAYEE')
    GROUP BY t.type_train
    ORDER BY nb_reservations DESC;
END//

DELIMITER ;

-- ========================================
-- ACTIVATION DES EVENEMENTS
-- ========================================
SET GLOBAL event_scheduler = ON;

-- ========================================
-- CONFIGURATION FINALE
-- ========================================
SET FOREIGN_KEY_CHECKS = 1;

-- Message de fin de migration
SELECT
    'MIGRATION TERMINEE AVEC SUCCES' as status,
    NOW() as date_migration,
    'Base de données train prête pour TrainSystem' as message;

-- Affichage de la structure créée
SELECT
    'TABLES CREEES' as info,
    TABLE_NAME as nom_table,
    TABLE_COMMENT as description
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_SCHEMA = 'train'
AND TABLE_TYPE = 'BASE TABLE'
ORDER BY TABLE_NAME;

COMMIT;
