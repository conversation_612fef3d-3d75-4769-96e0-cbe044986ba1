@echo off
echo ========================================
echo    MIGRATION AUTOMATIQUE BASE TRAIN
echo ========================================
echo.

cd /d "%~dp0"

REM Configuration de la base de données
set DB_HOST=localhost
set DB_PORT=3306
set DB_NAME=train
set DB_USER=root
set DB_PASSWORD=

REM Fichiers de migration
set MIGRATION_FILE=migration_train.sql
set DATA_FILE=insert_sample_data.sql

echo 1. Vérification des prérequis...

REM Vérifier si MySQL est accessible
mysql --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ MySQL n'est pas accessible depuis la ligne de commande
    echo.
    echo Solutions possibles :
    echo - Ajoutez MySQL au PATH système
    echo - Ou utilisez XAMPP/WAMP et leurs outils
    echo.
    echo Tentative avec les chemins XAMPP/WAMP...
    
    REM Essayer les chemins XAMPP/WAMP courants
    if exist "C:\xampp\mysql\bin\mysql.exe" (
        set MYSQL_PATH=C:\xampp\mysql\bin\mysql.exe
        echo ✅ MySQL trouvé dans XAMPP
        goto :mysql_found
    )
    
    if exist "C:\wamp64\bin\mysql\mysql8.0.31\bin\mysql.exe" (
        set MYSQL_PATH=C:\wamp64\bin\mysql\mysql8.0.31\bin\mysql.exe
        echo ✅ MySQL trouvé dans WAMP
        goto :mysql_found
    )
    
    if exist "C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" (
        set MYSQL_PATH="C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe"
        echo ✅ MySQL trouvé dans Program Files
        goto :mysql_found
    )
    
    echo ❌ MySQL non trouvé automatiquement
    echo.
    echo Veuillez :
    echo 1. Démarrer XAMPP/WAMP
    echo 2. Ou installer MySQL et l'ajouter au PATH
    echo 3. Ou utiliser phpMyAdmin manuellement
    echo.
    pause
    exit /b 1
) else (
    set MYSQL_PATH=mysql
    echo ✅ MySQL accessible
)

:mysql_found

echo 2. Test de connexion à MySQL...
%MYSQL_PATH% -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% -e "SELECT 1;" >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ Impossible de se connecter à MySQL
    echo.
    echo Vérifiez :
    echo - Que MySQL/MariaDB est démarré
    echo - Les paramètres de connexion (utilisateur/mot de passe)
    echo - Que le port 3306 est ouvert
    echo.
    
    REM Essayer sans mot de passe
    echo Tentative de connexion sans mot de passe...
    %MYSQL_PATH% -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -e "SELECT 1;" >nul 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ❌ Connexion échouée même sans mot de passe
        echo.
        echo Solutions :
        echo 1. Utilisez phpMyAdmin pour exécuter le script manuellement
        echo 2. Vérifiez que MySQL est démarré dans XAMPP/WAMP
        echo 3. Vérifiez les paramètres de connexion
        echo.
        pause
        exit /b 1
    ) else (
        echo ✅ Connexion réussie sans mot de passe
        set DB_PASSWORD=
    )
) else (
    echo ✅ Connexion MySQL réussie
)

echo 3. Vérification des fichiers de migration...
if not exist "%MIGRATION_FILE%" (
    echo ❌ Fichier de migration non trouvé : %MIGRATION_FILE%
    echo.
    echo Le fichier doit être dans le même dossier que ce script
    pause
    exit /b 1
)
echo ✅ Fichier de migration trouvé

echo 4. Création/Vérification de la base de données...
%MYSQL_PATH% -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% -e "CREATE DATABASE IF NOT EXISTS %DB_NAME% CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur lors de la création de la base de données
    pause
    exit /b 1
)
echo ✅ Base de données '%DB_NAME%' prête

echo 5. Exécution de la migration...
echo.
echo 🔄 Migration en cours... (cela peut prendre quelques minutes)
echo.

REM Exécuter le script de migration
%MYSQL_PATH% -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% %DB_NAME% < "%MIGRATION_FILE%"
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur lors de l'exécution de la migration
    echo.
    echo Vérifiez :
    echo - Le contenu du fichier SQL
    echo - Les permissions de la base de données
    echo - Les logs d'erreur MySQL
    echo.
    pause
    exit /b 1
)

echo ✅ Migration des tables terminée avec succès !

echo 6. Insertion des données de test...
if exist "%DATA_FILE%" (
    echo 🔄 Insertion des données de démonstration...
    %MYSQL_PATH% -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% %DB_NAME% < "%DATA_FILE%"
    if %ERRORLEVEL% neq 0 (
        echo ⚠️ Erreur lors de l'insertion des données de test
        echo La structure est créée mais sans données de démonstration
    ) else (
        echo ✅ Données de test insérées
    )
) else (
    echo ⚠️ Fichier de données de test non trouvé : %DATA_FILE%
    echo La migration continue sans données de démonstration
)

echo 7. Vérification de la migration...
echo.
echo 📊 Vérification des tables créées :

%MYSQL_PATH% -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% %DB_NAME% -e "SHOW TABLES;"
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur lors de la vérification
) else (
    echo.
    echo 📈 Statistiques de la base :
    %MYSQL_PATH% -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% %DB_NAME% -e "SELECT TABLE_NAME as 'Table', TABLE_ROWS as 'Lignes', ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) as 'Taille_MB' FROM information_schema.TABLES WHERE TABLE_SCHEMA = '%DB_NAME%' ORDER BY TABLE_NAME;"
)

echo.
echo ========================================
echo    MIGRATION TERMINEE !
echo ========================================
echo.
echo ✅ Base de données '%DB_NAME%' migrée avec succès
echo.
echo 🎯 Prochaines étapes :
echo   1. Testez la connexion depuis votre application
echo   2. Vérifiez les données dans phpMyAdmin
echo   3. Lancez votre application TrainSystem
echo.
echo 🌐 Accès phpMyAdmin :
echo   URL : http://localhost/phpmyadmin
echo   Base : %DB_NAME%
echo.
echo 📋 Tables créées :
echo   • gares (gares ferroviaires)
echo   • utilisateurs (comptes utilisateurs)  
echo   • trajets (liaisons entre gares)
echo   • voyages (instances de trajets)
echo   • reservations (réservations de billets)
echo.
echo 🔧 Fonctionnalités disponibles :
echo   • Vues optimisées pour les requêtes
echo   • Procédures stockées pour les opérations
echo   • Triggers automatiques pour la cohérence
echo   • Index pour les performances
echo.

pause
