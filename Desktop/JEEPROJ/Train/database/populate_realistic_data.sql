-- ========================================
-- SCRIPT DE REMPLISSAGE AVEC DONNEES REALISTES
-- Base de données TrainSystem
-- ========================================

USE train;

-- Désactiver les vérifications temporairement
SET FOREIGN_KEY_CHECKS = 0;
SET AUTOCOMMIT = 0;

-- Vider les tables existantes
TRUNCATE TABLE reservations;
TRUNCATE TABLE voyages;
TRUNCATE TABLE trajets;
TRUNCATE TABLE utilisateurs;
TRUNCATE TABLE gares;

-- Réactiver les vérifications
SET FOREIGN_KEY_CHECKS = 1;

-- ========================================
-- INSERTION DES GARES PRINCIPALES FRANÇAISES
-- ========================================

INSERT INTO gares (nom, ville, code_gare, adresse, code_postal, latitude, longitude, active) VALUES
-- Grandes gares parisiennes
('Gare du Nord', 'Paris', 'FRPNO', '18 Rue de Dunkerque', '75010', 48.8809, 2.3553, TRUE),
('Gare de Lyon', 'Paris', 'FRPLY', 'Place Louis-Armand', '75012', 48.8447, 2.3739, TRUE),
('Gare Montparnasse', 'Paris', 'FRPMO', '17 Boulevard de Vaugirard', '75015', 48.8404, 2.3188, TRUE),
('Gare de l\'Est', 'Paris', 'FRPES', 'Place du 11 Novembre 1918', '75010', 48.8766, 2.3590, TRUE),

-- Grandes villes françaises
('Gare Part-Dieu', 'Lyon', 'FRLYP', 'Place Charles Béraudier', '69003', 48.8604, 2.3370, TRUE),
('Gare Saint-Charles', 'Marseille', 'FRMSC', 'Square Narvik', '13001', 43.3030, 5.3808, TRUE),
('Gare Matabiau', 'Toulouse', 'FRTMA', '64 Boulevard Pierre Sémard', '31000', 43.6108, 1.4537, TRUE),
('Gare Saint-Jean', 'Bordeaux', 'FRBSJ', 'Rue Charles Domercq', '33800', 44.8258, -0.5570, TRUE),
('Gare Centrale', 'Lille', 'FRLCE', 'Place François Mitterrand', '59000', 50.6365, 3.0635, TRUE),
('Gare Centrale', 'Strasbourg', 'FRSCE', '20 Place de la Gare', '67000', 48.5847, 7.7339, TRUE),

-- Villes moyennes
('Gare SNCF', 'Nantes', 'FRNAN', '27 Boulevard de Stalingrad', '44000', 47.2173, -1.5534, TRUE),
('Gare SNCF', 'Rennes', 'FRREN', 'Place de la Gare', '35000', 48.1030, -1.6720, TRUE),
('Gare SNCF', 'Nice', 'FRNIC', 'Avenue Thiers', '06000', 43.7034, 7.2619, TRUE),
('Gare SNCF', 'Montpellier', 'FRMON', 'Place Auguste Gibert', '34000', 43.6058, 3.8808, TRUE),
('Gare SNCF', 'Dijon', 'FRDIJ', 'Cour de la Gare', '21000', 47.3236, 5.0279, TRUE);

-- ========================================
-- INSERTION DES UTILISATEURS REALISTES
-- ========================================

INSERT INTO utilisateurs (nom, prenom, email, mot_de_passe, telephone, date_naissance, adresse, ville, code_postal, type_utilisateur, actif) VALUES

-- ADMINISTRATEURS
('Dubois', 'Alexandre', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456789', '1980-03-15', '1 Rue de la Gare', 'Paris', '75001', 'ADMINISTRATEUR', TRUE),
('Martin', 'Sophie', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456788', '1975-07-22', '2 Avenue des Trains', 'Lyon', '69001', 'ADMINISTRATEUR', TRUE),

-- EMPLOYES
('Leroy', 'Pierre', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456787', '1985-11-10', '15 Rue du Rail', 'Marseille', '13001', 'EMPLOYE', TRUE),
('Moreau', 'Julie', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456786', '1990-02-28', '8 Place de la Station', 'Toulouse', '31000', 'EMPLOYE', TRUE),

-- CLIENTS ACTIFS
('Dupont', 'Jean', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456790', '1988-05-12', '25 Rue de la République', 'Paris', '75011', 'CLIENT', TRUE),
('Bernard', 'Marie', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456791', '1992-09-18', '42 Avenue des Champs', 'Lyon', '69002', 'CLIENT', TRUE),
('Petit', 'Paul', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456792', '1985-12-03', '17 Boulevard Victor Hugo', 'Marseille', '13002', 'CLIENT', TRUE),
('Robert', 'Claire', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456793', '1990-04-25', '33 Rue Nationale', 'Toulouse', '31001', 'CLIENT', TRUE),
('Richard', 'Thomas', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456794', '1987-08-14', '9 Place Bellecour', 'Lyon', '69001', 'CLIENT', TRUE),
('Durand', 'Emma', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456795', '1993-01-30', '55 Rue de Rivoli', 'Paris', '75001', 'CLIENT', TRUE),

-- CLIENTS SUPPLEMENTAIRES
('Roux', 'Lucas', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456796', '1989-06-07', '12 Avenue de la Liberté', 'Bordeaux', '33000', 'CLIENT', TRUE),
('Vincent', 'Léa', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456797', '1991-10-22', '28 Rue du Commerce', 'Lille', '59000', 'CLIENT', TRUE),
('Fournier', 'Antoine', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456798', '1986-03-11', '7 Place de la Comédie', 'Montpellier', '34000', 'CLIENT', TRUE),
('Girard', 'Camille', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456799', '1994-07-19', '41 Rue Masséna', 'Nice', '06000', 'CLIENT', TRUE),
('Bonnet', 'Hugo', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456800', '1988-11-05', '16 Cours Mirabeau', 'Strasbourg', '67000', 'CLIENT', TRUE),

-- CLIENTS INACTIFS (pour tester la gestion)
('Blanc', 'Julien', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456801', '1983-12-25', '22 Rue de la Paix', 'Nantes', '44000', 'CLIENT', FALSE),
('Gauthier', 'Manon', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456802', '1995-04-08', '35 Avenue Jean Jaurès', 'Rennes', '35000', 'CLIENT', FALSE);

-- ========================================
-- INSERTION DES TRAJETS REALISTES
-- ========================================

INSERT INTO trajets (gare_depart_id, gare_arrivee_id, heure_depart, heure_arrivee, prix, nombre_places, type_train, actif) VALUES

-- LIAISONS TGV PRINCIPALES (Paris vers grandes villes)
(1, 5, '07:15:00', '09:17:00', 89.00, 380, 'TGV', TRUE),    -- Paris Nord -> Lyon Part-Dieu
(2, 6, '08:30:00', '11:45:00', 135.00, 380, 'TGV', TRUE),   -- Paris Lyon -> Marseille
(3, 8, '09:00:00', '12:15:00', 125.00, 380, 'TGV', TRUE),   -- Paris Montparnasse -> Bordeaux
(3, 7, '10:30:00', '15:45:00', 110.00, 380, 'TGV', TRUE),   -- Paris Montparnasse -> Toulouse
(1, 9, '06:45:00', '07:45:00', 45.00, 380, 'TGV', TRUE),    -- Paris Nord -> Lille
(4, 10, '08:00:00', '11:30:00', 95.00, 380, 'TGV', TRUE),   -- Paris Est -> Strasbourg

-- LIAISONS RETOUR
(5, 1, '18:30:00', '20:32:00', 89.00, 380, 'TGV', TRUE),    -- Lyon -> Paris Nord
(6, 2, '17:15:00', '20:30:00', 135.00, 380, 'TGV', TRUE),   -- Marseille -> Paris Lyon
(8, 3, '16:45:00', '20:00:00', 125.00, 380, 'TGV', TRUE),   -- Bordeaux -> Paris Montparnasse
(7, 3, '14:15:00', '19:30:00', 110.00, 380, 'TGV', TRUE),   -- Toulouse -> Paris Montparnasse
(9, 1, '19:15:00', '20:15:00', 45.00, 380, 'TGV', TRUE),    -- Lille -> Paris Nord
(10, 4, '17:30:00', '21:00:00', 95.00, 380, 'TGV', TRUE),   -- Strasbourg -> Paris Est

-- LIAISONS INTER-REGIONALES
(5, 6, '12:00:00', '14:30:00', 65.00, 280, 'TGV', TRUE),    -- Lyon -> Marseille
(6, 5, '15:30:00', '18:00:00', 65.00, 280, 'TGV', TRUE),    -- Marseille -> Lyon
(5, 7, '11:15:00', '15:45:00', 75.00, 280, 'TGV', TRUE),    -- Lyon -> Toulouse
(8, 7, '13:30:00', '15:45:00', 45.00, 280, 'TER', TRUE),    -- Bordeaux -> Toulouse
(11, 12, '08:45:00', '10:30:00', 35.00, 200, 'TER', TRUE),  -- Nantes -> Rennes
(12, 11, '17:00:00', '18:45:00', 35.00, 200, 'TER', TRUE),  -- Rennes -> Nantes

-- LIAISONS LOCALES ET TER
(13, 6, '09:30:00', '12:00:00', 55.00, 200, 'TER', TRUE),   -- Nice -> Marseille
(14, 5, '10:15:00', '11:45:00', 25.00, 200, 'TER', TRUE),   -- Montpellier -> Lyon
(15, 10, '07:30:00', '11:00:00', 85.00, 200, 'TER', TRUE),  -- Dijon -> Strasbourg

-- TRAJETS SUPPLEMENTAIRES POUR PLUS DE REALISME
(1, 11, '14:20:00', '16:45:00', 75.00, 380, 'TGV', TRUE),   -- Paris Nord -> Nantes
(11, 1, '12:30:00', '14:55:00', 75.00, 380, 'TGV', TRUE),   -- Nantes -> Paris Nord
(2, 13, '22:15:00', '08:30:00', 145.00, 200, 'INTERCITES', TRUE), -- Paris Lyon -> Nice (nuit)
(5, 8, '06:30:00', '11:15:00', 95.00, 280, 'TGV', TRUE);    -- Lyon -> Bordeaux

-- ========================================
-- GENERATION DES VOYAGES (15 prochains jours)
-- ========================================

INSERT INTO voyages (trajet_id, date_voyage, places_disponibles, places_reservees, statut)
SELECT 
    t.id,
    DATE_ADD(CURDATE(), INTERVAL d.day_offset DAY),
    t.nombre_places - FLOOR(RAND() * 50), -- Places disponibles (simuler quelques réservations)
    FLOOR(RAND() * 50), -- Places réservées
    'PROGRAMME'
FROM trajets t
CROSS JOIN (
    SELECT 0 as day_offset UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL 
    SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL 
    SELECT 9 UNION ALL SELECT 10 UNION ALL SELECT 11 UNION ALL SELECT 12 UNION ALL SELECT 13 UNION ALL SELECT 14
) d
WHERE t.actif = TRUE;

-- ========================================
-- INSERTION DES RESERVATIONS REALISTES
-- ========================================

-- Réservations confirmées récentes
INSERT INTO reservations (numero_reservation, utilisateur_id, voyage_id, nombre_places, prix_total, statut, mode_paiement, date_reservation) VALUES

-- Réservations d'aujourd'hui
('RES2024001', 5, 1, 2, 178.00, 'CONFIRMEE', 'CARTE_BANCAIRE', NOW()),
('RES2024002', 6, 15, 1, 89.00, 'CONFIRMEE', 'PAYPAL', NOW()),
('RES2024003', 7, 8, 3, 375.00, 'CONFIRMEE', 'CARTE_BANCAIRE', NOW()),

-- Réservations d'hier
('RES2024004', 8, 22, 1, 125.00, 'CONFIRMEE', 'VIREMENT', DATE_SUB(NOW(), INTERVAL 1 DAY)),
('RES2024005', 9, 5, 2, 90.00, 'CONFIRMEE', 'CARTE_BANCAIRE', DATE_SUB(NOW(), INTERVAL 1 DAY)),
('RES2024006', 10, 12, 1, 95.00, 'EN_ATTENTE', 'CARTE_BANCAIRE', DATE_SUB(NOW(), INTERVAL 1 DAY)),

-- Réservations de cette semaine
('RES2024007', 11, 18, 4, 440.00, 'CONFIRMEE', 'CARTE_BANCAIRE', DATE_SUB(NOW(), INTERVAL 2 DAY)),
('RES2024008', 12, 25, 1, 75.00, 'CONFIRMEE', 'PAYPAL', DATE_SUB(NOW(), INTERVAL 3 DAY)),
('RES2024009', 13, 30, 2, 270.00, 'CONFIRMEE', 'CARTE_BANCAIRE', DATE_SUB(NOW(), INTERVAL 4 DAY)),
('RES2024010', 14, 7, 1, 45.00, 'CONFIRMEE', 'CARTE_BANCAIRE', DATE_SUB(NOW(), INTERVAL 5 DAY)),

-- Réservations en attente (pour tester la gestion)
('RES2024011', 15, 35, 2, 150.00, 'EN_ATTENTE', 'VIREMENT', DATE_SUB(NOW(), INTERVAL 1 DAY)),
('RES2024012', 5, 42, 1, 135.00, 'EN_ATTENTE', 'CARTE_BANCAIRE', NOW()),
('RES2024013', 6, 48, 3, 285.00, 'EN_ATTENTE', 'PAYPAL', NOW()),

-- Réservations annulées (pour tester les remboursements)
('RES2024014', 7, 55, 1, 89.00, 'ANNULEE', 'CARTE_BANCAIRE', DATE_SUB(NOW(), INTERVAL 2 DAY)),
('RES2024015', 8, 62, 2, 250.00, 'ANNULEE', 'PAYPAL', DATE_SUB(NOW(), INTERVAL 3 DAY)),

-- Réservations pour voyages futurs
('RES2024016', 9, 75, 2, 178.00, 'CONFIRMEE', 'CARTE_BANCAIRE', DATE_SUB(NOW(), INTERVAL 1 DAY)),
('RES2024017', 10, 82, 1, 125.00, 'CONFIRMEE', 'VIREMENT', DATE_SUB(NOW(), INTERVAL 2 DAY)),
('RES2024018', 11, 89, 4, 500.00, 'CONFIRMEE', 'CARTE_BANCAIRE', DATE_SUB(NOW(), INTERVAL 1 DAY)),
('RES2024019', 12, 96, 1, 75.00, 'CONFIRMEE', 'PAYPAL', NOW()),
('RES2024020', 13, 103, 2, 190.00, 'CONFIRMEE', 'CARTE_BANCAIRE', NOW());

-- ========================================
-- MISE A JOUR DES STATISTIQUES
-- ========================================

-- Mettre à jour les places disponibles dans les voyages
UPDATE voyages v
SET places_disponibles = (
    SELECT t.nombre_places - COALESCE(SUM(r.nombre_places), 0)
    FROM trajets t
    LEFT JOIN reservations r ON r.voyage_id = v.id AND r.statut = 'CONFIRMEE'
    WHERE t.id = v.trajet_id
    GROUP BY t.id
)
WHERE v.id IN (SELECT DISTINCT voyage_id FROM reservations WHERE statut = 'CONFIRMEE');

-- Mettre à jour les dernières connexions des utilisateurs
UPDATE utilisateurs
SET derniere_connexion = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY)
WHERE actif = TRUE AND type_utilisateur = 'CLIENT';

-- Mettre à jour les connexions récentes pour les admins
UPDATE utilisateurs
SET derniere_connexion = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 7) DAY)
WHERE type_utilisateur IN ('ADMINISTRATEUR', 'EMPLOYE');

-- ========================================
-- DONNEES SUPPLEMENTAIRES POUR TESTS ADMIN
-- ========================================

-- Ajouter quelques voyages avec statuts différents
UPDATE voyages SET statut = 'COMPLET' WHERE id IN (1, 15, 22, 35);
UPDATE voyages SET statut = 'ANNULE' WHERE id IN (8, 42);
UPDATE voyages SET statut = 'RETARDE' WHERE id IN (25, 48);

-- Ajouter des réservations avec différents modes de paiement
INSERT INTO reservations (numero_reservation, utilisateur_id, voyage_id, nombre_places, prix_total, statut, mode_paiement, date_reservation) VALUES
('RES2024021', 14, 110, 1, 89.00, 'CONFIRMEE', 'ESPECES', DATE_SUB(NOW(), INTERVAL 1 DAY)),
('RES2024022', 15, 117, 2, 220.00, 'CONFIRMEE', 'CHEQUE', DATE_SUB(NOW(), INTERVAL 2 DAY)),
('RES2024023', 5, 124, 1, 125.00, 'REMBOURSEE', 'CARTE_BANCAIRE', DATE_SUB(NOW(), INTERVAL 5 DAY));

-- ========================================
-- VERIFICATION DES DONNEES INSEREES
-- ========================================

COMMIT;

SELECT 'DONNEES REALISTES INSEREES AVEC SUCCES' as message;

SELECT
    'Gares' as table_name,
    COUNT(*) as nombre_enregistrements
FROM gares
UNION ALL
SELECT 'Utilisateurs', COUNT(*) FROM utilisateurs
UNION ALL
SELECT 'Trajets', COUNT(*) FROM trajets
UNION ALL
SELECT 'Voyages', COUNT(*) FROM voyages
UNION ALL
SELECT 'Réservations', COUNT(*) FROM reservations;

-- Afficher les comptes de test disponibles
SELECT
    'COMPTES DE TEST DISPONIBLES' as info,
    CONCAT(prenom, ' ', nom) as nom_complet,
    email,
    type_utilisateur as role,
    'password' as mot_de_passe
FROM utilisateurs
WHERE type_utilisateur IN ('ADMINISTRATEUR', 'EMPLOYE')
ORDER BY type_utilisateur, email;

-- Statistiques pour le dashboard admin
SELECT
    'STATISTIQUES DASHBOARD' as info,
    (SELECT COUNT(*) FROM utilisateurs WHERE actif = TRUE) as utilisateurs_actifs,
    (SELECT COUNT(*) FROM utilisateurs WHERE type_utilisateur = 'CLIENT') as clients,
    (SELECT COUNT(*) FROM voyages WHERE date_voyage >= CURDATE()) as voyages_futurs,
    (SELECT COUNT(*) FROM reservations WHERE statut = 'CONFIRMEE') as reservations_confirmees,
    (SELECT COUNT(*) FROM reservations WHERE statut = 'EN_ATTENTE') as reservations_en_attente;

SET FOREIGN_KEY_CHECKS = 1;
