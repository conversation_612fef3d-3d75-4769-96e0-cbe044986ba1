-- ========================================
-- DONNEES DE TEST POUR TOUTES LES FONCTIONNALITES
-- Script complet pour tester recherche, réservation, gestion
-- ========================================

USE train;

-- ========================================
-- TRAJETS SUPPLEMENTAIRES POUR TESTS
-- ========================================

-- Ajouter plus de trajets pour enrichir les tests
INSERT INTO trajets (gare_depart_id, gare_arrivee_id, heure_depart, heure_arrivee, prix, nombre_places, type_train) VALUES

-- Trajets matinaux (6h-10h)
(1, 2, '06:30:00', '11:00:00', 89.50, 200, 'TGV'),     -- Paris -> Lyon (matin)
(1, 3, '07:00:00', '13:30:00', 125.00, 150, 'TGV'),    -- Paris -> Bordeaux (matin)
(1, 4, '06:15:00', '15:00:00', 135.00, 180, 'TGV'),    -- Paris -> Marseille (matin)
(2, 1, '06:45:00', '11:15:00', 89.50, 200, 'TGV'),     -- Lyon -> Paris (matin)

-- Trajets de midi (11h-15h)
(1, 2, '12:00:00', '16:30:00', 89.50, 200, 'TGV'),     -- Paris -> Lyon (midi)
(2, 4, '11:30:00', '17:15:00', 95.00, 180, 'TGV'),     -- Lyon -> Marseille (midi)
(4, 8, '12:30:00', '15:45:00', 55.00, 120, 'TER'),     -- Marseille -> Nice (midi)

-- Trajets de soirée (18h-22h)
(1, 2, '19:00:00', '23:30:00', 89.50, 200, 'TGV'),     -- Paris -> Lyon (soir)
(2, 1, '20:15:00', '00:45:00', 89.50, 200, 'TGV'),     -- Lyon -> Paris (soir)
(1, 4, '18:30:00', '03:15:00', 135.00, 180, 'TGV'),    -- Paris -> Marseille (nuit)

-- Trajets OUIGO (low-cost)
(1, 2, '05:45:00', '10:45:00', 45.00, 250, 'OUIGO'),   -- Paris -> Lyon (OUIGO)
(2, 1, '21:30:00', '02:30:00', 45.00, 250, 'OUIGO'),   -- Lyon -> Paris (OUIGO)
(1, 4, '06:15:00', '15:45:00', 65.00, 220, 'OUIGO'),   -- Paris -> Marseille (OUIGO)

-- Trajets régionaux fréquents
(4, 8, '07:00:00', '10:15:00', 55.00, 120, 'TER'),     -- Marseille -> Nice (matin)
(4, 8, '14:00:00', '17:15:00', 55.00, 120, 'TER'),     -- Marseille -> Nice (après-midi)
(4, 8, '19:45:00', '23:00:00', 55.00, 120, 'TER'),     -- Marseille -> Nice (soir)
(8, 4, '08:30:00', '11:45:00', 55.00, 120, 'TER'),     -- Nice -> Marseille (matin)
(8, 4, '15:30:00', '18:45:00', 55.00, 120, 'TER'),     -- Nice -> Marseille (après-midi)
(8, 4, '20:15:00', '23:30:00', 55.00, 120, 'TER');     -- Nice -> Marseille (soir)

-- ========================================
-- VOYAGES POUR LES 14 PROCHAINS JOURS
-- ========================================

-- Supprimer les anciens voyages de test
DELETE FROM voyages WHERE id > (SELECT MAX(id) FROM (SELECT id FROM voyages) as temp);

-- Générer des voyages pour les 14 prochains jours (au lieu de 7)
INSERT INTO voyages (trajet_id, date_voyage, places_disponibles)
SELECT 
    t.id,
    DATE_ADD(CURDATE(), INTERVAL d.day_offset DAY),
    t.nombre_places
FROM trajets t
CROSS JOIN (
    SELECT 0 as day_offset UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL
    SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL
    SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10 UNION ALL SELECT 11 UNION ALL
    SELECT 12 UNION ALL SELECT 13
) d
WHERE t.actif = TRUE
ON DUPLICATE KEY UPDATE places_disponibles = t.nombre_places;

-- ========================================
-- RESERVATIONS REALISTES POUR TESTS
-- ========================================

-- Supprimer les anciennes réservations de test
DELETE FROM reservations WHERE numero_reservation LIKE 'DEMO%';

-- Générer des réservations réalistes
INSERT INTO reservations (numero_reservation, utilisateur_id, voyage_id, nombre_places, prix_total, statut, date_reservation, mode_paiement, reference_paiement) 

-- Réservations confirmées récentes
SELECT 
    CONCAT('DEMO', LPAD(ROW_NUMBER() OVER (ORDER BY v.id), 4, '0')),
    u.id,
    v.id,
    FLOOR(RAND() * 3) + 1,  -- 1 à 3 places
    t.prix * (FLOOR(RAND() * 3) + 1),
    'CONFIRMEE',
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY),
    CASE 
        WHEN RAND() < 0.6 THEN 'CARTE_BANCAIRE'
        WHEN RAND() < 0.8 THEN 'PAYPAL'
        ELSE 'VIREMENT'
    END,
    CONCAT('PAY', FLOOR(RAND() * 999999))
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
JOIN utilisateurs u ON u.type_utilisateur = 'CLIENT' AND u.actif = TRUE
WHERE v.date_voyage >= CURDATE()
AND RAND() < 0.2  -- 20% des voyages ont des réservations
LIMIT 30;

-- Réservations en attente (récentes)
INSERT INTO reservations (numero_reservation, utilisateur_id, voyage_id, nombre_places, prix_total, statut, date_reservation, mode_paiement) 
SELECT 
    CONCAT('PEND', LPAD(ROW_NUMBER() OVER (ORDER BY v.id), 3, '0')),
    u.id,
    v.id,
    FLOOR(RAND() * 2) + 1,  -- 1 à 2 places
    t.prix * (FLOOR(RAND() * 2) + 1),
    'EN_ATTENTE',
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 7) DAY),
    'CARTE_BANCAIRE'
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
JOIN utilisateurs u ON u.type_utilisateur = 'CLIENT' AND u.actif = TRUE
WHERE v.date_voyage >= DATE_ADD(CURDATE(), INTERVAL 1 DAY)
AND v.id NOT IN (SELECT voyage_id FROM reservations WHERE numero_reservation LIKE 'DEMO%')
AND RAND() < 0.1  -- 10% des voyages restants
LIMIT 10;

-- Quelques réservations annulées pour l'historique
INSERT INTO reservations (numero_reservation, utilisateur_id, voyage_id, nombre_places, prix_total, statut, date_reservation, date_annulation, motif_annulation, mode_paiement) 
SELECT 
    CONCAT('CANC', LPAD(ROW_NUMBER() OVER (ORDER BY v.id), 3, '0')),
    u.id,
    v.id,
    FLOOR(RAND() * 2) + 1,
    t.prix * (FLOOR(RAND() * 2) + 1),
    'ANNULEE',
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 60) DAY),
    DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY),
    CASE 
        WHEN RAND() < 0.3 THEN 'Changement de programme'
        WHEN RAND() < 0.6 THEN 'Problème personnel'
        ELSE 'Voyage reporté'
    END,
    'CARTE_BANCAIRE'
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
JOIN utilisateurs u ON u.type_utilisateur = 'CLIENT' AND u.actif = TRUE
WHERE v.date_voyage <= CURDATE()
AND RAND() < 0.05  -- 5% des voyages passés
LIMIT 5;

-- ========================================
-- MISE A JOUR DES PLACES ET STATISTIQUES
-- ========================================

-- Mettre à jour les places réservées pour tous les voyages
UPDATE voyages v 
SET places_reservees = (
    SELECT COALESCE(SUM(r.nombre_places), 0)
    FROM reservations r 
    WHERE r.voyage_id = v.id AND r.statut IN ('CONFIRMEE', 'EN_ATTENTE')
),
places_disponibles = (
    SELECT t.nombre_places - COALESCE(SUM(r.nombre_places), 0)
    FROM trajets t
    LEFT JOIN reservations r ON r.voyage_id = v.id AND r.statut IN ('CONFIRMEE', 'EN_ATTENTE')
    WHERE t.id = v.trajet_id
);

-- Mettre à jour les dates de confirmation pour les réservations confirmées
UPDATE reservations 
SET date_confirmation = DATE_ADD(date_reservation, INTERVAL FLOOR(RAND() * 24) HOUR)
WHERE statut = 'CONFIRMEE' AND date_confirmation IS NULL;

-- ========================================
-- VUES DE TEST POUR VALIDATION
-- ========================================

-- Vue pour les voyages populaires
CREATE OR REPLACE VIEW v_voyages_populaires AS
SELECT 
    CONCAT(gd.ville, ' → ', ga.ville) as trajet,
    t.type_train,
    COUNT(r.id) as nb_reservations,
    SUM(r.nombre_places) as places_reservees,
    SUM(r.prix_total) as chiffre_affaires,
    AVG(r.prix_total) as prix_moyen
FROM trajets t
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
JOIN voyages v ON v.trajet_id = t.id
LEFT JOIN reservations r ON r.voyage_id = v.id AND r.statut = 'CONFIRMEE'
GROUP BY t.id, gd.ville, ga.ville, t.type_train
HAVING nb_reservations > 0
ORDER BY nb_reservations DESC;

-- Vue pour les statistiques utilisateurs
CREATE OR REPLACE VIEW v_stats_utilisateurs AS
SELECT 
    u.id,
    CONCAT(u.prenom, ' ', u.nom) as nom_complet,
    u.email,
    u.type_utilisateur,
    COUNT(r.id) as nb_reservations,
    SUM(CASE WHEN r.statut = 'CONFIRMEE' THEN r.prix_total ELSE 0 END) as total_depense,
    MAX(r.date_reservation) as derniere_reservation,
    u.derniere_connexion
FROM utilisateurs u
LEFT JOIN reservations r ON r.utilisateur_id = u.id
WHERE u.actif = TRUE
GROUP BY u.id, u.nom, u.prenom, u.email, u.type_utilisateur, u.derniere_connexion
ORDER BY total_depense DESC;

-- ========================================
-- REQUETES DE VERIFICATION
-- ========================================

-- Statistiques générales
SELECT 'STATISTIQUES GENERALES' as titre;

SELECT 
    'Utilisateurs actifs' as metric, 
    COUNT(*) as valeur 
FROM utilisateurs 
WHERE actif = TRUE

UNION ALL

SELECT 
    'Trajets disponibles', 
    COUNT(*) 
FROM trajets 
WHERE actif = TRUE

UNION ALL

SELECT 
    'Voyages programmés', 
    COUNT(*) 
FROM voyages 
WHERE date_voyage >= CURDATE() AND statut = 'PROGRAMME'

UNION ALL

SELECT 
    'Réservations confirmées', 
    COUNT(*) 
FROM reservations 
WHERE statut = 'CONFIRMEE'

UNION ALL

SELECT 
    'Chiffre d\'affaires total (€)', 
    ROUND(SUM(prix_total), 2) 
FROM reservations 
WHERE statut = 'CONFIRMEE';

-- Voyages avec le plus de réservations
SELECT 'VOYAGES LES PLUS RESERVES' as titre;
SELECT * FROM v_voyages_populaires LIMIT 10;

-- Utilisateurs les plus actifs
SELECT 'UTILISATEURS LES PLUS ACTIFS' as titre;
SELECT * FROM v_stats_utilisateurs WHERE nb_reservations > 0 LIMIT 10;

-- Vérification des données de test
SELECT 'VERIFICATION DES DONNEES DE TEST' as titre;

SELECT 
    'Comptes de test' as type,
    COUNT(*) as nombre
FROM utilisateurs 
WHERE email LIKE '%@train.com' OR email LIKE '%@test.com'

UNION ALL

SELECT 
    'Réservations de test',
    COUNT(*)
FROM reservations 
WHERE numero_reservation LIKE 'DEMO%' OR numero_reservation LIKE 'TEST%' OR numero_reservation LIKE 'PEND%' OR numero_reservation LIKE 'CANC%';

COMMIT;
