-- ========================================
-- DONNEES DE TEST POUR BASE TRAIN
-- Insertion automatique des données de démonstration
-- ========================================

USE train;

-- ========================================
-- INSERTION DES GARES
-- ========================================
INSERT INTO gares (nom, ville, code_gare, adresse, code_postal, latitude, longitude, capacite_max) VALUES
('Gare de Paris-Montparnasse', 'Paris', 'PAR01', '17 Boulevard de Vaugirard', '75015', 48.8404, 2.3188, 1500),
('Gare de Lyon Part-Dieu', 'Lyon', 'LYO01', '3 Place Charles Béraudier', '69003', 45.7606, 4.8598, 1200),
('Gare Saint-Jean', 'Bordeaux', 'BOR01', 'Cours de la Marne', '33800', 44.8258, -0.5570, 800),
('Gare Saint-Charles', 'Marseille', 'MAR01', 'Square Narvik', '13001', 43.3030, 5.3811, 1000),
('Gare Matabiau', 'Toulouse', 'TOU01', '64 Boulevard Pierre Semard', '31000', 43.6108, 1.4537, 900),
('Gare Centrale', 'Strasbourg', 'STR01', '20 Place de la Gare', '67000', 48.5847, 7.7339, 700),
('Gare Lille-Flandres', 'Lille', 'LIL01', '3 Place des Buisses', '59000', 50.6365, 3.0708, 600),
('Gare de Nice-Ville', 'Nice', 'NIC01', 'Avenue Thiers', '06000', 43.7048, 7.2620, 500),
('Gare de Nantes', 'Nantes', 'NAN01', '27 Boulevard de Stalingrad', '44000', 47.2173, -1.5412, 750),
('Gare de Rennes', 'Rennes', 'REN01', '20 Place de la Gare', '35000', 48.1030, -1.6720, 650),
('Gare de Montpellier', 'Montpellier', 'MTP01', '1 Place Auguste Gibert', '34000', 43.6047, 3.8767, 550),
('Gare de Dijon', 'Dijon', 'DIJ01', 'Cour de la Gare', '21000', 47.3236, 5.0279, 450);

-- ========================================
-- INSERTION DES UTILISATEURS
-- ========================================
-- Mot de passe pour tous: "password" (haché avec BCrypt)
INSERT INTO utilisateurs (nom, prenom, email, mot_de_passe, telephone, date_naissance, adresse, ville, code_postal, type_utilisateur, actif, email_verifie) VALUES

-- ADMINISTRATEURS
('Admin', 'Principal', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456789', '1980-01-15', '1 Rue de la Gare', 'Paris', '75001', 'ADMINISTRATEUR', TRUE, TRUE),
('Super', 'Admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456788', '1975-05-20', '2 Avenue des Trains', 'Lyon', '69001', 'ADMINISTRATEUR', TRUE, TRUE),

-- EMPLOYES
('Employe', 'SNCF', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456787', '1985-03-10', '3 Boulevard SNCF', 'Marseille', '13001', 'EMPLOYE', TRUE, TRUE),
('Agent', 'Gare', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456786', '1990-07-25', '4 Place de la Station', 'Bordeaux', '33000', 'EMPLOYE', TRUE, TRUE),

-- CLIENTS DE TEST
('Client', 'Test', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456785', '1992-12-05', '5 Rue du Voyage', 'Toulouse', '31000', 'CLIENT', TRUE, TRUE),
('Demo', 'User', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456784', '1988-09-18', '6 Avenue des Voyageurs', 'Strasbourg', '67000', 'CLIENT', TRUE, TRUE),
('Utilisateur', 'Test', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456783', '1995-04-12', '7 Boulevard des Trains', 'Lille', '59000', 'CLIENT', TRUE, TRUE),

-- CLIENTS AVEC HISTORIQUE
('Dupont', 'Jean', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456782', '1987-11-30', '8 Rue de la République', 'Nice', '06000', 'CLIENT', TRUE, TRUE),
('Martin', 'Marie', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456781', '1993-06-22', '9 Place du Marché', 'Nantes', '44000', 'CLIENT', TRUE, TRUE),
('Bernard', 'Pierre', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456780', '1991-02-14', '10 Avenue de la Liberté', 'Rennes', '35000', 'CLIENT', TRUE, TRUE),
('Moreau', 'Sophie', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456779', '1989-08-03', '11 Rue des Fleurs', 'Montpellier', '34000', 'CLIENT', TRUE, TRUE),
('Petit', 'Luc', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456778', '1994-10-17', '12 Rue du Commerce', 'Dijon', '21000', 'CLIENT', TRUE, TRUE);

-- ========================================
-- INSERTION DES TRAJETS
-- ========================================
INSERT INTO trajets (gare_depart_id, gare_arrivee_id, heure_depart, heure_arrivee, prix, nombre_places, type_train, numero_train) VALUES

-- TRAJETS TGV PRINCIPAUX
(1, 2, '08:00:00', '12:30:00', 89.50, 200, 'TGV', 'TGV 6651'), -- Paris -> Lyon
(2, 1, '14:00:00', '18:30:00', 89.50, 200, 'TGV', 'TGV 6652'), -- Lyon -> Paris
(1, 3, '09:15:00', '15:45:00', 125.00, 150, 'TGV', 'TGV 8501'), -- Paris -> Bordeaux
(3, 1, '16:30:00', '23:00:00', 125.00, 150, 'TGV', 'TGV 8502'), -- Bordeaux -> Paris
(1, 4, '07:30:00', '16:15:00', 135.00, 180, 'TGV', 'TGV 6171'), -- Paris -> Marseille
(4, 1, '15:45:00', '00:30:00', 135.00, 180, 'TGV', 'TGV 6172'), -- Marseille -> Paris
(1, 5, '10:00:00', '17:20:00', 110.00, 160, 'TGV', 'TGV 8301'), -- Paris -> Toulouse
(5, 1, '18:00:00', '01:20:00', 110.00, 160, 'TGV', 'TGV 8302'), -- Toulouse -> Paris

-- TRAJETS INTER-REGIONS
(2, 4, '07:30:00', '13:15:00', 95.00, 180, 'TGV', 'TGV 6181'), -- Lyon -> Marseille
(4, 2, '15:45:00', '21:30:00', 95.00, 180, 'TGV', 'TGV 6182'), -- Marseille -> Lyon
(2, 5, '09:00:00', '15:30:00', 85.00, 160, 'INTERCITES', 'IC 3651'), -- Lyon -> Toulouse
(5, 2, '16:00:00', '22:30:00', 85.00, 160, 'INTERCITES', 'IC 3652'), -- Toulouse -> Lyon

-- TRAJETS REGIONAUX TER
(4, 8, '06:30:00', '09:45:00', 55.00, 120, 'TER', 'TER 17501'), -- Marseille -> Nice
(8, 4, '18:15:00', '21:30:00', 55.00, 120, 'TER', 'TER 17502'), -- Nice -> Marseille
(4, 8, '12:30:00', '15:45:00', 55.00, 120, 'TER', 'TER 17503'), -- Marseille -> Nice (midi)
(8, 4, '14:00:00', '17:15:00', 55.00, 120, 'TER', 'TER 17504'), -- Nice -> Marseille (après-midi)

-- TRAJETS OUIGO (low-cost)
(1, 2, '05:45:00', '10:45:00', 45.00, 250, 'OUIGO', 'OUI 7601'), -- Paris -> Lyon
(2, 1, '21:30:00', '02:30:00', 45.00, 250, 'OUIGO', 'OUI 7602'), -- Lyon -> Paris
(1, 4, '06:15:00', '15:45:00', 65.00, 220, 'OUIGO', 'OUI 7701'), -- Paris -> Marseille

-- TRAJETS SUPPLEMENTAIRES
(9, 10, '08:45:00', '10:15:00', 35.00, 90, 'TER', 'TER 57801'), -- Nantes -> Rennes
(10, 9, '19:30:00', '21:00:00', 35.00, 90, 'TER', 'TER 57802'), -- Rennes -> Nantes
(11, 4, '09:30:00', '12:45:00', 45.00, 100, 'TER', 'TER 17601'), -- Montpellier -> Marseille
(4, 11, '16:15:00', '19:30:00', 45.00, 100, 'TER', 'TER 17602'), -- Marseille -> Montpellier
(12, 2, '07:45:00', '10:30:00', 65.00, 120, 'TER', 'TER 27801'), -- Dijon -> Lyon
(2, 12, '18:45:00', '21:30:00', 65.00, 120, 'TER', 'TER 27802'); -- Lyon -> Dijon

-- ========================================
-- GENERATION DES VOYAGES (14 jours)
-- ========================================
INSERT INTO voyages (trajet_id, date_voyage, places_disponibles)
SELECT 
    t.id,
    DATE_ADD(CURDATE(), INTERVAL d.day_offset DAY),
    t.nombre_places
FROM trajets t
CROSS JOIN (
    SELECT 0 as day_offset UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL
    SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL
    SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10 UNION ALL SELECT 11 UNION ALL
    SELECT 12 UNION ALL SELECT 13
) d
WHERE t.actif = TRUE;

-- ========================================
-- INSERTION DES RESERVATIONS DE TEST
-- ========================================
INSERT INTO reservations (numero_reservation, utilisateur_id, voyage_id, nombre_places, prix_unitaire, prix_total, statut, mode_paiement, reference_paiement) VALUES

-- Réservations confirmées
('RES2025000001', 5, 1, 2, 89.50, 179.00, 'CONFIRMEE', 'CARTE_BANCAIRE', 'PAY123456789'),
('RES2025000002', 6, 8, 1, 89.50, 89.50, 'CONFIRMEE', 'PAYPAL', 'PP987654321'),
('RES2025000003', 7, 15, 1, 125.00, 125.00, 'CONFIRMEE', 'CARTE_BANCAIRE', 'PAY456789123'),
('RES2025000004', 8, 22, 3, 135.00, 405.00, 'CONFIRMEE', 'CARTE_BANCAIRE', 'PAY789123456'),
('RES2025000005', 9, 29, 1, 110.00, 110.00, 'CONFIRMEE', 'VIREMENT', 'VIR123456789'),

-- Réservations en attente
('RES2025000006', 10, 36, 2, 95.00, 190.00, 'EN_ATTENTE', 'CARTE_BANCAIRE', NULL),
('RES2025000007', 11, 43, 1, 85.00, 85.00, 'EN_ATTENTE', 'CARTE_BANCAIRE', NULL),
('RES2025000008', 12, 50, 4, 55.00, 220.00, 'EN_ATTENTE', 'PAYPAL', NULL),

-- Réservations avec réductions
('RES2025000009', 5, 57, 1, 55.00, 41.25, 'CONFIRMEE', 'CARTE_BANCAIRE', 'PAY111222333'),
('RES2025000010', 6, 64, 2, 45.00, 72.00, 'CONFIRMEE', 'CARTE_BANCAIRE', 'PAY444555666');

-- Mettre à jour les réductions appliquées
UPDATE reservations SET reduction_appliquee = 13.75, code_reduction = 'ETUDIANT' WHERE numero_reservation = 'RES2025000009';
UPDATE reservations SET reduction_appliquee = 18.00, code_reduction = 'SENIOR' WHERE numero_reservation = 'RES2025000010';

-- Mettre à jour les dates de confirmation
UPDATE reservations 
SET date_confirmation = DATE_ADD(date_reservation, INTERVAL 1 HOUR),
    date_paiement = DATE_ADD(date_reservation, INTERVAL 2 HOUR),
    montant_paye = prix_total
WHERE statut = 'CONFIRMEE';

-- Mettre à jour les dernières connexions des utilisateurs
UPDATE utilisateurs 
SET derniere_connexion = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 30) DAY) 
WHERE actif = TRUE;

-- ========================================
-- VERIFICATION DES DONNEES INSEREES
-- ========================================
SELECT 'DONNEES DE TEST INSEREES AVEC SUCCES' as message;

SELECT 
    'Gares' as table_name, 
    COUNT(*) as nombre_enregistrements
FROM gares

UNION ALL

SELECT 
    'Utilisateurs', 
    COUNT(*)
FROM utilisateurs

UNION ALL

SELECT 
    'Trajets', 
    COUNT(*)
FROM trajets

UNION ALL

SELECT 
    'Voyages', 
    COUNT(*)
FROM voyages

UNION ALL

SELECT 
    'Réservations', 
    COUNT(*)
FROM reservations;

-- Afficher les comptes de test
SELECT 
    'COMPTES DE TEST DISPONIBLES' as info,
    CONCAT(prenom, ' ', nom) as nom_complet,
    email,
    type_utilisateur as role,
    'password' as mot_de_passe
FROM utilisateurs 
ORDER BY type_utilisateur, email;

COMMIT;
