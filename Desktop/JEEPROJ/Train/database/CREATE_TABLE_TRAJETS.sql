-- ========================================
-- SCRIPT DE CRÉATION DE LA TABLE TRAJETS
-- ========================================

-- Utiliser la base de données train
USE train;

-- Supprimer la table si elle existe déjà (optionnel)
-- DROP TABLE IF EXISTS trajets;

-- Créer la table trajets
CREATE TABLE IF NOT EXISTS trajets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    gare_depart_id BIGINT NOT NULL,
    gare_arrivee_id BIGINT NOT NULL,
    heure_depart TIME NOT NULL,
    heure_arrivee TIME NOT NULL,
    prix DECIMAL(10, 2) NOT NULL,
    nombre_places INT NOT NULL DEFAULT 200,
    type_train ENUM('TGV', 'INTERCITES', 'TER', 'OUIGO') DEFAULT 'TER',
    actif BOOLEAN DEFAULT TRUE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Contraintes de clés étrangères
    FOREIGN KEY (gare_depart_id) REFERENCES gares(id) ON DELETE CASCADE,
    FOREIGN KEY (gare_arrivee_id) REFERENCES gares(id) ON DELETE CASCADE,
    
    -- Contraintes de validation
    CONSTRAINT chk_gares_differentes CHECK (gare_depart_id != gare_arrivee_id),
    CONSTRAINT chk_prix_positif CHECK (prix > 0),
    CONSTRAINT chk_places_positives CHECK (nombre_places > 0),
    
    -- Index pour améliorer les performances
    INDEX idx_gare_depart (gare_depart_id),
    INDEX idx_gare_arrivee (gare_arrivee_id),
    INDEX idx_gares_couple (gare_depart_id, gare_arrivee_id),
    INDEX idx_horaires (heure_depart, heure_arrivee),
    INDEX idx_actif (actif),
    INDEX idx_type_train (type_train)
);

-- Insérer quelques données de test
INSERT INTO trajets (gare_depart_id, gare_arrivee_id, heure_depart, heure_arrivee, prix, nombre_places, type_train, actif) VALUES
-- Paris Nord → Lyon Part-Dieu
(1, 2, '06:30:00', '08:45:00', 89.00, 300, 'TGV', TRUE),
(1, 2, '09:15:00', '11:30:00', 95.00, 300, 'TGV', TRUE),
(1, 2, '14:20:00', '16:35:00', 89.00, 300, 'TGV', TRUE),
(1, 2, '18:45:00', '21:00:00', 105.00, 300, 'TGV', TRUE),

-- Lyon Part-Dieu → Paris Nord
(2, 1, '07:00:00', '09:15:00', 89.00, 300, 'TGV', TRUE),
(2, 1, '11:30:00', '13:45:00', 95.00, 300, 'TGV', TRUE),
(2, 1, '15:45:00', '18:00:00', 89.00, 300, 'TGV', TRUE),
(2, 1, '19:30:00', '21:45:00', 105.00, 300, 'TGV', TRUE),

-- Paris Nord → Marseille Saint-Charles
(1, 3, '07:45:00', '11:15:00', 125.00, 280, 'TGV', TRUE),
(1, 3, '13:30:00', '17:00:00', 135.00, 280, 'TGV', TRUE),
(1, 3, '17:15:00', '20:45:00', 125.00, 280, 'TGV', TRUE),

-- Marseille Saint-Charles → Paris Nord
(3, 1, '08:15:00', '11:45:00', 125.00, 280, 'TGV', TRUE),
(3, 1, '14:00:00', '17:30:00', 135.00, 280, 'TGV', TRUE),
(3, 1, '18:30:00', '22:00:00', 125.00, 280, 'TGV', TRUE),

-- Lyon Part-Dieu → Marseille Saint-Charles
(2, 3, '08:30:00', '10:15:00', 45.00, 250, 'TGV', TRUE),
(2, 3, '12:45:00', '14:30:00', 50.00, 250, 'TGV', TRUE),
(2, 3, '16:20:00', '18:05:00', 45.00, 250, 'TGV', TRUE),
(2, 3, '19:45:00', '21:30:00', 55.00, 250, 'TGV', TRUE),

-- Marseille Saint-Charles → Lyon Part-Dieu
(3, 2, '09:00:00', '10:45:00', 45.00, 250, 'TGV', TRUE),
(3, 2, '13:15:00', '15:00:00', 50.00, 250, 'TGV', TRUE),
(3, 2, '17:30:00', '19:15:00', 45.00, 250, 'TGV', TRUE),
(3, 2, '20:15:00', '22:00:00', 55.00, 250, 'TGV', TRUE),

-- Trajets TER régionaux
(1, 2, '06:00:00', '09:30:00', 35.00, 150, 'TER', TRUE),
(1, 2, '12:00:00', '15:30:00', 35.00, 150, 'TER', TRUE),
(1, 2, '20:00:00', '23:30:00', 35.00, 150, 'TER', TRUE),

(2, 1, '06:30:00', '10:00:00', 35.00, 150, 'TER', TRUE),
(2, 1, '12:30:00', '16:00:00', 35.00, 150, 'TER', TRUE),
(2, 1, '20:30:00', '00:00:00', 35.00, 150, 'TER', TRUE),

-- Trajets INTERCITES
(1, 3, '22:30:00', '07:15:00', 65.00, 200, 'INTERCITES', TRUE),
(3, 1, '22:45:00', '07:30:00', 65.00, 200, 'INTERCITES', TRUE),

-- Trajets OUIGO (low-cost)
(1, 2, '05:45:00', '08:30:00', 25.00, 400, 'OUIGO', TRUE),
(2, 1, '21:15:00', '00:00:00', 25.00, 400, 'OUIGO', TRUE);

-- Afficher les données insérées
SELECT 
    t.id,
    gd.ville as ville_depart,
    ga.ville as ville_arrivee,
    t.heure_depart,
    t.heure_arrivee,
    t.prix,
    t.nombre_places,
    t.type_train,
    t.actif
FROM trajets t
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
ORDER BY gd.ville, ga.ville, t.heure_depart;

-- Afficher le nombre total de trajets
SELECT COUNT(*) as total_trajets FROM trajets;

-- Afficher les statistiques par type de train
SELECT 
    type_train,
    COUNT(*) as nombre_trajets,
    AVG(prix) as prix_moyen,
    AVG(nombre_places) as places_moyennes
FROM trajets 
WHERE actif = TRUE
GROUP BY type_train
ORDER BY type_train;

-- Afficher les statistiques générales
SELECT 
    COUNT(*) as total_trajets,
    COUNT(CASE WHEN actif = TRUE THEN 1 END) as trajets_actifs,
    COUNT(CASE WHEN actif = FALSE THEN 1 END) as trajets_inactifs,
    AVG(prix) as prix_moyen,
    MIN(prix) as prix_minimum,
    MAX(prix) as prix_maximum,
    SUM(nombre_places) as total_places
FROM trajets;

COMMIT;
