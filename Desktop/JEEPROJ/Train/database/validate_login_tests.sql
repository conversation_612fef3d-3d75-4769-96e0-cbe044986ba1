-- ========================================
-- VALIDATION DES TESTS DE CONNEXION
-- Requêtes pour vérifier que tout fonctionne
-- ========================================

USE train;

-- ========================================
-- VERIFICATION DES COMPTES DE TEST
-- ========================================

SELECT '=== COMPTES DE TEST DISPONIBLES ===' as info;

SELECT 
    ROW_NUMBER() OVER (ORDER BY type_utilisateur, email) as num,
    CONCAT(prenom, ' ', nom) as nom_complet,
    email,
    type_utilisateur as role,
    CASE WHEN actif THEN '✅ ACTIF' ELSE '❌ INACTIF' END as statut,
    'password' as mot_de_passe,
    telephone,
    ville
FROM utilisateurs 
WHERE email LIKE '%@train.com' OR email LIKE '%@test.com'
ORDER BY 
    CASE type_utilisateur 
        WHEN 'ADMINISTRATEUR' THEN 1 
        WHEN 'EMPLOYE' THEN 2 
        WHEN 'CLIENT' THEN 3 
    END, 
    email;

-- ========================================
-- TEST DE RECHERCHE DE VOYAGES
-- ========================================

SELECT '=== TEST RECHERCHE VOYAGES ===' as info;

-- Recherche Paris -> Lyon pour aujourd'hui
SELECT 
    'Paris → Lyon (Aujourd\'hui)' as recherche,
    COUNT(*) as voyages_disponibles
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
WHERE gd.ville = 'Paris' 
AND ga.ville = 'Lyon'
AND v.date_voyage = CURDATE()
AND v.places_disponibles > 0
AND v.statut = 'PROGRAMME'

UNION ALL

-- Recherche Lyon -> Marseille pour demain
SELECT 
    'Lyon → Marseille (Demain)',
    COUNT(*)
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
WHERE gd.ville = 'Lyon' 
AND ga.ville = 'Marseille'
AND v.date_voyage = DATE_ADD(CURDATE(), INTERVAL 1 DAY)
AND v.places_disponibles > 0
AND v.statut = 'PROGRAMME'

UNION ALL

-- Recherche Marseille -> Nice pour après-demain
SELECT 
    'Marseille → Nice (Après-demain)',
    COUNT(*)
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
WHERE gd.ville = 'Marseille' 
AND ga.ville = 'Nice'
AND v.date_voyage = DATE_ADD(CURDATE(), INTERVAL 2 DAY)
AND v.places_disponibles > 0
AND v.statut = 'PROGRAMME';

-- ========================================
-- EXEMPLES DE VOYAGES DISPONIBLES
-- ========================================

SELECT '=== EXEMPLES DE VOYAGES DISPONIBLES ===' as info;

SELECT 
    CONCAT(gd.ville, ' → ', ga.ville) as trajet,
    v.date_voyage,
    t.heure_depart,
    t.heure_arrivee,
    t.prix as prix_euros,
    t.type_train,
    v.places_disponibles,
    CASE 
        WHEN v.places_disponibles > 100 THEN '🟢 Beaucoup'
        WHEN v.places_disponibles > 50 THEN '🟡 Moyen'
        WHEN v.places_disponibles > 10 THEN '🟠 Peu'
        ELSE '🔴 Très peu'
    END as disponibilite
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
WHERE v.date_voyage BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 3 DAY)
AND v.places_disponibles > 0
AND v.statut = 'PROGRAMME'
ORDER BY v.date_voyage, t.heure_depart
LIMIT 15;

-- ========================================
-- HISTORIQUE DES RESERVATIONS PAR UTILISATEUR
-- ========================================

SELECT '=== HISTORIQUE RESERVATIONS UTILISATEURS TEST ===' as info;

SELECT 
    u.email,
    CONCAT(u.prenom, ' ', u.nom) as nom,
    COUNT(r.id) as nb_reservations,
    SUM(CASE WHEN r.statut = 'CONFIRMEE' THEN r.prix_total ELSE 0 END) as total_confirme,
    SUM(CASE WHEN r.statut = 'EN_ATTENTE' THEN r.prix_total ELSE 0 END) as total_en_attente,
    MAX(r.date_reservation) as derniere_reservation
FROM utilisateurs u
LEFT JOIN reservations r ON r.utilisateur_id = u.id
WHERE u.email LIKE '%@train.com' OR u.email LIKE '%@test.com'
GROUP BY u.id, u.email, u.prenom, u.nom
HAVING nb_reservations > 0
ORDER BY total_confirme DESC;

-- ========================================
-- DETAILS DES RESERVATIONS RECENTES
-- ========================================

SELECT '=== RESERVATIONS RECENTES (7 derniers jours) ===' as info;

SELECT 
    r.numero_reservation,
    CONCAT(u.prenom, ' ', u.nom) as client,
    u.email,
    CONCAT(gd.ville, ' → ', ga.ville) as trajet,
    v.date_voyage,
    t.heure_depart,
    r.nombre_places,
    r.prix_total,
    r.statut,
    r.date_reservation,
    r.mode_paiement
FROM reservations r
JOIN utilisateurs u ON r.utilisateur_id = u.id
JOIN voyages v ON r.voyage_id = v.id
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
WHERE r.date_reservation >= DATE_SUB(NOW(), INTERVAL 7 DAY)
AND (u.email LIKE '%@train.com' OR u.email LIKE '%@test.com')
ORDER BY r.date_reservation DESC
LIMIT 10;

-- ========================================
-- VERIFICATION DE L'INTEGRITE DES DONNEES
-- ========================================

SELECT '=== VERIFICATION INTEGRITE DONNEES ===' as info;

-- Vérifier que les places sont cohérentes
SELECT 
    'Voyages avec places incohérentes' as verification,
    COUNT(*) as nombre_problemes
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
WHERE v.places_disponibles + v.places_reservees != t.nombre_places

UNION ALL

-- Vérifier les réservations sans voyage
SELECT 
    'Réservations orphelines',
    COUNT(*)
FROM reservations r
LEFT JOIN voyages v ON r.voyage_id = v.id
WHERE v.id IS NULL

UNION ALL

-- Vérifier les utilisateurs sans email valide
SELECT 
    'Utilisateurs email invalide',
    COUNT(*)
FROM utilisateurs
WHERE email NOT LIKE '%@%' OR email IS NULL

UNION ALL

-- Vérifier les trajets avec gares identiques
SELECT 
    'Trajets gares identiques',
    COUNT(*)
FROM trajets
WHERE gare_depart_id = gare_arrivee_id;

-- ========================================
-- REQUETES DE TEST POUR L'APPLICATION
-- ========================================

SELECT '=== REQUETES DE TEST POUR APPLICATION ===' as info;

-- Test 1: Authentification admin
SELECT 
    'Test authentification admin' as test,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ OK'
        ELSE '❌ ECHEC'
    END as resultat
FROM utilisateurs 
WHERE email = '<EMAIL>' 
AND type_utilisateur = 'ADMINISTRATEUR' 
AND actif = TRUE

UNION ALL

-- Test 2: Recherche voyages disponibles
SELECT 
    'Test recherche voyages',
    CASE 
        WHEN COUNT(*) > 0 THEN CONCAT('✅ ', COUNT(*), ' voyages trouvés')
        ELSE '❌ Aucun voyage'
    END
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
WHERE v.date_voyage >= CURDATE()
AND v.places_disponibles > 0
AND v.statut = 'PROGRAMME'

UNION ALL

-- Test 3: Utilisateurs avec réservations
SELECT 
    'Test utilisateurs actifs',
    CASE 
        WHEN COUNT(DISTINCT r.utilisateur_id) > 0 THEN CONCAT('✅ ', COUNT(DISTINCT r.utilisateur_id), ' utilisateurs avec réservations')
        ELSE '❌ Aucune réservation'
    END
FROM reservations r
JOIN utilisateurs u ON r.utilisateur_id = u.id
WHERE u.actif = TRUE;

-- ========================================
-- RESUME FINAL
-- ========================================

SELECT '=== RESUME FINAL ===' as info;

SELECT 
    'Base de données TrainSystem' as systeme,
    'Prête pour les tests' as statut,
    NOW() as date_verification;

SELECT 
    'Utilisateurs de test' as categorie,
    COUNT(*) as total,
    SUM(CASE WHEN type_utilisateur = 'ADMINISTRATEUR' THEN 1 ELSE 0 END) as admins,
    SUM(CASE WHEN type_utilisateur = 'EMPLOYE' THEN 1 ELSE 0 END) as employes,
    SUM(CASE WHEN type_utilisateur = 'CLIENT' THEN 1 ELSE 0 END) as clients
FROM utilisateurs 
WHERE email LIKE '%@train.com' OR email LIKE '%@test.com';

SELECT 
    'Données de test' as categorie,
    'Gares' as type,
    COUNT(*) as nombre
FROM gares
UNION ALL
SELECT 'Données de test', 'Trajets', COUNT(*) FROM trajets
UNION ALL
SELECT 'Données de test', 'Voyages disponibles', COUNT(*) FROM voyages WHERE date_voyage >= CURDATE()
UNION ALL
SELECT 'Données de test', 'Réservations', COUNT(*) FROM reservations;

COMMIT;
