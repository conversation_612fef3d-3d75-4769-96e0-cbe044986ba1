-- ========================================
-- BASE DE DONNEES COMPLETE TRAINSYSTEM
-- Nom de la base: train
-- Exécutez ce script dans phpMyAdmin
-- ========================================

-- Créer et utiliser la base de données
CREATE DATABASE IF NOT EXISTS train CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE train;

-- Désactiver les vérifications temporairement
SET FOREIGN_KEY_CHECKS = 0;
SET AUTOCOMMIT = 0;

-- Supprimer les tables existantes dans l'ordre des dépendances
DROP TABLE IF EXISTS reservations;
DROP TABLE IF EXISTS voyages;
DROP TABLE IF EXISTS trajets;
DROP TABLE IF EXISTS utilisateurs;
DROP TABLE IF EXISTS gares;

-- Réactiver les vérifications
SET FOREIGN_KEY_CHECKS = 1;

-- ========================================
-- TABLE: gares
-- ========================================
CREATE TABLE gares (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL COMMENT 'Nom de la gare',
    ville VARCHAR(100) NOT NULL COMMENT 'Ville de la gare',
    code_gare VARCHAR(10) UNIQUE NOT NULL COMMENT 'Code unique de la gare',
    adresse VARCHAR(255) COMMENT 'Adresse complète',
    code_postal VARCHAR(10) COMMENT 'Code postal',
    latitude DECIMAL(10, 8) COMMENT 'Latitude GPS',
    longitude DECIMAL(11, 8) COMMENT 'Longitude GPS',
    active BOOLEAN DEFAULT TRUE COMMENT 'Gare active ou non',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_ville (ville),
    INDEX idx_code_gare (code_gare),
    INDEX idx_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Table des gares';

-- ========================================
-- TABLE: utilisateurs
-- ========================================
CREATE TABLE utilisateurs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL COMMENT 'Nom de famille',
    prenom VARCHAR(100) NOT NULL COMMENT 'Prénom',
    email VARCHAR(255) UNIQUE NOT NULL COMMENT 'Email unique',
    mot_de_passe VARCHAR(255) NOT NULL COMMENT 'Mot de passe haché',
    telephone VARCHAR(20) COMMENT 'Numéro de téléphone',
    date_naissance DATE COMMENT 'Date de naissance',
    adresse VARCHAR(255) COMMENT 'Adresse postale',
    ville VARCHAR(100) COMMENT 'Ville de résidence',
    code_postal VARCHAR(10) COMMENT 'Code postal',
    type_utilisateur ENUM('CLIENT', 'EMPLOYE', 'ADMINISTRATEUR') DEFAULT 'CLIENT' COMMENT 'Type d\'utilisateur',
    actif BOOLEAN DEFAULT TRUE COMMENT 'Compte actif ou non',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    derniere_connexion TIMESTAMP NULL COMMENT 'Dernière connexion',
    
    INDEX idx_email (email),
    INDEX idx_type (type_utilisateur),
    INDEX idx_actif (actif),
    INDEX idx_derniere_connexion (derniere_connexion)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Table des utilisateurs';

-- ========================================
-- TABLE: trajets
-- ========================================
CREATE TABLE trajets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    gare_depart_id BIGINT NOT NULL COMMENT 'ID gare de départ',
    gare_arrivee_id BIGINT NOT NULL COMMENT 'ID gare d\'arrivée',
    heure_depart TIME NOT NULL COMMENT 'Heure de départ',
    heure_arrivee TIME NOT NULL COMMENT 'Heure d\'arrivée',
    duree_minutes INT GENERATED ALWAYS AS (
        CASE 
            WHEN heure_arrivee >= heure_depart THEN 
                TIME_TO_SEC(heure_arrivee) - TIME_TO_SEC(heure_depart)
            ELSE 
                (24*3600) - TIME_TO_SEC(heure_depart) + TIME_TO_SEC(heure_arrivee)
        END / 60
    ) STORED COMMENT 'Durée en minutes (calculée)',
    prix DECIMAL(10, 2) NOT NULL COMMENT 'Prix du billet',
    nombre_places INT NOT NULL DEFAULT 200 COMMENT 'Nombre de places total',
    type_train ENUM('TGV', 'INTERCITES', 'TER', 'OUIGO') DEFAULT 'TER' COMMENT 'Type de train',
    actif BOOLEAN DEFAULT TRUE COMMENT 'Trajet actif ou non',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (gare_depart_id) REFERENCES gares(id) ON DELETE CASCADE,
    FOREIGN KEY (gare_arrivee_id) REFERENCES gares(id) ON DELETE CASCADE,
    
    INDEX idx_gare_depart (gare_depart_id),
    INDEX idx_gare_arrivee (gare_arrivee_id),
    INDEX idx_heure_depart (heure_depart),
    INDEX idx_prix (prix),
    INDEX idx_type_train (type_train),
    INDEX idx_actif (actif),
    
    CONSTRAINT chk_gares_differentes CHECK (gare_depart_id != gare_arrivee_id),
    CONSTRAINT chk_prix_positif CHECK (prix > 0),
    CONSTRAINT chk_places_positives CHECK (nombre_places > 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Table des trajets';

-- ========================================
-- TABLE: voyages
-- ========================================
CREATE TABLE voyages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    trajet_id BIGINT NOT NULL COMMENT 'ID du trajet',
    date_voyage DATE NOT NULL COMMENT 'Date du voyage',
    places_disponibles INT NOT NULL COMMENT 'Places disponibles',
    places_reservees INT DEFAULT 0 COMMENT 'Places réservées',
    statut ENUM('PROGRAMME', 'EN_COURS', 'TERMINE', 'ANNULE', 'RETARDE') DEFAULT 'PROGRAMME' COMMENT 'Statut du voyage',
    retard_minutes INT DEFAULT 0 COMMENT 'Retard en minutes',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (trajet_id) REFERENCES trajets(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_trajet_date (trajet_id, date_voyage),
    INDEX idx_date_voyage (date_voyage),
    INDEX idx_statut (statut),
    INDEX idx_places_disponibles (places_disponibles),
    INDEX idx_trajet_date (trajet_id, date_voyage),
    
    CONSTRAINT chk_places_coherentes CHECK (places_disponibles >= 0),
    CONSTRAINT chk_places_reservees_positives CHECK (places_reservees >= 0),
    CONSTRAINT chk_retard_positif CHECK (retard_minutes >= 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Table des voyages';

-- ========================================
-- TABLE: reservations
-- ========================================
CREATE TABLE reservations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    numero_reservation VARCHAR(20) UNIQUE NOT NULL COMMENT 'Numéro unique de réservation',
    utilisateur_id BIGINT NOT NULL COMMENT 'ID de l\'utilisateur',
    voyage_id BIGINT NOT NULL COMMENT 'ID du voyage',
    nombre_places INT NOT NULL DEFAULT 1 COMMENT 'Nombre de places réservées',
    prix_total DECIMAL(10, 2) NOT NULL COMMENT 'Prix total de la réservation',
    statut ENUM('EN_ATTENTE', 'CONFIRMEE', 'ANNULEE', 'REMBOURSEE') DEFAULT 'EN_ATTENTE' COMMENT 'Statut de la réservation',
    date_reservation TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Date de création',
    date_confirmation TIMESTAMP NULL COMMENT 'Date de confirmation',
    date_annulation TIMESTAMP NULL COMMENT 'Date d\'annulation',
    motif_annulation TEXT COMMENT 'Motif d\'annulation',
    mode_paiement ENUM('CARTE_BANCAIRE', 'PAYPAL', 'VIREMENT', 'ESPECES') DEFAULT 'CARTE_BANCAIRE' COMMENT 'Mode de paiement',
    reference_paiement VARCHAR(100) COMMENT 'Référence du paiement',
    
    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id) ON DELETE CASCADE,
    FOREIGN KEY (voyage_id) REFERENCES voyages(id) ON DELETE CASCADE,
    
    INDEX idx_numero_reservation (numero_reservation),
    INDEX idx_utilisateur (utilisateur_id),
    INDEX idx_voyage (voyage_id),
    INDEX idx_statut (statut),
    INDEX idx_date_reservation (date_reservation),
    INDEX idx_user_statut (utilisateur_id, statut),
    
    CONSTRAINT chk_nombre_places_positif CHECK (nombre_places > 0),
    CONSTRAINT chk_prix_total_positif CHECK (prix_total > 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Table des réservations';

-- ========================================
-- INSERTION DES GARES
-- ========================================
INSERT INTO gares (nom, ville, code_gare, adresse, code_postal, latitude, longitude) VALUES
('Gare de Paris-Montparnasse', 'Paris', 'PAR01', '17 Boulevard de Vaugirard', '75015', 48.8404, 2.3188),
('Gare de Lyon Part-Dieu', 'Lyon', 'LYO01', '3 Place Charles Béraudier', '69003', 45.7606, 4.8598),
('Gare Saint-Jean', 'Bordeaux', 'BOR01', 'Cours de la Marne', '33800', 44.8258, -0.5570),
('Gare Saint-Charles', 'Marseille', 'MAR01', 'Square Narvik', '13001', 43.3030, 5.3811),
('Gare Matabiau', 'Toulouse', 'TOU01', '64 Boulevard Pierre Semard', '31000', 43.6108, 1.4537),
('Gare Centrale', 'Strasbourg', 'STR01', '20 Place de la Gare', '67000', 48.5847, 7.7339),
('Gare Lille-Flandres', 'Lille', 'LIL01', '3 Place des Buisses', '59000', 50.6365, 3.0708),
('Gare de Nice-Ville', 'Nice', 'NIC01', 'Avenue Thiers', '06000', 43.7048, 7.2620),
('Gare de Nantes', 'Nantes', 'NAN01', '27 Boulevard de Stalingrad', '44000', 47.2173, -1.5412),
('Gare de Rennes', 'Rennes', 'REN01', '20 Place de la Gare', '35000', 48.1030, -1.6720),
('Gare de Montpellier', 'Montpellier', 'MTP01', '1 Place Auguste Gibert', '34000', 43.6047, 3.8767),
('Gare de Dijon', 'Dijon', 'DIJ01', 'Cour de la Gare', '21000', 47.3236, 5.0279);

-- ========================================
-- INSERTION DES UTILISATEURS
-- ========================================
-- Mot de passe pour tous: "password" (haché avec BCrypt)
INSERT INTO utilisateurs (nom, prenom, email, mot_de_passe, telephone, date_naissance, adresse, ville, code_postal, type_utilisateur, actif) VALUES

-- ADMINISTRATEURS
('Admin', 'Principal', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456789', '1980-01-15', '1 Rue de la Gare', 'Paris', '75001', 'ADMINISTRATEUR', TRUE),
('Super', 'Admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456788', '1975-05-20', '2 Avenue des Trains', 'Lyon', '69001', 'ADMINISTRATEUR', TRUE),

-- EMPLOYES
('Employe', 'SNCF', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456787', '1985-03-10', '3 Boulevard SNCF', 'Marseille', '13001', 'EMPLOYE', TRUE),
('Agent', 'Gare', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456786', '1990-07-25', '4 Place de la Station', 'Bordeaux', '33000', 'EMPLOYE', TRUE),

-- CLIENTS
('Client', 'Test', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456785', '1992-12-05', '5 Rue du Voyage', 'Toulouse', '31000', 'CLIENT', TRUE),
('Demo', 'User', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456784', '1988-09-18', '6 Avenue des Voyageurs', 'Strasbourg', '67000', 'CLIENT', TRUE),
('Utilisateur', 'Test', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456783', '1995-04-12', '7 Boulevard des Trains', 'Lille', '59000', 'CLIENT', TRUE),
('Dupont', 'Jean', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456782', '1987-11-30', '8 Rue de la République', 'Nice', '06000', 'CLIENT', TRUE),
('Martin', 'Marie', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456781', '1993-06-22', '9 Place du Marché', 'Nantes', '44000', 'CLIENT', TRUE),
('Bernard', 'Pierre', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456780', '1991-02-14', '10 Avenue de la Liberté', 'Rennes', '35000', 'CLIENT', TRUE),
('Moreau', 'Sophie', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456779', '1989-08-03', '11 Rue des Fleurs', 'Montpellier', '34000', 'CLIENT', TRUE),
('Petit', 'Luc', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456778', '1994-10-17', '12 Rue du Commerce', 'Dijon', '21000', 'CLIENT', TRUE);

-- ========================================
-- INSERTION DES TRAJETS
-- ========================================
INSERT INTO trajets (gare_depart_id, gare_arrivee_id, heure_depart, heure_arrivee, prix, nombre_places, type_train) VALUES

-- TRAJETS PRINCIPAUX TGV
-- Paris vers autres villes
(1, 2, '08:00:00', '12:30:00', 89.50, 200, 'TGV'),     -- Paris -> Lyon
(1, 3, '09:15:00', '15:45:00', 125.00, 150, 'TGV'),    -- Paris -> Bordeaux
(1, 4, '07:30:00', '16:15:00', 135.00, 180, 'TGV'),    -- Paris -> Marseille
(1, 5, '10:00:00', '17:20:00', 110.00, 160, 'TGV'),    -- Paris -> Toulouse
(1, 6, '06:45:00', '10:30:00', 95.00, 140, 'TGV'),     -- Paris -> Strasbourg
(1, 7, '07:00:00', '08:00:00', 45.00, 120, 'TGV'),     -- Paris -> Lille

-- Retours vers Paris
(2, 1, '14:00:00', '18:30:00', 89.50, 200, 'TGV'),     -- Lyon -> Paris
(3, 1, '16:30:00', '23:00:00', 125.00, 150, 'TGV'),    -- Bordeaux -> Paris
(4, 1, '15:45:00', '00:30:00', 135.00, 180, 'TGV'),    -- Marseille -> Paris
(5, 1, '18:00:00', '01:20:00', 110.00, 160, 'TGV'),    -- Toulouse -> Paris
(6, 1, '17:15:00', '21:00:00', 95.00, 140, 'TGV'),     -- Strasbourg -> Paris
(7, 1, '19:00:00', '20:00:00', 45.00, 120, 'TGV'),     -- Lille -> Paris

-- TRAJETS INTER-REGIONS
(2, 4, '07:30:00', '13:15:00', 95.00, 180, 'TGV'),     -- Lyon -> Marseille
(4, 2, '15:45:00', '21:30:00', 95.00, 180, 'TGV'),     -- Marseille -> Lyon
(2, 5, '09:00:00', '15:30:00', 85.00, 160, 'INTERCITES'), -- Lyon -> Toulouse
(5, 2, '16:00:00', '22:30:00', 85.00, 160, 'INTERCITES'), -- Toulouse -> Lyon
(3, 5, '08:15:00', '10:45:00', 65.00, 140, 'TER'),     -- Bordeaux -> Toulouse
(5, 3, '17:30:00', '20:00:00', 65.00, 140, 'TER'),     -- Toulouse -> Bordeaux

-- TRAJETS REGIONAUX TER
(4, 8, '06:30:00', '09:45:00', 55.00, 120, 'TER'),     -- Marseille -> Nice
(8, 4, '18:15:00', '21:30:00', 55.00, 120, 'TER'),     -- Nice -> Marseille
(4, 8, '12:30:00', '15:45:00', 55.00, 120, 'TER'),     -- Marseille -> Nice (midi)
(8, 4, '14:00:00', '17:15:00', 55.00, 120, 'TER'),     -- Nice -> Marseille (après-midi)
(9, 10, '08:45:00', '10:15:00', 35.00, 90, 'TER'),     -- Nantes -> Rennes
(10, 9, '19:30:00', '21:00:00', 35.00, 90, 'TER'),     -- Rennes -> Nantes

-- TRAJETS OUIGO (low-cost)
(1, 2, '05:45:00', '10:45:00', 45.00, 250, 'OUIGO'),   -- Paris -> Lyon
(2, 1, '21:30:00', '02:30:00', 45.00, 250, 'OUIGO'),   -- Lyon -> Paris
(1, 4, '06:15:00', '15:45:00', 65.00, 220, 'OUIGO'),   -- Paris -> Marseille

-- TRAJETS SUPPLEMENTAIRES
(6, 7, '12:00:00', '17:30:00', 120.00, 100, 'TGV'),    -- Strasbourg -> Lille
(7, 6, '13:30:00', '19:00:00', 120.00, 100, 'TGV'),    -- Lille -> Strasbourg
(9, 3, '11:00:00', '14:30:00', 70.00, 110, 'TER'),     -- Nantes -> Bordeaux
(3, 9, '15:45:00', '19:15:00', 70.00, 110, 'TER'),     -- Bordeaux -> Nantes
(11, 4, '09:30:00', '12:45:00', 45.00, 100, 'TER'),    -- Montpellier -> Marseille
(4, 11, '16:15:00', '19:30:00', 45.00, 100, 'TER'),    -- Marseille -> Montpellier
(12, 2, '07:45:00', '10:30:00', 65.00, 120, 'TER'),    -- Dijon -> Lyon
(2, 12, '18:45:00', '21:30:00', 65.00, 120, 'TER');    -- Lyon -> Dijon

-- ========================================
-- GENERATION DES VOYAGES (10 jours)
-- ========================================
INSERT INTO voyages (trajet_id, date_voyage, places_disponibles)
SELECT
    t.id,
    DATE_ADD(CURDATE(), INTERVAL d.day_offset DAY),
    t.nombre_places
FROM trajets t
CROSS JOIN (
    SELECT 0 as day_offset UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL
    SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL
    SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9
) d
WHERE t.actif = TRUE;

-- ========================================
-- INSERTION DES RESERVATIONS DE TEST
-- ========================================
INSERT INTO reservations (numero_reservation, utilisateur_id, voyage_id, nombre_places, prix_total, statut, date_reservation, mode_paiement, reference_paiement) VALUES

-- Réservations confirmées
('RES000001', 5, 1, 2, 179.00, 'CONFIRMEE', DATE_SUB(NOW(), INTERVAL 5 DAY), 'CARTE_BANCAIRE', 'PAY123456'),
('RES000002', 6, 8, 1, 89.50, 'CONFIRMEE', DATE_SUB(NOW(), INTERVAL 3 DAY), 'PAYPAL', 'PP789012'),
('RES000003', 7, 15, 1, 125.00, 'CONFIRMEE', DATE_SUB(NOW(), INTERVAL 2 DAY), 'CARTE_BANCAIRE', 'PAY345678'),
('RES000004', 8, 22, 3, 285.00, 'CONFIRMEE', DATE_SUB(NOW(), INTERVAL 7 DAY), 'CARTE_BANCAIRE', 'PAY901234'),
('RES000005', 9, 29, 1, 95.00, 'CONFIRMEE', DATE_SUB(NOW(), INTERVAL 1 DAY), 'VIREMENT', 'VIR567890'),

-- Réservations en attente
('RES000006', 10, 36, 2, 190.00, 'EN_ATTENTE', DATE_SUB(NOW(), INTERVAL 1 HOUR), 'CARTE_BANCAIRE', NULL),
('RES000007', 11, 43, 1, 45.00, 'EN_ATTENTE', DATE_SUB(NOW(), INTERVAL 3 HOUR), 'CARTE_BANCAIRE', NULL),
('RES000008', 12, 50, 4, 358.00, 'EN_ATTENTE', DATE_SUB(NOW(), INTERVAL 2 HOUR), 'PAYPAL', NULL),

-- Réservations annulées
('RES000009', 5, 57, 1, 89.50, 'ANNULEE', DATE_SUB(NOW(), INTERVAL 10 DAY), 'CARTE_BANCAIRE', 'PAY111111'),
('RES000010', 6, 64, 2, 250.00, 'ANNULEE', DATE_SUB(NOW(), INTERVAL 8 DAY), 'CARTE_BANCAIRE', 'PAY222222');

-- Mettre à jour les dates de confirmation
UPDATE reservations
SET date_confirmation = DATE_ADD(date_reservation, INTERVAL 1 HOUR)
WHERE statut = 'CONFIRMEE' AND date_confirmation IS NULL;

-- Mettre à jour les dates d'annulation
UPDATE reservations
SET date_annulation = DATE_ADD(date_reservation, INTERVAL 2 DAY),
    motif_annulation = 'Changement de programme'
WHERE statut = 'ANNULEE' AND date_annulation IS NULL;

-- ========================================
-- MISE A JOUR DES PLACES RESERVEES
-- ========================================
UPDATE voyages v
SET places_reservees = (
    SELECT COALESCE(SUM(r.nombre_places), 0)
    FROM reservations r
    WHERE r.voyage_id = v.id AND r.statut IN ('CONFIRMEE', 'EN_ATTENTE')
),
places_disponibles = (
    SELECT t.nombre_places - COALESCE(SUM(r.nombre_places), 0)
    FROM trajets t
    LEFT JOIN reservations r ON r.voyage_id = v.id AND r.statut IN ('CONFIRMEE', 'EN_ATTENTE')
    WHERE t.id = v.trajet_id
);

-- ========================================
-- CREATION DES VUES UTILES
-- ========================================

-- Vue des voyages avec détails complets
CREATE VIEW v_voyages_details AS
SELECT
    v.id as voyage_id,
    v.date_voyage,
    v.places_disponibles,
    v.places_reservees,
    v.statut as statut_voyage,
    t.id as trajet_id,
    t.heure_depart,
    t.heure_arrivee,
    t.duree_minutes,
    t.prix,
    t.type_train,
    gd.nom as gare_depart_nom,
    gd.ville as ville_depart,
    gd.code_gare as code_depart,
    ga.nom as gare_arrivee_nom,
    ga.ville as ville_arrivee,
    ga.code_gare as code_arrivee
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
WHERE v.statut = 'PROGRAMME' AND t.actif = TRUE;

-- Vue des réservations avec détails
CREATE VIEW v_reservations_details AS
SELECT
    r.id as reservation_id,
    r.numero_reservation,
    r.nombre_places,
    r.prix_total,
    r.statut as statut_reservation,
    r.date_reservation,
    r.date_confirmation,
    r.mode_paiement,
    CONCAT(u.prenom, ' ', u.nom) as nom_complet,
    u.email,
    u.telephone,
    v.date_voyage,
    CONCAT(gd.ville, ' → ', ga.ville) as trajet,
    t.heure_depart,
    t.heure_arrivee,
    t.type_train
FROM reservations r
JOIN utilisateurs u ON r.utilisateur_id = u.id
JOIN voyages v ON r.voyage_id = v.id
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id;

-- ========================================
-- PROCEDURES STOCKEES
-- ========================================

DELIMITER //

-- Procédure de recherche de voyages
CREATE PROCEDURE sp_rechercher_voyages(
    IN p_ville_depart VARCHAR(100),
    IN p_ville_arrivee VARCHAR(100),
    IN p_date_voyage DATE
)
BEGIN
    SELECT
        v.id as voyage_id,
        v.date_voyage,
        v.places_disponibles,
        v.statut,
        t.id as trajet_id,
        t.heure_depart,
        t.heure_arrivee,
        t.duree_minutes,
        t.prix,
        t.type_train,
        gd.nom as gare_depart,
        gd.ville as ville_depart,
        ga.nom as gare_arrivee,
        ga.ville as ville_arrivee
    FROM voyages v
    JOIN trajets t ON v.trajet_id = t.id
    JOIN gares gd ON t.gare_depart_id = gd.id
    JOIN gares ga ON t.gare_arrivee_id = ga.id
    WHERE gd.ville LIKE CONCAT('%', p_ville_depart, '%')
    AND ga.ville LIKE CONCAT('%', p_ville_arrivee, '%')
    AND v.date_voyage = p_date_voyage
    AND v.statut = 'PROGRAMME'
    AND v.places_disponibles > 0
    AND t.actif = TRUE
    ORDER BY t.heure_depart;
END//

-- Procédure de statistiques
CREATE PROCEDURE sp_statistiques()
BEGIN
    SELECT
        'Utilisateurs actifs' as metric,
        COUNT(*) as valeur
    FROM utilisateurs
    WHERE actif = TRUE

    UNION ALL

    SELECT
        'Réservations confirmées',
        COUNT(*)
    FROM reservations
    WHERE statut = 'CONFIRMEE'

    UNION ALL

    SELECT
        'Voyages programmés',
        COUNT(*)
    FROM voyages
    WHERE statut = 'PROGRAMME' AND date_voyage >= CURDATE()

    UNION ALL

    SELECT
        'Chiffre d\'affaires (€)',
        ROUND(SUM(prix_total), 2)
    FROM reservations
    WHERE statut = 'CONFIRMEE';
END//

DELIMITER ;

-- ========================================
-- VERIFICATION FINALE
-- ========================================

-- Afficher un résumé des données
SELECT 'RESUME DE LA BASE DE DONNEES TRAIN' as titre;

SELECT
    'Gares' as table_name,
    COUNT(*) as nombre_enregistrements,
    'Gares principales de France' as description
FROM gares

UNION ALL

SELECT
    'Utilisateurs',
    COUNT(*),
    CONCAT(
        SUM(CASE WHEN type_utilisateur = 'ADMINISTRATEUR' THEN 1 ELSE 0 END), ' admins, ',
        SUM(CASE WHEN type_utilisateur = 'EMPLOYE' THEN 1 ELSE 0 END), ' employés, ',
        SUM(CASE WHEN type_utilisateur = 'CLIENT' THEN 1 ELSE 0 END), ' clients'
    )
FROM utilisateurs

UNION ALL

SELECT
    'Trajets',
    COUNT(*),
    CONCAT(
        SUM(CASE WHEN type_train = 'TGV' THEN 1 ELSE 0 END), ' TGV, ',
        SUM(CASE WHEN type_train = 'TER' THEN 1 ELSE 0 END), ' TER, ',
        SUM(CASE WHEN type_train = 'OUIGO' THEN 1 ELSE 0 END), ' OUIGO'
    )
FROM trajets

UNION ALL

SELECT
    'Voyages',
    COUNT(*),
    CONCAT('Programmés pour ', COUNT(DISTINCT date_voyage), ' jours')
FROM voyages
WHERE date_voyage >= CURDATE()

UNION ALL

SELECT
    'Réservations',
    COUNT(*),
    CONCAT(
        SUM(CASE WHEN statut = 'CONFIRMEE' THEN 1 ELSE 0 END), ' confirmées, ',
        SUM(CASE WHEN statut = 'EN_ATTENTE' THEN 1 ELSE 0 END), ' en attente'
    )
FROM reservations;

-- Comptes de test disponibles
SELECT 'COMPTES DE TEST DISPONIBLES' as info;
SELECT
    CONCAT(prenom, ' ', nom) as nom_complet,
    email,
    type_utilisateur as role,
    'password' as mot_de_passe,
    CASE WHEN actif THEN 'ACTIF' ELSE 'INACTIF' END as statut
FROM utilisateurs
ORDER BY
    CASE type_utilisateur
        WHEN 'ADMINISTRATEUR' THEN 1
        WHEN 'EMPLOYE' THEN 2
        WHEN 'CLIENT' THEN 3
    END,
    email;

COMMIT;

-- Message de fin
SELECT
    'BASE DE DONNEES TRAIN INITIALISEE AVEC SUCCES' as message,
    NOW() as date_creation,
    'Prête pour les tests de l\'application TrainSystem' as statut;
