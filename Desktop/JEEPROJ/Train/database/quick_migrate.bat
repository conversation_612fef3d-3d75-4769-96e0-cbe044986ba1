@echo off
echo ========================================
echo    MIGRATION RAPIDE BASE TRAIN
echo ========================================
echo.

cd /d "%~dp0"

echo 🚀 Démarrage de la migration automatique...
echo.

REM Vérifier si les fichiers existent
if not exist "migration_train.sql" (
    echo ❌ Fichier migration_train.sql non trouvé
    echo Assurez-vous que tous les fichiers sont dans le même dossier
    pause
    exit /b 1
)

if not exist "insert_sample_data.sql" (
    echo ❌ Fichier insert_sample_data.sql non trouvé
    echo Assurez-vous que tous les fichiers sont dans le même dossier
    pause
    exit /b 1
)

echo ✅ Fichiers de migration trouvés
echo.

REM Essayer différentes méthodes de connexion MySQL
echo 🔍 Recherche de MySQL...

REM Méthode 1: MySQL dans le PATH
mysql --version >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo ✅ MySQL trouvé dans le PATH système
    set MYSQL_CMD=mysql
    goto :execute_migration
)

REM Méthode 2: XAMPP
if exist "C:\xampp\mysql\bin\mysql.exe" (
    echo ✅ MySQL trouvé dans XAMPP
    set MYSQL_CMD="C:\xampp\mysql\bin\mysql.exe"
    goto :execute_migration
)

REM Méthode 3: WAMP64
for /d %%i in ("C:\wamp64\bin\mysql\mysql*") do (
    if exist "%%i\bin\mysql.exe" (
        echo ✅ MySQL trouvé dans WAMP64
        set MYSQL_CMD="%%i\bin\mysql.exe"
        goto :execute_migration
    )
)

REM Méthode 4: Installation standard MySQL
if exist "C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" (
    echo ✅ MySQL trouvé dans Program Files
    set MYSQL_CMD="C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe"
    goto :execute_migration
)

REM Méthode 5: MySQL Server 5.7
if exist "C:\Program Files\MySQL\MySQL Server 5.7\bin\mysql.exe" (
    echo ✅ MySQL trouvé dans Program Files (5.7)
    set MYSQL_CMD="C:\Program Files\MySQL\MySQL Server 5.7\bin\mysql.exe"
    goto :execute_migration
)

echo ❌ MySQL non trouvé automatiquement
echo.
echo 💡 Solutions alternatives :
echo.
echo 1. 🌐 Utiliser phpMyAdmin (Recommandé)
echo    - Ouvrez http://localhost/phpmyadmin
echo    - Sélectionnez la base 'train'
echo    - Cliquez sur 'SQL'
echo    - Copiez le contenu de migration_train.sql
echo    - Cliquez 'Exécuter'
echo    - Répétez avec insert_sample_data.sql
echo.
echo 2. 🔧 Installer MySQL et l'ajouter au PATH
echo.
echo 3. 🚀 Démarrer XAMPP/WAMP et relancer ce script
echo.
pause
exit /b 1

:execute_migration
echo.
echo 🔄 Exécution de la migration...
echo.

REM Test de connexion
echo 1. Test de connexion MySQL...
%MYSQL_CMD% -hlocalhost -uroot -e "SELECT 1;" >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ Impossible de se connecter à MySQL
    echo.
    echo Vérifiez que :
    echo - MySQL/MariaDB est démarré
    echo - L'utilisateur root existe
    echo - Aucun mot de passe n'est requis (ou modifiez le script)
    echo.
    echo 💡 Essayez de démarrer XAMPP/WAMP d'abord
    pause
    exit /b 1
)
echo ✅ Connexion MySQL réussie

REM Création de la base
echo 2. Création de la base de données...
%MYSQL_CMD% -hlocalhost -uroot -e "CREATE DATABASE IF NOT EXISTS train CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur lors de la création de la base
    pause
    exit /b 1
)
echo ✅ Base 'train' créée/vérifiée

REM Migration des tables
echo 3. Migration des tables...
%MYSQL_CMD% -hlocalhost -uroot train < migration_train.sql
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur lors de la migration des tables
    echo.
    echo Vérifiez le fichier migration_train.sql
    pause
    exit /b 1
)
echo ✅ Tables migrées avec succès

REM Insertion des données
echo 4. Insertion des données de test...
%MYSQL_CMD% -hlocalhost -uroot train < insert_sample_data.sql
if %ERRORLEVEL% neq 0 (
    echo ⚠️ Erreur lors de l'insertion des données
    echo Les tables sont créées mais sans données de test
) else (
    echo ✅ Données de test insérées
)

REM Vérification finale
echo 5. Vérification finale...
echo.
echo 📊 Tables créées dans la base 'train' :
%MYSQL_CMD% -hlocalhost -uroot train -e "SHOW TABLES;"

echo.
echo 📈 Nombre d'enregistrements par table :
%MYSQL_CMD% -hlocalhost -uroot train -e "SELECT 'gares' as table_name, COUNT(*) as records FROM gares UNION ALL SELECT 'utilisateurs', COUNT(*) FROM utilisateurs UNION ALL SELECT 'trajets', COUNT(*) FROM trajets UNION ALL SELECT 'voyages', COUNT(*) FROM voyages UNION ALL SELECT 'reservations', COUNT(*) FROM reservations;"

echo.
echo ========================================
echo    🎉 MIGRATION TERMINEE AVEC SUCCES !
echo ========================================
echo.
echo ✅ Base de données 'train' prête pour TrainSystem
echo.
echo 🎯 Prochaines étapes :
echo   1. Lancez votre application TrainSystem
echo   2. Testez la connexion avec les comptes de test
echo   3. Vérifiez les données dans phpMyAdmin
echo.
echo 🔐 Comptes de test disponibles :
echo   👨‍💼 Admin : <EMAIL> / password
echo   👤 Client : <EMAIL> / password
echo   🧪 Demo  : <EMAIL> / password
echo.
echo 🌐 phpMyAdmin : http://localhost/phpmyadmin
echo 📊 Base de données : train
echo.
echo 🚂 Votre système de réservation est prêt !
echo.

pause
