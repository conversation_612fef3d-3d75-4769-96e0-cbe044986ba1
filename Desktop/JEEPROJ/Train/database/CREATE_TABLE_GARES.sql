-- ========================================
-- SCRIPT DE CRÉATION DE LA TABLE GARES
-- ========================================

-- Utiliser la base de données train
USE train;

-- Supprimer la table si elle existe déjà (optionnel)
-- DROP TABLE IF EXISTS gares;

-- Créer la table gares
CREATE TABLE IF NOT EXISTS gares (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    ville VARCHAR(100) NOT NULL,
    code_gare VARCHAR(10) NOT NULL UNIQUE,
    adresse TEXT,
    code_postal VARCHAR(10),
    latitude DECIMAL(10, 6),
    longitude DECIMAL(10, 6),
    active BOOLEAN DEFAULT TRUE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Index pour améliorer les performances
    INDEX idx_code_gare (code_gare),
    INDEX idx_ville (ville),
    INDEX idx_active (active)
);

-- Insérer quelques données de test
INSERT INTO gares (nom, ville, code_gare, adresse, code_postal, latitude, longitude, active) VALUES
('Gare de Paris-Nord', 'Paris', 'PARN', '18 Rue de Dunkerque', '75010', 48.8809, 2.3553, TRUE),
('Gare de Lyon-Part-Dieu', 'Lyon', 'LYONPD', 'Place Charles Béraudier', '69003', 45.7606, 4.8598, TRUE),
('Gare de Marseille-Saint-Charles', 'Marseille', 'MARSC', 'Square Narvik', '13001', 43.3030, 5.3808, TRUE),
('Gare de Toulouse-Matabiau', 'Toulouse', 'TOLMAT', 'Boulevard Pierre Semard', '31000', 43.6108, 1.4537, TRUE),
('Gare de Nice-Ville', 'Nice', 'NICEVI', 'Avenue Thiers', '06000', 43.7044, 7.2615, TRUE),
('Gare de Strasbourg-Ville', 'Strasbourg', 'STRAVI', 'Place de la Gare', '67000', 48.5847, 7.7339, TRUE),
('Gare de Bordeaux-Saint-Jean', 'Bordeaux', 'BORSJ', 'Rue Charles Domercq', '33800', 44.8262, -0.5565, TRUE),
('Gare de Lille-Europe', 'Lille', 'LILEUR', 'Parvis François Mitterrand', '59000', 50.6388, 3.0757, TRUE),
('Gare de Nantes', 'Nantes', 'NANTES', '27 Boulevard de Stalingrad', '44000', 47.2173, -1.5415, TRUE),
('Gare de Rennes', 'Rennes', 'RENNES', 'Place de la Gare', '35000', 48.1036, -1.6722, TRUE);

-- Afficher les données insérées
SELECT * FROM gares ORDER BY nom;

-- Afficher le nombre total de gares
SELECT COUNT(*) as total_gares FROM gares;

-- Afficher les statistiques
SELECT 
    COUNT(*) as total_gares,
    COUNT(CASE WHEN active = TRUE THEN 1 END) as gares_actives,
    COUNT(CASE WHEN active = FALSE THEN 1 END) as gares_inactives,
    COUNT(CASE WHEN latitude IS NOT NULL AND longitude IS NOT NULL THEN 1 END) as gares_avec_gps
FROM gares;

COMMIT;
