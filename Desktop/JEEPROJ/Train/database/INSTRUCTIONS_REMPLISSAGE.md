# 📊 Instructions pour remplir la base de données avec des données réalistes

## 🎯 Objectif
Remplir votre base de données MySQL avec des données réalistes pour tester toutes les fonctionnalités admin de votre application TrainSystem.

## 📋 Étapes à suivre

### 1. Ouvrir phpMyAdmin
- Allez sur `http://localhost/phpmyadmin` (ou votre URL phpMyAdmin)
- Connectez-vous avec vos identifiants MySQL

### 2. Sélectionner la base de données
- Cliquez sur la base de données `train` dans la liste de gauche
- Si elle n'existe pas, créez-la d'abord

### 3. Exécuter le script SQL
- Cliquez sur l'onglet **"SQL"** en haut
- Ouvrez le fichier `PHPMYADMIN_COPY_PASTE.sql` dans un éditeur de texte
- **Copiez tout le contenu** du fichier
- **Collez-le** dans la zone de texte de phpMyAdmin
- Cliquez sur **"Exécuter"**

### 4. Vérifier l'exécution
Le script va :
- ✅ Vider les tables existantes
- ✅ Insérer 15 gares principales françaises
- ✅ Créer 17 utilisateurs (2 admins, 2 employés, 13 clients)
- ✅ Ajouter 25 trajets réalistes (TGV, TER, Intercités)
- ✅ Générer 375 voyages (15 jours de données)
- ✅ Créer 23 réservations avec différents statuts
- ✅ Mettre à jour les statistiques

## 🔑 Comptes de test créés

### Administrateurs
- **Email:** `<EMAIL>` | **Mot de passe:** `password`
- **Email:** `<EMAIL>` | **Mot de passe:** `password`

### Employés
- **Email:** `<EMAIL>` | **Mot de passe:** `password`
- **Email:** `<EMAIL>` | **Mot de passe:** `password`

### Clients (exemples)
- **Email:** `<EMAIL>` | **Mot de passe:** `password`
- **Email:** `<EMAIL>` | **Mot de passe:** `password`
- Et 11 autres clients...

## 📈 Données créées pour tester les fonctionnalités admin

### 🚉 Gares (15)
- Paris (Gare du Nord, Gare de Lyon, Montparnasse, Gare de l'Est)
- Lyon Part-Dieu
- Marseille Saint-Charles
- Toulouse Matabiau
- Bordeaux Saint-Jean
- Lille, Strasbourg, Nantes, Rennes, Nice, Montpellier, Dijon

### 🚄 Trajets (25)
- **TGV:** Paris ↔ grandes villes (Lyon, Marseille, Bordeaux, Toulouse, Lille, Strasbourg)
- **TER:** Liaisons régionales et locales
- **Intercités:** Trains de nuit (Paris → Nice)

### 🎫 Réservations (23)
- **Confirmées:** 15 réservations
- **En attente:** 3 réservations (pour tester la validation)
- **Annulées:** 2 réservations (pour tester les remboursements)
- **Remboursées:** 1 réservation
- **Différents modes de paiement:** Carte bancaire, PayPal, Virement, Espèces, Chèque

### 📊 Statuts de voyages variés
- **Programmés:** Voyages normaux
- **Complets:** 4 voyages sans places disponibles
- **Annulés:** 2 voyages annulés
- **Retardés:** 2 voyages en retard

## 🧪 Fonctionnalités admin à tester

### Dashboard
- ✅ Statistiques en temps réel
- ✅ Graphiques et métriques
- ✅ Voyages à venir
- ✅ Réservations récentes

### Gestion des utilisateurs
- ✅ Liste des clients actifs/inactifs
- ✅ Détails des comptes
- ✅ Historique des connexions

### Gestion des voyages
- ✅ Planification des voyages
- ✅ Modification des statuts
- ✅ Gestion des places disponibles

### Gestion des réservations
- ✅ Validation des réservations en attente
- ✅ Traitement des annulations
- ✅ Gestion des remboursements
- ✅ Différents modes de paiement

### Gestion des trajets
- ✅ Création/modification de trajets
- ✅ Gestion des horaires et prix
- ✅ Activation/désactivation

## 🚀 Après l'exécution

1. **Redémarrez votre serveur** si nécessaire
2. **Connectez-vous en tant qu'admin** : `<EMAIL>` / `password`
3. **Testez le dashboard** : `http://localhost:8080/Train/admin`
4. **Explorez toutes les fonctionnalités** avec les données réalistes

## ⚠️ Notes importantes

- Tous les mots de passe sont : `password`
- Les dates sont générées dynamiquement (15 prochains jours)
- Les données sont cohérentes et réalistes
- Vous pouvez réexécuter le script pour réinitialiser les données

## 🔧 En cas de problème

Si vous rencontrez des erreurs :
1. Vérifiez que la base `train` existe
2. Assurez-vous que toutes les tables sont créées
3. Vérifiez les contraintes de clés étrangères
4. Consultez les logs d'erreur de phpMyAdmin

---

**Bon test de votre application TrainSystem ! 🚄**
