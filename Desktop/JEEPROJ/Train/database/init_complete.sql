-- ========================================
-- SCRIPT COMPLET D'INITIALISATION - TRAINSYSTEM
-- Exécutez ce script dans phpMyAdmin pour initialiser complètement la base
-- ========================================

-- Créer la base de données si elle n'existe pas
CREATE DATABASE IF NOT EXISTS train CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE train;

-- Désactiver les vérifications de clés étrangères temporairement
SET FOREIGN_KEY_CHECKS = 0;

-- Supprimer les tables existantes
DROP TABLE IF EXISTS reservations;
DROP TABLE IF EXISTS voyages;
DROP TABLE IF EXISTS trajets;
DROP TABLE IF EXISTS utilisateurs;
DROP TABLE IF EXISTS gares;

-- Réactiver les vérifications
SET FOREIGN_KEY_CHECKS = 1;

-- ========================================
-- CREATION DES TABLES
-- ========================================

-- Table des gares
CREATE TABLE gares (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    ville VARCHAR(100) NOT NULL,
    code_gare VARCHAR(10) UNIQUE NOT NULL,
    adresse VARCHAR(255),
    code_postal VARCHAR(10),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    active BOOLEAN DEFAULT TRUE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_ville (ville),
    INDEX idx_code_gare (code_gare)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des utilisateurs
CREATE TABLE utilisateurs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    mot_de_passe VARCHAR(255) NOT NULL,
    telephone VARCHAR(20),
    date_naissance DATE,
    adresse VARCHAR(255),
    ville VARCHAR(100),
    code_postal VARCHAR(10),
    type_utilisateur ENUM('CLIENT', 'EMPLOYE', 'ADMINISTRATEUR') DEFAULT 'CLIENT',
    actif BOOLEAN DEFAULT TRUE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    derniere_connexion TIMESTAMP NULL,
    INDEX idx_email (email),
    INDEX idx_type (type_utilisateur)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des trajets
CREATE TABLE trajets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    gare_depart_id BIGINT NOT NULL,
    gare_arrivee_id BIGINT NOT NULL,
    heure_depart TIME NOT NULL,
    heure_arrivee TIME NOT NULL,
    prix DECIMAL(10, 2) NOT NULL,
    nombre_places INT NOT NULL DEFAULT 200,
    type_train ENUM('TGV', 'INTERCITES', 'TER', 'OUIGO') DEFAULT 'TER',
    actif BOOLEAN DEFAULT TRUE,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (gare_depart_id) REFERENCES gares(id),
    FOREIGN KEY (gare_arrivee_id) REFERENCES gares(id),
    INDEX idx_gare_depart (gare_depart_id),
    INDEX idx_gare_arrivee (gare_arrivee_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des voyages
CREATE TABLE voyages (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    trajet_id BIGINT NOT NULL,
    date_voyage DATE NOT NULL,
    places_disponibles INT NOT NULL,
    places_reservees INT DEFAULT 0,
    statut ENUM('PROGRAMME', 'EN_COURS', 'TERMINE', 'ANNULE', 'RETARDE') DEFAULT 'PROGRAMME',
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (trajet_id) REFERENCES trajets(id),
    UNIQUE KEY unique_trajet_date (trajet_id, date_voyage),
    INDEX idx_date_voyage (date_voyage)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table des réservations
CREATE TABLE reservations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    numero_reservation VARCHAR(20) UNIQUE NOT NULL,
    utilisateur_id BIGINT NOT NULL,
    voyage_id BIGINT NOT NULL,
    nombre_places INT NOT NULL DEFAULT 1,
    prix_total DECIMAL(10, 2) NOT NULL,
    statut ENUM('EN_ATTENTE', 'CONFIRMEE', 'ANNULEE', 'REMBOURSEE') DEFAULT 'EN_ATTENTE',
    date_reservation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_confirmation TIMESTAMP NULL,
    date_annulation TIMESTAMP NULL,
    motif_annulation TEXT,
    mode_paiement ENUM('CARTE_BANCAIRE', 'PAYPAL', 'VIREMENT', 'ESPECES') DEFAULT 'CARTE_BANCAIRE',
    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id),
    FOREIGN KEY (voyage_id) REFERENCES voyages(id),
    INDEX idx_numero_reservation (numero_reservation),
    INDEX idx_utilisateur (utilisateur_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ========================================
-- INSERTION DES DONNEES
-- ========================================

-- Gares principales
INSERT INTO gares (nom, ville, code_gare, adresse, code_postal, latitude, longitude) VALUES
('Gare de Paris-Montparnasse', 'Paris', 'PAR01', '17 Boulevard de Vaugirard', '75015', 48.8404, 2.3188),
('Gare de Lyon Part-Dieu', 'Lyon', 'LYO01', '3 Place Charles Béraudier', '69003', 45.7606, 4.8598),
('Gare Saint-Jean', 'Bordeaux', 'BOR01', 'Cours de la Marne', '33800', 44.8258, -0.5570),
('Gare Saint-Charles', 'Marseille', 'MAR01', 'Square Narvik', '13001', 43.3030, 5.3811),
('Gare Matabiau', 'Toulouse', 'TOU01', '64 Boulevard Pierre Semard', '31000', 43.6108, 1.4537),
('Gare Centrale', 'Strasbourg', 'STR01', '20 Place de la Gare', '67000', 48.5847, 7.7339),
('Gare Lille-Flandres', 'Lille', 'LIL01', '3 Place des Buisses', '59000', 50.6365, 3.0708),
('Gare de Nice-Ville', 'Nice', 'NIC01', 'Avenue Thiers', '06000', 43.7048, 7.2620);

-- Utilisateurs (mot de passe: "password" haché avec BCrypt)
INSERT INTO utilisateurs (nom, prenom, email, mot_de_passe, telephone, type_utilisateur, actif) VALUES
('Admin', 'Système', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456789', 'ADMINISTRATEUR', TRUE),
('Dupont', 'Jean', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456790', 'CLIENT', TRUE),
('Martin', 'Marie', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456791', 'CLIENT', TRUE),
('Bernard', 'Pierre', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456792', 'CLIENT', TRUE),
('Dubois', 'Sophie', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456793', 'CLIENT', TRUE);

-- Trajets principaux
INSERT INTO trajets (gare_depart_id, gare_arrivee_id, heure_depart, heure_arrivee, prix, nombre_places, type_train) VALUES
(1, 2, '08:00:00', '12:30:00', 89.50, 200, 'TGV'),     -- Paris -> Lyon
(2, 1, '14:00:00', '18:30:00', 89.50, 200, 'TGV'),     -- Lyon -> Paris
(1, 3, '09:15:00', '15:45:00', 125.00, 150, 'TGV'),    -- Paris -> Bordeaux
(3, 1, '16:30:00', '23:00:00', 125.00, 150, 'TGV'),    -- Bordeaux -> Paris
(1, 4, '07:30:00', '16:15:00', 135.00, 180, 'TGV'),    -- Paris -> Marseille
(4, 1, '15:45:00', '00:30:00', 135.00, 180, 'TGV'),    -- Marseille -> Paris
(1, 5, '10:00:00', '17:20:00', 110.00, 160, 'TGV'),    -- Paris -> Toulouse
(5, 1, '18:00:00', '01:20:00', 110.00, 160, 'TGV'),    -- Toulouse -> Paris
(2, 4, '07:30:00', '13:15:00', 95.00, 180, 'TGV'),     -- Lyon -> Marseille
(4, 2, '15:45:00', '21:30:00', 95.00, 180, 'TGV'),     -- Marseille -> Lyon
(4, 8, '06:30:00', '09:45:00', 55.00, 120, 'TER'),     -- Marseille -> Nice
(8, 4, '18:15:00', '21:30:00', 55.00, 120, 'TER');     -- Nice -> Marseille

-- Voyages pour les 7 prochains jours
INSERT INTO voyages (trajet_id, date_voyage, places_disponibles)
SELECT 
    t.id,
    DATE_ADD(CURDATE(), INTERVAL d.day_offset DAY),
    t.nombre_places
FROM trajets t
CROSS JOIN (
    SELECT 0 as day_offset UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL 
    SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6
) d;

-- Réservations de test
INSERT INTO reservations (numero_reservation, utilisateur_id, voyage_id, nombre_places, prix_total, statut) VALUES
('RES000001', 2, 1, 2, 179.00, 'CONFIRMEE'),
('RES000002', 3, 8, 1, 89.50, 'CONFIRMEE'),
('RES000003', 4, 15, 1, 125.00, 'EN_ATTENTE'),
('RES000004', 5, 22, 2, 270.00, 'CONFIRMEE');

-- Mettre à jour les places réservées
UPDATE voyages v 
SET places_reservees = (
    SELECT COALESCE(SUM(r.nombre_places), 0)
    FROM reservations r 
    WHERE r.voyage_id = v.id AND r.statut IN ('CONFIRMEE', 'EN_ATTENTE')
),
places_disponibles = nombre_places - (
    SELECT COALESCE(SUM(r.nombre_places), 0)
    FROM reservations r 
    JOIN trajets t ON v.trajet_id = t.id
    WHERE r.voyage_id = v.id AND r.statut IN ('CONFIRMEE', 'EN_ATTENTE')
);

-- ========================================
-- VERIFICATION
-- ========================================
SELECT 'Initialisation terminée' as status, NOW() as timestamp;
SELECT 'Gares' as table_name, COUNT(*) as count FROM gares
UNION ALL SELECT 'Utilisateurs', COUNT(*) FROM utilisateurs
UNION ALL SELECT 'Trajets', COUNT(*) FROM trajets
UNION ALL SELECT 'Voyages', COUNT(*) FROM voyages
UNION ALL SELECT 'Réservations', COUNT(*) FROM reservations;

COMMIT;
