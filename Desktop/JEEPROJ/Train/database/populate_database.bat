@echo off
chcp 65001 >nul
cls

echo ========================================
echo    REMPLISSAGE BASE DE DONNEES TRAIN
echo    Données réalistes pour tests admin
echo ========================================
echo.

REM Vérifier si MySQL est disponible
where mysql >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ MySQL n'est pas installé ou pas dans le PATH
    echo.
    echo Solutions possibles :
    echo 1. Installer MySQL/MariaDB
    echo 2. Utiliser XAMPP/WAMP/MAMP
    echo 3. Ajouter MySQL au PATH système
    echo.
    pause
    exit /b 1
)

echo ✅ MySQL détecté

REM Tester la connexion
echo.
echo 🔍 Test de connexion à MySQL...
mysql -hlocalhost -uroot -e "SELECT 'Connexion réussie' as status;" 2>nul
if %ERRORLEVEL% neq 0 (
    echo ❌ Impossible de se connecter à MySQL
    echo.
    echo Vérifiez que :
    echo - MySQL/MariaDB est démarré
    echo - L'utilisateur 'root' existe
    echo - Le mot de passe est vide (ou modifiez le script)
    echo.
    pause
    exit /b 1
)

echo ✅ Connexion MySQL réussie

REM Exécuter le script de remplissage
echo.
echo 📊 Remplissage de la base avec des données réalistes...
echo.

mysql -hlocalhost -uroot < populate_realistic_data.sql
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur lors du remplissage de la base
    echo.
    echo Vérifiez le fichier populate_realistic_data.sql
    pause
    exit /b 1
)

echo ✅ Base de données remplie avec succès !

REM Afficher un résumé
echo.
echo 📈 Résumé des données insérées :
echo.
mysql -hlocalhost -uroot train -e "
SELECT 
    'Gares' as table_name, 
    COUNT(*) as nombre_enregistrements
FROM gares
UNION ALL
SELECT 'Utilisateurs', COUNT(*) FROM utilisateurs
UNION ALL
SELECT 'Trajets', COUNT(*) FROM trajets
UNION ALL
SELECT 'Voyages', COUNT(*) FROM voyages
UNION ALL
SELECT 'Réservations', COUNT(*) FROM reservations;
"

echo.
echo 👥 Comptes de test disponibles :
echo.
mysql -hlocalhost -uroot train -e "
SELECT 
    CONCAT(prenom, ' ', nom) as nom_complet,
    email,
    type_utilisateur as role,
    'password' as mot_de_passe
FROM utilisateurs 
WHERE type_utilisateur IN ('ADMINISTRATEUR', 'EMPLOYE')
ORDER BY type_utilisateur, email;
"

echo.
echo ========================================
echo    REMPLISSAGE TERMINE AVEC SUCCES
echo ========================================
echo.
echo 🚀 Votre base de données contient maintenant :
echo    • 15 gares principales françaises
echo    • 17 utilisateurs (2 admins, 2 employés, 13 clients)
echo    • 25 trajets réalistes (TGV, TER, Intercités)
echo    • 375 voyages (15 jours de données)
echo    • 23 réservations avec différents statuts
echo.
echo 🔑 Comptes admin pour tester :
echo    • <EMAIL> / password
echo    • <EMAIL> / password
echo.
echo 🌐 Accédez à l'application : http://localhost:8080/Train
echo.
pause
