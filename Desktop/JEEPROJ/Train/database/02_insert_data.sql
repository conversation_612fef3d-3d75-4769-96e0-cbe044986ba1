-- ========================================
-- SCRIPT D'INSERTION DES DONNEES - TRAINSYSTEM
-- ========================================

USE train;

-- ========================================
-- INSERTION DES GARES
-- ========================================

INSERT INTO gares (nom, ville, code_gare, adresse, code_postal, latitude, longitude) VALUES
('Gare de Paris-Montparnasse', 'Paris', 'PAR01', '17 Boulevard de Vaugirard, Paris', '75015', 48.8404, 2.3188),
('Gare de Lyon Part-Dieu', 'Lyon', 'LYO01', '3 Place Charles <PERSON>, Lyon', '69003', 45.7606, 4.8598),
('Gare Saint-Jean', 'Bordeaux', 'BOR01', 'Cours de la Marne, Bordeaux', '33800', 44.8258, -0.5570),
('Gare Saint-Charles', 'Marseille', 'MAR01', 'Square Narvik, Marseille', '13001', 43.3030, 5.3811),
('Gare Matabiau', 'Toulouse', 'TOU01', '64 Boulevard Pierre Semard, Toulouse', '31000', 43.6108, 1.4537),
('Gare Centrale', 'Strasbourg', 'STR01', '20 Place de la Gare, Strasbourg', '67000', 48.5847, 7.7339),
('Gare Lille-Flandres', 'Lille', 'LIL01', '3 Place des Buisses, Lille', '59000', 50.6365, 3.0708),
('Gare de Nice-Ville', 'Nice', 'NIC01', 'Avenue Thiers, Nice', '06000', 43.7048, 7.2620),
('Gare de Nantes', 'Nantes', 'NAN01', '27 Boulevard de Stalingrad, Nantes', '44000', 47.2173, -1.5412),
('Gare de Rennes', 'Rennes', 'REN01', '20 Place de la Gare, Rennes', '35000', 48.1030, -1.6720);

-- ========================================
-- INSERTION DES UTILISATEURS
-- ========================================

-- Mot de passe haché pour "password" avec BCrypt
-- $2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi

INSERT INTO utilisateurs (nom, prenom, email, mot_de_passe, telephone, type_utilisateur, actif) VALUES
('Admin', 'Système', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456789', 'ADMINISTRATEUR', TRUE),
('Dupont', 'Jean', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456790', 'CLIENT', TRUE),
('Martin', 'Marie', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456791', 'CLIENT', TRUE),
('Bernard', 'Pierre', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456792', 'CLIENT', TRUE),
('Dubois', 'Sophie', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456793', 'CLIENT', TRUE),
('Moreau', 'Luc', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456794', 'EMPLOYE', TRUE),
('Petit', 'Anne', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456795', 'CLIENT', TRUE),
('Roux', 'Paul', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '0123456796', 'CLIENT', TRUE);

-- ========================================
-- INSERTION DES TRAJETS
-- ========================================

INSERT INTO trajets (gare_depart_id, gare_arrivee_id, heure_depart, heure_arrivee, prix, nombre_places, type_train) VALUES
-- Paris vers autres villes
(1, 2, '08:00:00', '12:30:00', 89.50, 200, 'TGV'),     -- Paris -> Lyon
(1, 3, '09:15:00', '15:45:00', 125.00, 150, 'TGV'),    -- Paris -> Bordeaux
(1, 4, '07:30:00', '16:15:00', 135.00, 180, 'TGV'),    -- Paris -> Marseille
(1, 5, '10:00:00', '17:20:00', 110.00, 160, 'TGV'),    -- Paris -> Toulouse
(1, 6, '06:45:00', '10:30:00', 95.00, 140, 'TGV'),     -- Paris -> Strasbourg
(1, 7, '07:00:00', '08:00:00', 45.00, 120, 'TGV'),     -- Paris -> Lille
(1, 9, '08:30:00', '11:45:00', 75.00, 130, 'TGV'),     -- Paris -> Nantes

-- Retours vers Paris
(2, 1, '14:00:00', '18:30:00', 89.50, 200, 'TGV'),     -- Lyon -> Paris
(3, 1, '16:30:00', '23:00:00', 125.00, 150, 'TGV'),    -- Bordeaux -> Paris
(4, 1, '15:45:00', '00:30:00', 135.00, 180, 'TGV'),    -- Marseille -> Paris
(5, 1, '18:00:00', '01:20:00', 110.00, 160, 'TGV'),    -- Toulouse -> Paris
(6, 1, '17:15:00', '21:00:00', 95.00, 140, 'TGV'),     -- Strasbourg -> Paris
(7, 1, '19:00:00', '20:00:00', 45.00, 120, 'TGV'),     -- Lille -> Paris
(9, 1, '16:15:00', '19:30:00', 75.00, 130, 'TGV'),     -- Nantes -> Paris

-- Trajets inter-régions
(2, 4, '07:30:00', '13:15:00', 95.00, 180, 'TGV'),     -- Lyon -> Marseille
(4, 2, '15:45:00', '21:30:00', 95.00, 180, 'TGV'),     -- Marseille -> Lyon
(2, 5, '09:00:00', '15:30:00', 85.00, 160, 'INTERCITES'), -- Lyon -> Toulouse
(5, 2, '16:00:00', '22:30:00', 85.00, 160, 'INTERCITES'), -- Toulouse -> Lyon
(3, 5, '08:15:00', '10:45:00', 65.00, 140, 'TER'),     -- Bordeaux -> Toulouse
(5, 3, '17:30:00', '20:00:00', 65.00, 140, 'TER'),     -- Toulouse -> Bordeaux
(6, 7, '12:00:00', '17:30:00', 120.00, 100, 'TGV'),    -- Strasbourg -> Lille
(7, 6, '13:30:00', '19:00:00', 120.00, 100, 'TGV'),    -- Lille -> Strasbourg

-- Trajets régionaux
(8, 4, '06:30:00', '09:45:00', 55.00, 120, 'TER'),     -- Nice -> Marseille
(4, 8, '18:15:00', '21:30:00', 55.00, 120, 'TER'),     -- Marseille -> Nice
(9, 3, '11:00:00', '14:30:00', 70.00, 110, 'TER'),     -- Nantes -> Bordeaux
(3, 9, '15:45:00', '19:15:00', 70.00, 110, 'TER'),     -- Bordeaux -> Nantes
(10, 9, '08:45:00', '10:15:00', 35.00, 90, 'TER'),     -- Rennes -> Nantes
(9, 10, '19:30:00', '21:00:00', 35.00, 90, 'TER');     -- Nantes -> Rennes

-- ========================================
-- INSERTION DES VOYAGES (7 jours à partir d'aujourd'hui)
-- ========================================

-- Générer des voyages pour les 7 prochains jours
INSERT INTO voyages (trajet_id, date_voyage, places_disponibles)
SELECT 
    t.id,
    DATE_ADD(CURDATE(), INTERVAL d.day_offset DAY),
    t.nombre_places
FROM trajets t
CROSS JOIN (
    SELECT 0 as day_offset UNION ALL
    SELECT 1 UNION ALL
    SELECT 2 UNION ALL
    SELECT 3 UNION ALL
    SELECT 4 UNION ALL
    SELECT 5 UNION ALL
    SELECT 6
) d
WHERE t.actif = TRUE;

-- ========================================
-- INSERTION DE QUELQUES RESERVATIONS DE TEST
-- ========================================

-- Réservations pour les utilisateurs de test
INSERT INTO reservations (numero_reservation, utilisateur_id, voyage_id, nombre_places, prix_total, statut, mode_paiement) VALUES
('RES001', 2, 1, 2, 179.00, 'CONFIRMEE', 'CARTE_BANCAIRE'),
('RES002', 3, 8, 1, 89.50, 'CONFIRMEE', 'PAYPAL'),
('RES003', 4, 15, 3, 285.00, 'EN_ATTENTE', 'CARTE_BANCAIRE'),
('RES004', 5, 22, 1, 95.00, 'CONFIRMEE', 'CARTE_BANCAIRE'),
('RES005', 7, 29, 2, 190.00, 'CONFIRMEE', 'VIREMENT'),
('RES006', 8, 36, 1, 45.00, 'ANNULEE', 'CARTE_BANCAIRE');

-- ========================================
-- MISE A JOUR DES STATISTIQUES
-- ========================================

-- Mettre à jour les compteurs de places pour les voyages avec réservations
UPDATE voyages v 
SET places_reservees = (
    SELECT COALESCE(SUM(r.nombre_places), 0)
    FROM reservations r 
    WHERE r.voyage_id = v.id 
    AND r.statut IN ('CONFIRMEE', 'EN_ATTENTE')
),
places_disponibles = nombre_places - (
    SELECT COALESCE(SUM(r.nombre_places), 0)
    FROM reservations r 
    JOIN trajets t ON v.trajet_id = t.id
    WHERE r.voyage_id = v.id 
    AND r.statut IN ('CONFIRMEE', 'EN_ATTENTE')
);

-- ========================================
-- VERIFICATION DES DONNEES INSEREES
-- ========================================

-- Afficher un résumé des données insérées
SELECT 'Gares' as table_name, COUNT(*) as count FROM gares
UNION ALL
SELECT 'Utilisateurs', COUNT(*) FROM utilisateurs
UNION ALL
SELECT 'Trajets', COUNT(*) FROM trajets
UNION ALL
SELECT 'Voyages', COUNT(*) FROM voyages
UNION ALL
SELECT 'Réservations', COUNT(*) FROM reservations;

COMMIT;
