@echo off
echo ========================================
echo    CONFIGURATION APPLICATION TRAINSYSTEM
echo ========================================
echo.

cd /d "%~dp0\.."

echo 🔧 Configuration automatique de l'application...
echo.

REM Vérifier si le fichier de configuration existe
if exist "src\main\resources\application.properties" (
    echo ✅ Fichier de configuration trouvé
) else (
    echo 📁 Création du dossier de configuration...
    mkdir "src\main\resources" 2>nul
)

echo 📝 Création du fichier de configuration de base de données...

REM Créer le fichier de configuration
echo # Configuration Base de Donnees TrainSystem > "src\main\resources\application.properties"
echo # Genere automatiquement le %date% %time% >> "src\main\resources\application.properties"
echo. >> "src\main\resources\application.properties"
echo # Configuration MySQL >> "src\main\resources\application.properties"
echo db.url=*********************************?useSSL=false^&serverTimezone=UTC^&allowPublicKeyRetrieval=true >> "src\main\resources\application.properties"
echo db.username=root >> "src\main\resources\application.properties"
echo db.password= >> "src\main\resources\application.properties"
echo db.driver=com.mysql.cj.jdbc.Driver >> "src\main\resources\application.properties"
echo. >> "src\main\resources\application.properties"
echo # Configuration Pool de Connexions >> "src\main\resources\application.properties"
echo db.pool.minSize=5 >> "src\main\resources\application.properties"
echo db.pool.maxSize=20 >> "src\main\resources\application.properties"
echo db.pool.timeout=30000 >> "src\main\resources\application.properties"
echo. >> "src\main\resources\application.properties"
echo # Configuration Application >> "src\main\resources\application.properties"
echo app.name=TrainSystem >> "src\main\resources\application.properties"
echo app.version=1.0.0 >> "src\main\resources\application.properties"
echo app.mode=development >> "src\main\resources\application.properties"
echo. >> "src\main\resources\application.properties"
echo # Configuration Session >> "src\main\resources\application.properties"
echo session.timeout=1800 >> "src\main\resources\application.properties"
echo session.secure=false >> "src\main\resources\application.properties"

echo ✅ Fichier de configuration créé

REM Créer un fichier de test de connexion
echo 📝 Création du script de test de connexion...

echo package com.train.util; > "src\main\java\com\train\util\DatabaseTest.java"
echo. >> "src\main\java\com\train\util\DatabaseTest.java"
echo import java.sql.Connection; >> "src\main\java\com\train\util\DatabaseTest.java"
echo import java.sql.DriverManager; >> "src\main\java\com\train\util\DatabaseTest.java"
echo import java.sql.ResultSet; >> "src\main\java\com\train\util\DatabaseTest.java"
echo import java.sql.Statement; >> "src\main\java\com\train\util\DatabaseTest.java"
echo. >> "src\main\java\com\train\util\DatabaseTest.java"
echo public class DatabaseTest { >> "src\main\java\com\train\util\DatabaseTest.java"
echo     public static void main(String[] args) { >> "src\main\java\com\train\util\DatabaseTest.java"
echo         try { >> "src\main\java\com\train\util\DatabaseTest.java"
echo             String url = "*********************************?useSSL=false&serverTimezone=UTC"; >> "src\main\java\com\train\util\DatabaseTest.java"
echo             Connection conn = DriverManager.getConnection(url, "root", ""); >> "src\main\java\com\train\util\DatabaseTest.java"
echo             Statement stmt = conn.createStatement(); >> "src\main\java\com\train\util\DatabaseTest.java"
echo             ResultSet rs = stmt.executeQuery("SELECT COUNT(*) as total FROM gares"); >> "src\main\java\com\train\util\DatabaseTest.java"
echo             if (rs.next()) { >> "src\main\java\com\train\util\DatabaseTest.java"
echo                 System.out.println("✅ Connexion réussie ! " + rs.getInt("total") + " gares trouvées"); >> "src\main\java\com\train\util\DatabaseTest.java"
echo             } >> "src\main\java\com\train\util\DatabaseTest.java"
echo             conn.close(); >> "src\main\java\com\train\util\DatabaseTest.java"
echo         } catch (Exception e) { >> "src\main\java\com\train\util\DatabaseTest.java"
echo             System.out.println("❌ Erreur de connexion: " + e.getMessage()); >> "src\main\java\com\train\util\DatabaseTest.java"
echo         } >> "src\main\java\com\train\util\DatabaseTest.java"
echo     } >> "src\main\java\com\train\util\DatabaseTest.java"
echo } >> "src\main\java\com\train\util\DatabaseTest.java"

echo ✅ Script de test créé

echo 🧪 Test de la connexion à la base de données...
echo.

REM Compiler et tester la connexion
call mvn compile -q
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur de compilation
) else (
    echo ✅ Compilation réussie
    echo.
    echo 🔗 Test de connexion à la base de données...
    call mvn exec:java -Dexec.mainClass="com.train.util.DatabaseTest" -q
)

echo.
echo ========================================
echo    CONFIGURATION TERMINEE
echo ========================================
echo.
echo ✅ Application configurée pour la base 'train'
echo.
echo 📁 Fichiers créés :
echo   • src/main/resources/application.properties
echo   • src/main/java/com/train/util/DatabaseTest.java
echo.
echo 🔧 Configuration de base de données :
echo   • URL : *********************************
echo   • Utilisateur : root
echo   • Mot de passe : (vide)
echo.
echo 🚀 Prochaines étapes :
echo   1. Lancez votre application avec : mvn jetty:run
echo   2. Accédez à : http://localhost:8080/Train
echo   3. Testez avec : <EMAIL> / password
echo.
echo 💡 Si la connexion échoue :
echo   • Vérifiez que MySQL est démarré
echo   • Vérifiez que la base 'train' existe
echo   • Modifiez application.properties si nécessaire
echo.

pause
