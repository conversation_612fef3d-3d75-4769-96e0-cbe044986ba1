@echo off
echo ========================================
echo    TRAINSYSTEM - SERVEUR INTEGRE
echo ========================================
echo.

cd /d "%~dp0"

REM Arrêter les processus Java existants
echo 1. Nettoyage des processus existants...
taskkill /F /IM java.exe 2>nul
timeout /t 2 /nobreak >nul

REM Compiler l'application
echo 2. Compilation de l'application...
call mvn clean compile -q
if %ERRORLEVEL% neq 0 (
    echo ❌ Erreur de compilation
    pause
    exit /b 1
)
echo ✅ Compilation réussie

REM Démarrer avec le serveur intégré Maven
echo 3. Démarrage du serveur intégré...
echo.
echo 🚀 Démarrage en cours...
echo 📍 URL: http://localhost:8080/Train
echo 🔑 Compte test: <EMAIL> / password
echo.
echo ⚠️  Pour arrêter le serveur, appuyez sur Ctrl+C
echo.

REM Utiliser Maven avec Tomcat intégré
start "TrainSystem Server" cmd /k "mvn tomcat7:run -Dmaven.tomcat.port=8080 -Dmaven.tomcat.path=/Train"

REM Attendre que le serveur démarre
echo ⏳ Attente du démarrage (15 secondes)...
timeout /t 15 /nobreak >nul

REM Vérifier si le serveur répond
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8080/Train' -TimeoutSec 5; Write-Host '✅ Serveur démarré avec succès !'; exit 0 } catch { Write-Host '⚠️ Serveur en cours de démarrage...'; exit 1 }"

if %ERRORLEVEL%==0 (
    echo.
    echo 🌐 Ouverture de l'application dans le navigateur...
    start http://localhost:8080/Train
) else (
    echo.
    echo ⏳ Le serveur démarre encore, patientez quelques secondes...
    echo 🌐 Essayez d'accéder manuellement à : http://localhost:8080/Train
)

echo.
echo ========================================
echo    SERVEUR EN COURS D'EXECUTION
echo ========================================
echo.
echo L'application TrainSystem est maintenant disponible !
echo.
echo 📱 Fonctionnalités disponibles :
echo   • Inscription et connexion
echo   • Recherche de voyages (Paris, Lyon, Marseille, etc.)
echo   • Réservation de billets
echo   • Gestion des réservations
echo.
echo 👤 Compte administrateur :
echo   Email    : <EMAIL>
echo   Password : password
echo.
echo 🔧 Mode démonstration activé (données en mémoire)
echo.
echo Pour arrêter l'application, fermez cette fenêtre ou appuyez sur Ctrl+C
echo.

pause
