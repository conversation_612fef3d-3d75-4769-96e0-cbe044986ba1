-- Script pour vérifier les voyages existants dans votre base de données
USE train;

-- 1. Vérifier si la table voyages existe
SHOW TABLES LIKE 'voyages';

-- 2. Vérifier la structure de la table voyages
DESCRIBE voyages;

-- 3. Compt<PERSON> le nombre de voyages existants
SELECT COUNT(*) as nombre_voyages FROM voyages;

-- 4. <PERSON><PERSON><PERSON><PERSON> tous les voyages avec détails des trajets
SELECT 
    v.id as voyage_id,
    v.date_voyage,
    v.places_disponibles,
    v.places_reservees,
    v.statut,
    v.retard_minutes,
    t.id as trajet_id,
    CONCAT(gd.ville, ' → ', ga.ville) as trajet_libelle,
    t.heure_depart,
    t.heure_arrivee,
    t.prix,
    t.type_train,
    gd.nom as gare_depart,
    ga.nom as gare_arrivee
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
ORDER BY v.date_voyage DESC, gd.ville;

-- 5. Statistiques des voyages par statut
SELECT 
    statut,
    COUNT(*) as nombre,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM voyages), 2) as pourcentage
FROM voyages 
GROUP BY statut
ORDER BY nombre DESC;

-- 6. Voyages par date (les 10 prochains)
SELECT 
    v.date_voyage,
    COUNT(*) as nombre_voyages,
    GROUP_CONCAT(CONCAT(gd.ville, '→', ga.ville) SEPARATOR ', ') as trajets
FROM voyages v
JOIN trajets t ON v.trajet_id = t.id
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
WHERE v.date_voyage >= CURDATE()
GROUP BY v.date_voyage
ORDER BY v.date_voyage
LIMIT 10;

-- 7. Vérifier les trajets disponibles pour créer des voyages
SELECT 
    t.id,
    CONCAT(gd.ville, ' → ', ga.ville) as trajet,
    t.heure_depart,
    t.heure_arrivee,
    t.prix,
    t.type_train,
    t.actif,
    COUNT(v.id) as nombre_voyages_programmes
FROM trajets t
JOIN gares gd ON t.gare_depart_id = gd.id
JOIN gares ga ON t.gare_arrivee_id = ga.id
LEFT JOIN voyages v ON t.id = v.trajet_id AND v.statut = 'PROGRAMME'
WHERE t.actif = TRUE
GROUP BY t.id, gd.ville, ga.ville, t.heure_depart, t.heure_arrivee, t.prix, t.type_train, t.actif
ORDER BY gd.ville, t.heure_depart;

-- 8. Si aucun voyage n'existe, créer quelques voyages de test
-- (Décommentez les lignes suivantes si vous voulez créer des données de test)

/*
-- Créer des voyages pour les 7 prochains jours sur tous les trajets actifs
INSERT INTO voyages (trajet_id, date_voyage, places_disponibles, places_reservees, statut)
SELECT 
    t.id,
    CURDATE() + INTERVAL day_offset.offset DAY,
    t.nombre_places,
    FLOOR(RAND() * t.nombre_places * 0.2), -- 0-20% de places réservées
    'PROGRAMME'
FROM trajets t
CROSS JOIN (
    SELECT 1 as offset UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL 
    SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7
) day_offset
WHERE t.actif = TRUE
AND NOT EXISTS (
    SELECT 1 FROM voyages v 
    WHERE v.trajet_id = t.id 
    AND v.date_voyage = CURDATE() + INTERVAL day_offset.offset DAY
)
ORDER BY t.id, day_offset.offset;
*/
